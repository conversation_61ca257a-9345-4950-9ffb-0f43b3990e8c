python_stdnum-1.19.dist-info/COPYING,sha256=XfBwBxmJicYi9dQd6NcD577z0OedYuJDMu5zmkUq9io,26436
python_stdnum-1.19.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_stdnum-1.19.dist-info/METADATA,sha256=D7c5MsD-oWw33r_Bsb2Urbq6D24fxsOxqcCWx2qQC4Q,17933
python_stdnum-1.19.dist-info/RECORD,,
python_stdnum-1.19.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
python_stdnum-1.19.dist-info/WHEEL,sha256=k3vXr0c0OitO0k9eCWBlI2yTYnpb_n_I2SGzrrfY7HY,110
python_stdnum-1.19.dist-info/top_level.txt,sha256=2TbxjF-YBqLmFtkp0c2tNeXjolXDpXEHdX05q9nqOKs,7
stdnum/__init__.py,sha256=MLm-W2rJVSZu8XUuIRlzQ3WaPoXYxk-bF5x34UZ3uGs,1488
stdnum/__pycache__/__init__.cpython-311.pyc,,
stdnum/__pycache__/bic.cpython-311.pyc,,
stdnum/__pycache__/bitcoin.cpython-311.pyc,,
stdnum/__pycache__/casrn.cpython-311.pyc,,
stdnum/__pycache__/cfi.cpython-311.pyc,,
stdnum/__pycache__/cusip.cpython-311.pyc,,
stdnum/__pycache__/damm.cpython-311.pyc,,
stdnum/__pycache__/ean.cpython-311.pyc,,
stdnum/__pycache__/exceptions.cpython-311.pyc,,
stdnum/__pycache__/figi.cpython-311.pyc,,
stdnum/__pycache__/grid.cpython-311.pyc,,
stdnum/__pycache__/gs1_128.cpython-311.pyc,,
stdnum/__pycache__/iban.cpython-311.pyc,,
stdnum/__pycache__/imei.cpython-311.pyc,,
stdnum/__pycache__/imo.cpython-311.pyc,,
stdnum/__pycache__/imsi.cpython-311.pyc,,
stdnum/__pycache__/isan.cpython-311.pyc,,
stdnum/__pycache__/isbn.cpython-311.pyc,,
stdnum/__pycache__/isil.cpython-311.pyc,,
stdnum/__pycache__/isin.cpython-311.pyc,,
stdnum/__pycache__/ismn.cpython-311.pyc,,
stdnum/__pycache__/iso11649.cpython-311.pyc,,
stdnum/__pycache__/iso6346.cpython-311.pyc,,
stdnum/__pycache__/iso9362.cpython-311.pyc,,
stdnum/__pycache__/isrc.cpython-311.pyc,,
stdnum/__pycache__/issn.cpython-311.pyc,,
stdnum/__pycache__/lei.cpython-311.pyc,,
stdnum/__pycache__/luhn.cpython-311.pyc,,
stdnum/__pycache__/mac.cpython-311.pyc,,
stdnum/__pycache__/meid.cpython-311.pyc,,
stdnum/__pycache__/numdb.cpython-311.pyc,,
stdnum/__pycache__/util.cpython-311.pyc,,
stdnum/__pycache__/vatin.cpython-311.pyc,,
stdnum/__pycache__/verhoeff.cpython-311.pyc,,
stdnum/ad/__init__.py,sha256=Bu192nVwQFG9sFvmkfm8rREO8eqYxPo9cKZPhe2EMJI,939
stdnum/ad/__pycache__/__init__.cpython-311.pyc,,
stdnum/ad/__pycache__/nrt.cpython-311.pyc,,
stdnum/ad/nrt.py,sha256=JQ9R3Xb5lbmvN6npSn6wfqDomGmvJ0mnSG8ljMakCSw,2901
stdnum/al/__init__.py,sha256=_vDWFZyh33t52eajOJeZhG3K4EfD2s439roZQnpglIo,945
stdnum/al/__pycache__/__init__.cpython-311.pyc,,
stdnum/al/__pycache__/nipt.cpython-311.pyc,,
stdnum/al/nipt.py,sha256=O_iNTXI7OuIsw13E8zbFbTx3Lq_RHr-NuZjreNK2_eY,2868
stdnum/ar/__init__.py,sha256=uUj0unOAf0Bvax6iWq7NmnrZvyO62GQMJWQYsWx4X4s,997
stdnum/ar/__pycache__/__init__.cpython-311.pyc,,
stdnum/ar/__pycache__/cbu.cpython-311.pyc,,
stdnum/ar/__pycache__/cuit.cpython-311.pyc,,
stdnum/ar/__pycache__/dni.cpython-311.pyc,,
stdnum/ar/cbu.py,sha256=pShOpFonFrxxW5u7LK3WBoC08GHHmMmgfI37JtOqOzg,2717
stdnum/ar/cuit.py,sha256=IrpOAJ6GT5paN9-HgVM89EJBowAQUxgJbunZ0VNWW_Y,2909
stdnum/ar/dni.py,sha256=P5jKbWopI1YguvlZa1wryvAY5g1Wr0y_mHW1FIrCn2M,2223
stdnum/at/__init__.py,sha256=pjclBQou0BzpcEPgdFYmHMTpnwAPrC8LKhSTOs2aYCo,1059
stdnum/at/__pycache__/__init__.cpython-311.pyc,,
stdnum/at/__pycache__/businessid.cpython-311.pyc,,
stdnum/at/__pycache__/postleitzahl.cpython-311.pyc,,
stdnum/at/__pycache__/tin.cpython-311.pyc,,
stdnum/at/__pycache__/uid.cpython-311.pyc,,
stdnum/at/__pycache__/vnr.cpython-311.pyc,,
stdnum/at/businessid.py,sha256=99dPXAZzRwMFafM4skQLWO4gE3Fu4y5_9WN8gSxEYO4,2232
stdnum/at/fa.dat,sha256=9sny5KVmrtZKx5oBQGUiouD5rmjyhNskDbjtr6Jo9SA,2184
stdnum/at/postleitzahl.dat,sha256=2QVXGFBTfuzd5AWQGP03jRTFd-w5Bnf_cWSan9uaUMg,130513
stdnum/at/postleitzahl.py,sha256=4hJ9-u_2-V_oKPW-R9EyqnUgYJFEEdTwx2G3n5g5Ids,2461
stdnum/at/tin.py,sha256=DzwzfTyDYjqSbwhwGUb3qDO0pnYL87AMdg1sPkArsPw,3714
stdnum/at/uid.py,sha256=qH-wIi6k8LhovTMWlOhRWpQb798-XVoOiTvYFR57RLw,2375
stdnum/at/vnr.py,sha256=RO57GiXU5wnC2xD6gGxgH-Nd0tXiyhrZNCW_V63BZ0A,2462
stdnum/au/__init__.py,sha256=F8ehSwZOeiflhemldFfV9kfQPZG7dD5yxGGPliu9FQc,940
stdnum/au/__pycache__/__init__.cpython-311.pyc,,
stdnum/au/__pycache__/abn.cpython-311.pyc,,
stdnum/au/__pycache__/acn.cpython-311.pyc,,
stdnum/au/__pycache__/tfn.cpython-311.pyc,,
stdnum/au/abn.py,sha256=-QgaSUBsjI1iPftNVCrTo-WwLx6y--s4vfdg6jU1aOU,2685
stdnum/au/acn.py,sha256=2H40FX78TVA-KPNnk414w3K2OkV4lQ-y40yjCenVOKU,2699
stdnum/au/tfn.py,sha256=SXp34RSM5mGQEL8-n3NpT24-_ZIE1-XD-hJkAcYprR0,2659
stdnum/be/__init__.py,sha256=ZnB4c-TuVSLtLGxU1nedi0XIb2LtlDKUhv4jqbkfmhM,1014
stdnum/be/__pycache__/__init__.cpython-311.pyc,,
stdnum/be/__pycache__/bis.cpython-311.pyc,,
stdnum/be/__pycache__/iban.cpython-311.pyc,,
stdnum/be/__pycache__/nn.cpython-311.pyc,,
stdnum/be/__pycache__/vat.cpython-311.pyc,,
stdnum/be/banks.dat,sha256=2S4Zqfl5qtN2UocO0H5ljtw8m61dr3rKr7kZbJwGvbA,9930
stdnum/be/bis.py,sha256=cICRNVw9RepqNGCNpb6DPrqLo51FOA1mP6L0y8ruemw,4092
stdnum/be/iban.py,sha256=X4QZBAOWZo9K6xisPey675o7QAC10YyVCQIjfk_Mi7c,3059
stdnum/be/nn.py,sha256=MpFE9ShMCj1Iml3gS0mfERrpKNILSKJRkk8cnvN2rEI,6568
stdnum/be/vat.py,sha256=5T0VzR0Nl9DWIVgGk5NJ2ucsfQ3goOZapy_8ysFfcS8,2493
stdnum/bg/__init__.py,sha256=bxXttweQIC5Ihb3-ff8-CqDn4R5CqGT3Fv2sE2FoP6A,872
stdnum/bg/__pycache__/__init__.cpython-311.pyc,,
stdnum/bg/__pycache__/egn.cpython-311.pyc,,
stdnum/bg/__pycache__/pnf.cpython-311.pyc,,
stdnum/bg/__pycache__/vat.cpython-311.pyc,,
stdnum/bg/egn.py,sha256=ieh53MilOafhhXcE-iOPPTkuv_j2PoKkJ8d-8hkLgZw,3318
stdnum/bg/pnf.py,sha256=xnjezx2iQjT3-UVA4lHhpqGUB7rgLzVyAF1skYAIVPw,2491
stdnum/bg/vat.py,sha256=lDM9TqFSydmti6kMyQrAe8TfQ_m5aIH79LHbeWdJX8w,3292
stdnum/bic.py,sha256=Oepb3J25-4LJjR5rSAau57KrBw7P-g33GTdydfT8A58,2575
stdnum/bitcoin.py,sha256=WSPH-mgFGhK_gOGnqZ7L7urDx-fa3dbJguAnas2zrNo,5251
stdnum/br/__init__.py,sha256=tGuMMfGwTUcXVGnIh04Tc4toxKNUaUZTJfuKeISd_P0,920
stdnum/br/__pycache__/__init__.cpython-311.pyc,,
stdnum/br/__pycache__/cnpj.cpython-311.pyc,,
stdnum/br/__pycache__/cpf.cpython-311.pyc,,
stdnum/br/cnpj.py,sha256=XXUpQqzrjdRXtncOnQaHxLhmhj2SpZoHa1bwfcBM_J4,2883
stdnum/br/cpf.py,sha256=yCgEG9u8jhqKma0uxDfiVt-b0LsOwBpkxL4Fbvnk25I,2817
stdnum/by/__init__.py,sha256=vLWPw6-G9LUd-CHLcYvrVYsbFmVeBDEfor7pVGTvQgk,940
stdnum/by/__pycache__/__init__.cpython-311.pyc,,
stdnum/by/__pycache__/unp.cpython-311.pyc,,
stdnum/by/unp.py,sha256=f3E-MRU1oCXklG4cJtLTBuN-ep3E9lXn810DML8JnAE,4582
stdnum/ca/__init__.py,sha256=uTE6R16ToN-O_klZEeRg_FFPHkhXV6P-jYy9kJ76qxc,916
stdnum/ca/__pycache__/__init__.cpython-311.pyc,,
stdnum/ca/__pycache__/bn.cpython-311.pyc,,
stdnum/ca/__pycache__/sin.cpython-311.pyc,,
stdnum/ca/bn.py,sha256=A_gbV_xYiDXGZuiOl5cjZ6OWJEYGDyL_-ZXi5HgDlaE,2587
stdnum/ca/sin.py,sha256=jBa4QovzW86YvN_DavN8jBBr1F9_-JkJ0iFSNw7kzKE,2633
stdnum/casrn.py,sha256=ZkcfNQqatvNTR6TBw7Uwo_jFdGJBge-zffFjvBy15wk,2465
stdnum/cfi.dat,sha256=SI9VB2aH95c6QsZn1bTiVeBtluQXWUfspmhXC03Gfw0,36681
stdnum/cfi.py,sha256=6LSk3DMKAZpc3Bv-4vhLFQZo_GJ7RDSw51bgWUNuYQE,3106
stdnum/ch/__init__.py,sha256=ajzUrF-bUK6t_nzy-pa3bVpiRwsXfzF3Zy5gjBqOwV4,866
stdnum/ch/__pycache__/__init__.cpython-311.pyc,,
stdnum/ch/__pycache__/esr.cpython-311.pyc,,
stdnum/ch/__pycache__/ssn.cpython-311.pyc,,
stdnum/ch/__pycache__/uid.cpython-311.pyc,,
stdnum/ch/__pycache__/vat.cpython-311.pyc,,
stdnum/ch/esr.py,sha256=vMNW_Lw_SQZsHXeiB2fbkLH1UKnlSK4v49qYiRxo7Q4,3377
stdnum/ch/ssn.py,sha256=dgUBqG1gNgk22MhkZmFK1eoom_WFvVqhy-tJ2U71soQ,2630
stdnum/ch/uid.py,sha256=5OtlE3CWZwYlWTAxXF5mkrkbSqKaaEd7W3L2GJWAVXs,5287
stdnum/ch/vat.py,sha256=eW8ieOlDqpDkzwfXf-yyD8N2j9QfM7LGTu3Yz9RdDXA,2591
stdnum/cl/__init__.py,sha256=yAHEvSMBuFpV45Amn_BOIl8b2eZEJvc03Y9OZAlxruM,1021
stdnum/cl/__pycache__/__init__.cpython-311.pyc,,
stdnum/cl/__pycache__/rut.cpython-311.pyc,,
stdnum/cl/rut.py,sha256=7HV7n5vwOxSIB0yZHgmLpxh4OnDxPrBNfSYqoTpxbZo,2851
stdnum/cn/__init__.py,sha256=ZPsxhjest2KF-sZad4QygLvodHnE-rLTAbxH_fJtxgE,953
stdnum/cn/__pycache__/__init__.cpython-311.pyc,,
stdnum/cn/__pycache__/ric.cpython-311.pyc,,
stdnum/cn/__pycache__/uscc.cpython-311.pyc,,
stdnum/cn/loc.dat,sha256=8uHrwipk7sXX9a-suu-5E-CROnv2JJtse7TilM4ZeP4,251803
stdnum/cn/ric.py,sha256=lt39hH19Zd1Oy3ay6zwsRslTYYIi9V8CgEgXiZk-iz4,3452
stdnum/cn/uscc.py,sha256=ipbTdei3kG75Z82W22EzDBX253c6gguT3F8FxzGcreo,4195
stdnum/co/__init__.py,sha256=QnWeFdCr7ccaiuURuQdDbf5PKt8BjKfyJ8td2Fs4HRE,1025
stdnum/co/__pycache__/__init__.cpython-311.pyc,,
stdnum/co/__pycache__/nit.cpython-311.pyc,,
stdnum/co/nit.py,sha256=fXCwPwO-0eMLhj5BHqESAYlEduEXK5zI1Kj81xIcMpo,2625
stdnum/cr/__init__.py,sha256=SVxldmbbGDut7yIJNipLLEo9jBhlpvgO8EcwPm8fhpQ,925
stdnum/cr/__pycache__/__init__.cpython-311.pyc,,
stdnum/cr/__pycache__/cpf.cpython-311.pyc,,
stdnum/cr/__pycache__/cpj.cpython-311.pyc,,
stdnum/cr/__pycache__/cr.cpython-311.pyc,,
stdnum/cr/cpf.py,sha256=05wIdljhc3TCNnLbV5N3pQyoju4z6zmbkGoHxFP5Zpg,3262
stdnum/cr/cpj.py,sha256=7OsCzXm6cNztWfSIqjZYrNUqjZVFMCrwvwoHQs_w6GY,3583
stdnum/cr/cr.py,sha256=nOr2atn-5Fx3_UhlEJ1oVv5cirjdjXloNc_BmQtuFCw,2951
stdnum/cu/__init__.py,sha256=gpdPhdsiJnAW2TFPifT0Rzt-MrLe1_W_MyEUTsxfBho,864
stdnum/cu/__pycache__/__init__.cpython-311.pyc,,
stdnum/cu/__pycache__/ni.cpython-311.pyc,,
stdnum/cu/ni.py,sha256=Uozmq5pCWMjZ5toWxhMcIak-qiqkFk08fWu02Q37PTY,3063
stdnum/cusip.py,sha256=6MOrUZONI2rnDD8eZBTHMRsV09ypSOwXW_OjcG1shIg,2782
stdnum/cy/__init__.py,sha256=aaNIXKyro60dmmIN8Ml7LHKMVNWNGz6eZ9TFI3TwwxI,868
stdnum/cy/__pycache__/__init__.cpython-311.pyc,,
stdnum/cy/__pycache__/vat.cpython-311.pyc,,
stdnum/cy/vat.py,sha256=i1OyqlgmzgCARUb_5GjkMGFVY4A6_LjM0bD700HGanU,2666
stdnum/cz/__init__.py,sha256=l3V1EA1JSe3Qs8wBfRvJl4EZY__9_RmlJC4YcDzOWNo,938
stdnum/cz/__pycache__/__init__.cpython-311.pyc,,
stdnum/cz/__pycache__/bankaccount.cpython-311.pyc,,
stdnum/cz/__pycache__/dic.cpython-311.pyc,,
stdnum/cz/__pycache__/rc.cpython-311.pyc,,
stdnum/cz/bankaccount.py,sha256=GdnbNHGwzDbluS188-4PheYUO9Eg6blkAJDSCtUSm5w,4136
stdnum/cz/banks.dat,sha256=DE3f86pDpGHzhJqioJk6gckHmGtauC8gKEcD3ajvJPI,4033
stdnum/cz/dic.py,sha256=zjR-lS1cdJyw_unHfa1QN1gYCkmisH9WRNW-7HizzZI,3472
stdnum/cz/rc.py,sha256=Q3hklNA0vqFexLHday7aiRcO_Vu09Nc7Jk6ckeUHhwI,3786
stdnum/damm.py,sha256=hOvYa1E0wy0fFZphcaJWvhV-6spGCQ3gozAAkyGfb-k,3297
stdnum/de/__init__.py,sha256=uomtJChdAIFwsc1viIWo6bnSqNHquFseUJrRiYuGM2o,972
stdnum/de/__pycache__/__init__.cpython-311.pyc,,
stdnum/de/__pycache__/handelsregisternummer.cpython-311.pyc,,
stdnum/de/__pycache__/idnr.cpython-311.pyc,,
stdnum/de/__pycache__/stnr.cpython-311.pyc,,
stdnum/de/__pycache__/vat.cpython-311.pyc,,
stdnum/de/__pycache__/wkn.cpython-311.pyc,,
stdnum/de/handelsregisternummer.py,sha256=HJ91FvaF7QfWevg33CcCWssnNkmHUhBqW4DKsTp9tIc,10242
stdnum/de/idnr.py,sha256=RgHzHbu-zA34aNECtB0wdSPQOpfUqhTlIzAb3mFSe40,3167
stdnum/de/stnr.py,sha256=ymlJWiSqqZ_XVaKgxZnz-oGb6y4qlYs3Uhcsobq3KfY,6889
stdnum/de/vat.py,sha256=RiCz6iaAtOTInqQF6bLeWvQPNhn7RUuDFnEi9piT-jM,2162
stdnum/de/wkn.py,sha256=Y5akJDS_6BMomd0m4OCOdGi7gtBRSceI_eoo8i_MwCU,2359
stdnum/dk/__init__.py,sha256=MSWZspc8caPQAJdmqSeYLpjPsdFNwWKXs4M9TFmgGK0,986
stdnum/dk/__pycache__/__init__.cpython-311.pyc,,
stdnum/dk/__pycache__/cpr.cpython-311.pyc,,
stdnum/dk/__pycache__/cvr.cpython-311.pyc,,
stdnum/dk/cpr.py,sha256=xpkhiD5exuLvT1yjgxN7P58f_xRDrpqP8sgTTj_lHL4,4184
stdnum/dk/cvr.py,sha256=hrWKlWLM0DcR_t2Dt74BL9C25qvpnuflM6jJV9AQCbk,2276
stdnum/do/__init__.py,sha256=39xWgAHowCtf5S5_dTd5FdnL3guM8Si52R2Wd9iCSJw,938
stdnum/do/__pycache__/__init__.cpython-311.pyc,,
stdnum/do/__pycache__/cedula.cpython-311.pyc,,
stdnum/do/__pycache__/ncf.cpython-311.pyc,,
stdnum/do/__pycache__/rnc.cpython-311.pyc,,
stdnum/do/cedula.py,sha256=yarr6ujtHtAUUoFTnpRWNZKl2FHe8oocBxS7nicEHQY,10556
stdnum/do/ncf.py,sha256=bbG65v3shWpAVbaVumMWKE675BJRk7wBuErqiW0mtr0,8671
stdnum/do/rnc.py,sha256=sLKIajXCRwTaTXcEYX6VTHAdaxtNYyxorw_hY9OZt-0,6947
stdnum/dz/__init__.py,sha256=zsNjIxFSm1DuJXacm2UVlrKG-wGsjMXow9sDYmP60TA,946
stdnum/dz/__pycache__/__init__.cpython-311.pyc,,
stdnum/dz/__pycache__/nif.cpython-311.pyc,,
stdnum/dz/nif.py,sha256=4rTHYAHQ62TX570956KgQIOKF2uy-Lvp1FQfVgXrYMU,3033
stdnum/ean.py,sha256=P4Qb6vrHk75icN4lMfpYdbQDDNADBmnPLmEOfssp0qA,2515
stdnum/ec/__init__.py,sha256=-WNy7pVeLYWi2ByNfDFJZ1-7o0AexxXrvSc5i3cY-uU,949
stdnum/ec/__pycache__/__init__.cpython-311.pyc,,
stdnum/ec/__pycache__/ci.cpython-311.pyc,,
stdnum/ec/__pycache__/ruc.cpython-311.pyc,,
stdnum/ec/ci.py,sha256=FXpT20z0qHRR0aDljM_d8O1d29H8WgBFT8Zi35mIw3s,2618
stdnum/ec/ruc.py,sha256=iNUH0N-f9m37ywVmRHiQzrwoLU6FFa6Je3i2CL4SKMo,3741
stdnum/ee/__init__.py,sha256=ASI96NujTWhKGOH9rM1dZN0LLa_T_WQ1DY9UH8EtFo8,945
stdnum/ee/__pycache__/__init__.cpython-311.pyc,,
stdnum/ee/__pycache__/ik.cpython-311.pyc,,
stdnum/ee/__pycache__/kmkr.cpython-311.pyc,,
stdnum/ee/__pycache__/registrikood.cpython-311.pyc,,
stdnum/ee/ik.py,sha256=6UqQiiQWmVIVVcRWpOWLnumQe25p6opgpqn_lQviIaE,3516
stdnum/ee/kmkr.py,sha256=U-gch7xx69eRjhEXYt4khkgLt4Nvdcc83fAxNdeLjn8,2236
stdnum/ee/registrikood.py,sha256=sc3szj2hSVbGJdBL7gh0xoMh8SkzPMgIc6ZHqqnArns,2665
stdnum/eg/__init__.py,sha256=50hVcvtwOFbq1G0ZfOzDve1oDo42kx6ECB4IyWsfgXg,931
stdnum/eg/__pycache__/__init__.cpython-311.pyc,,
stdnum/eg/__pycache__/tn.cpython-311.pyc,,
stdnum/eg/tn.py,sha256=e2-rQIOgTwhLu2TKiEAh0K76RIcW0ZA8wG5dNv-GpKY,3072
stdnum/es/__init__.py,sha256=L4TS2ESrXeGyIcEOLYChsPyco5bSYl14WTbCivrqaiU,942
stdnum/es/__pycache__/__init__.cpython-311.pyc,,
stdnum/es/__pycache__/ccc.cpython-311.pyc,,
stdnum/es/__pycache__/cif.cpython-311.pyc,,
stdnum/es/__pycache__/cups.cpython-311.pyc,,
stdnum/es/__pycache__/dni.cpython-311.pyc,,
stdnum/es/__pycache__/iban.cpython-311.pyc,,
stdnum/es/__pycache__/nie.cpython-311.pyc,,
stdnum/es/__pycache__/nif.cpython-311.pyc,,
stdnum/es/__pycache__/postal_code.cpython-311.pyc,,
stdnum/es/__pycache__/referenciacatastral.cpython-311.pyc,,
stdnum/es/ccc.py,sha256=nG-CT59s6Z5VlhN0Jw_E4k1sKCWW9FTdU3-iPDbSJVc,4080
stdnum/es/cif.py,sha256=hgGoxurJxi-244i2wBuP_32Z666xE9DDDKGqtoFyEkU,3569
stdnum/es/cups.py,sha256=ixV7PLU_WbJHyrmh2Zw9u6E_n0mAjUvbxLVWZzlW7II,3655
stdnum/es/dni.py,sha256=6KVk2nTudUeaNn2LHii-I8d7-bqWLnWEM2RQXEal7lU,2533
stdnum/es/iban.py,sha256=rO35Fu8b-HD6WZYKSE0LVXqz1Nj8sVXG9i4ZlERV9XE,2494
stdnum/es/nie.py,sha256=i8biwXU-JphlFNra0rTsCOWu386Ig5-O0ZEWnfG8RjU,2563
stdnum/es/nif.py,sha256=NXx0bx2M10ViPX9etlKm6w4IFEo55Di05uM6cAzIYgk,3174
stdnum/es/postal_code.py,sha256=mTnxTFRj7fSC74E57jqA1tKLrQLdOXW3hsF0QHjEhKQ,2369
stdnum/es/referenciacatastral.py,sha256=zuOPt4GwkqVTieTJ7AtrIBp0TV9ge0FwHureorId4rc,4098
stdnum/eu/__init__.py,sha256=Eyl7TknedZQYKk4gpxdMpZU8zG2Oz3O0WKBhB6K7I9s,882
stdnum/eu/__pycache__/__init__.cpython-311.pyc,,
stdnum/eu/__pycache__/at_02.cpython-311.pyc,,
stdnum/eu/__pycache__/banknote.cpython-311.pyc,,
stdnum/eu/__pycache__/eic.cpython-311.pyc,,
stdnum/eu/__pycache__/nace.cpython-311.pyc,,
stdnum/eu/__pycache__/oss.cpython-311.pyc,,
stdnum/eu/__pycache__/vat.cpython-311.pyc,,
stdnum/eu/at_02.py,sha256=I0lyfftICk-jH_TY_BwqKxr2ladl-k-sANU_si47Ggs,3154
stdnum/eu/banknote.py,sha256=Wm4N_LOJ87Jd40fX9GqZmo9nDwGOsRsVpO4Wlq1DUUk,2298
stdnum/eu/eic.py,sha256=EX9D1bu9xqLft7ZhqaV4B9CgvLJjyRKFWaLRUBLISTQ,2839
stdnum/eu/nace.dat,sha256=3sJwInkC6T3sKxQspzTmAH0V3wyq2EVNt5nwXoSg2i0,67035
stdnum/eu/nace.py,sha256=f9F9DPKH1J0zHtpH9CahCy_4oDJzRqgQpGTUb4fM6pI,3839
stdnum/eu/oss.py,sha256=K0kgEfXQQ3ltnaCKdwoEqxIp7a-MOCFJdbeLActe2DA,3875
stdnum/eu/vat.py,sha256=-r35bGbvbJTsvGfBsPxJqAUjuyqYHvUayZPYNvwH7Ag,5577
stdnum/exceptions.py,sha256=AVKip-3XpA6jb9pl0-GPkxBBKGbSKTB2RWyjc6f-_wI,2425
stdnum/fi/__init__.py,sha256=L-prPUwrstJA0j6Olbkb1-IA5MI8JKbm7JCdV2dKpgM,1055
stdnum/fi/__pycache__/__init__.cpython-311.pyc,,
stdnum/fi/__pycache__/alv.cpython-311.pyc,,
stdnum/fi/__pycache__/associationid.cpython-311.pyc,,
stdnum/fi/__pycache__/hetu.cpython-311.pyc,,
stdnum/fi/__pycache__/veronumero.cpython-311.pyc,,
stdnum/fi/__pycache__/ytunnus.cpython-311.pyc,,
stdnum/fi/alv.py,sha256=Q5zdZE9BGY-Oj7IXBPttNsOClRRmDYUS5hHRkS3M_sA,2177
stdnum/fi/associationid.py,sha256=WIquqsnceZXFNi6MRUfvRURX-9kAKDvclpWwEfriHjI,2808
stdnum/fi/hetu.py,sha256=YeC7DIh1eeJQA_mbMQOrN6nz8wm2KdgZJYWvgLpYjq0,3988
stdnum/fi/veronumero.py,sha256=UynvrYt8_Lu3E67RRMhvh08q60a-gzDL9agdFcTlSz0,2372
stdnum/fi/ytunnus.py,sha256=n_1mduPEO488cBBbCC0-42uuNIXnSwzCvJn9J3WgT7o,1976
stdnum/figi.py,sha256=dq90uRQp6FfN4hN663SvTm6mxcEraO7RjmQD3yU-rwU,2891
stdnum/fo/__init__.py,sha256=Xnnw0HNypBsrmEFjEMUft6DcbyBLE8Z9LxzzlBdqSTI,947
stdnum/fo/__pycache__/__init__.cpython-311.pyc,,
stdnum/fo/__pycache__/vn.cpython-311.pyc,,
stdnum/fo/vn.py,sha256=tOBKx7w5W2Im3ZNIpF8isgd_M4OxgXokngj-pm0qGKY,2484
stdnum/fr/__init__.py,sha256=jnlnricbXmV4IaDY2jEjCzV9UUOfEQ7pR8nstwW3_ns,940
stdnum/fr/__pycache__/__init__.cpython-311.pyc,,
stdnum/fr/__pycache__/nif.cpython-311.pyc,,
stdnum/fr/__pycache__/nir.cpython-311.pyc,,
stdnum/fr/__pycache__/siren.cpython-311.pyc,,
stdnum/fr/__pycache__/siret.cpython-311.pyc,,
stdnum/fr/__pycache__/tva.cpython-311.pyc,,
stdnum/fr/nif.py,sha256=VKOdiPCy6pJw-DH3pURgzSaHnhGUm3TcJ-0KUWBztyM,2664
stdnum/fr/nir.py,sha256=6ubRLUCgMmY3XdgmG9l5sS9wFdvMjUk5CpEtadk3AQQ,3866
stdnum/fr/siren.py,sha256=CzEu10u-mQNpMtn2acHBfvsl5JegPc6PEAXUKBMthTY,2665
stdnum/fr/siret.py,sha256=46HV-BfcMc7W2aTCTttSmZstxBR06voeytmuaUMKgvk,3566
stdnum/fr/tva.py,sha256=mPay7XivwIdIniRawwRorF6EJ656szDLsOe7ULe4Lag,3438
stdnum/gb/__init__.py,sha256=ZJ6CGiJ8FWTvgjeO-ZySQfdQvJ6IDI2x-NIeR8ZbjB0,882
stdnum/gb/__pycache__/__init__.cpython-311.pyc,,
stdnum/gb/__pycache__/nhs.cpython-311.pyc,,
stdnum/gb/__pycache__/sedol.cpython-311.pyc,,
stdnum/gb/__pycache__/upn.cpython-311.pyc,,
stdnum/gb/__pycache__/utr.cpython-311.pyc,,
stdnum/gb/__pycache__/vat.cpython-311.pyc,,
stdnum/gb/nhs.py,sha256=iznHh5GDKOkMQ5n2bDHl_nT_6bjqWV1KtmXlwehBrGM,2844
stdnum/gb/sedol.py,sha256=yk5j-dxuFmSXMvkV4S8c1lC_AdoqGEQk2ndwDZWjP2s,2751
stdnum/gb/upn.py,sha256=EuNbC2t8D0p20gsJkdVgVTTWoisskfFu53NqwpSp_BY,3975
stdnum/gb/utr.py,sha256=J30C7WYY8TfKvZ659F7bPF316j9rVj9bpW6H0Ypj1VM,2375
stdnum/gb/vat.py,sha256=6sXwYWkRGWneU4f2Nf1l1OgkR25hEjpVPu__KMMFz78,4282
stdnum/gh/__init__.py,sha256=9WKBsyLCyeI0jvtcZVVQgYscKQgHgQtd_LjWoxDCb7Y,932
stdnum/gh/__pycache__/__init__.cpython-311.pyc,,
stdnum/gh/__pycache__/tin.cpython-311.pyc,,
stdnum/gh/tin.py,sha256=ajBcZcAbQ9pkJA_68LxQwMBzUIbUl-Xm392HEYsZ-us,2924
stdnum/gn/__init__.py,sha256=Fxg8M-IvyFi9cQJ9RO4OVL_IG_e3qQjERt0WCmti9eY,935
stdnum/gn/__pycache__/__init__.cpython-311.pyc,,
stdnum/gn/__pycache__/nifp.cpython-311.pyc,,
stdnum/gn/nifp.py,sha256=5DiVzfRiNXNdba9IyqCqYwr28ylciv0dG41gUyzFvvQ,2536
stdnum/gr/__init__.py,sha256=N-2f6pGt7Q0weSyb6hzGWwgqUUjYZfPxnCO5sDJneu4,864
stdnum/gr/__pycache__/__init__.cpython-311.pyc,,
stdnum/gr/__pycache__/amka.cpython-311.pyc,,
stdnum/gr/__pycache__/vat.cpython-311.pyc,,
stdnum/gr/amka.py,sha256=FTKVNOvG6qjv3DOx8SLNv-wOHbsgYjE8J2QIQ6RvST4,3080
stdnum/gr/vat.py,sha256=Rst3_wMM5tu2ti4OHwM8rhxbTdbOkkECQS_JmAuFT4c,2460
stdnum/grid.py,sha256=jPCLqao9VVBjyVsf8XQRx4x_eSEkQ9CsXfTRjEnO_cc,2452
stdnum/gs1_128.py,sha256=y2PxI0SIyPXuo8nYATOTtGWGRKJLh15A9jgVMbmeJhY,10874
stdnum/gs1_ai.dat,sha256=D_yRuxhloBTw1J1Ip7Le38WkLTjupoEJ_xxFkqL_EO4,22088
stdnum/gt/__init__.py,sha256=ZNUyWbeF_3hrB1PW33lZ9r2zTFFuGs_CP3zjdQ7A4hs,942
stdnum/gt/__pycache__/__init__.cpython-311.pyc,,
stdnum/gt/__pycache__/nit.cpython-311.pyc,,
stdnum/gt/nit.py,sha256=heaB2Z3xE7TD_9s4LffdXeRkwmO93QFJ8hcib5G9s4o,3134
stdnum/hr/__init__.py,sha256=b0HQ5hhAWPlP9ng4cJ67lYXuztw03ReqeVEzJCB2rho,944
stdnum/hr/__pycache__/__init__.cpython-311.pyc,,
stdnum/hr/__pycache__/oib.cpython-311.pyc,,
stdnum/hr/oib.py,sha256=MpYPScxaPZHN9BzmLfjEdTza7zTpNyb6om_1gt_zbSA,2214
stdnum/hu/__init__.py,sha256=coHELcZg1hnDz40kit4ZtVs4eVJX69LgHYZ4_hWwdt8,947
stdnum/hu/__pycache__/__init__.cpython-311.pyc,,
stdnum/hu/__pycache__/anum.cpython-311.pyc,,
stdnum/hu/anum.py,sha256=be0qeP9uOV5i6gnX3QC2xUnrLzqKkU2nk72ejDThrtA,2320
stdnum/iban.dat,sha256=jmkrLTHwj5vTfBWnmwEu7xGkyWf2ykpcC52R-imIEqM,3591
stdnum/iban.py,sha256=RthGgzrwIhOvtmSIAbYRpu5R7Ix_ewqDaIFQWKy4arE,4527
stdnum/id/__init__.py,sha256=kBOFHkzaXvtNAx6Qon0zPzT_y2Das01b-_kVgWaisGI,943
stdnum/id/__pycache__/__init__.cpython-311.pyc,,
stdnum/id/__pycache__/npwp.cpython-311.pyc,,
stdnum/id/npwp.py,sha256=NgTWDg8g3WQ6oBVmq5hdwgegLqcDhik5EJxNPsu_q0g,2831
stdnum/ie/__init__.py,sha256=qRZkaTMOl9PxzcpdegS7QA9osO_J1Hs-NTMz1VTcEPs,864
stdnum/ie/__pycache__/__init__.cpython-311.pyc,,
stdnum/ie/__pycache__/pps.cpython-311.pyc,,
stdnum/ie/__pycache__/vat.cpython-311.pyc,,
stdnum/ie/pps.py,sha256=3esPFa1xZBDpfM_JExHdCCHB8RZ-wJX539lmnx1F3Ms,3174
stdnum/ie/vat.py,sha256=vKX3eKz_7ouOXwb9pTYM-kjRK6yuZCxW_l8eP1VRc0U,3711
stdnum/il/__init__.py,sha256=1FubbxRtyNF0HyMLkS6XcUzhIyMDcONKdCvjweoeKeg,933
stdnum/il/__pycache__/__init__.cpython-311.pyc,,
stdnum/il/__pycache__/hp.cpython-311.pyc,,
stdnum/il/__pycache__/idnr.cpython-311.pyc,,
stdnum/il/hp.py,sha256=qkBWZ4MYKOETmtM1oE98ckWGn-jLPT_jnPErBUn7hcA,2800
stdnum/il/idnr.py,sha256=OMfRWvoaWagFDEd-EXdM3oMGl17ugiPLjwGVHCfmTPU,2645
stdnum/imei.py,sha256=ZsossgIvT2zHP8JGq2EXPct1SymUkSh5pX55Nxc4S-Q,3588
stdnum/imo.py,sha256=ncDcGgOxhLqz1Buo_Q--XhFUIqb5TmNKH4BUpicCDCc,2724
stdnum/imsi.dat,sha256=T3KxXua3ZOtiuxGw-7oCC7Ciwla1yVDhmRaqiuGbe08,367668
stdnum/imsi.py,sha256=37upSYZC9XAwR1p8lg4ywI2v4XaNQUPge7NCLsYH_BI,3013
stdnum/in_/__init__.py,sha256=ajBfsenh9BMDWOBYL5hkeOSJyQwsOfyUiBlTK9l2t3A,940
stdnum/in_/__pycache__/__init__.cpython-311.pyc,,
stdnum/in_/__pycache__/aadhaar.cpython-311.pyc,,
stdnum/in_/__pycache__/epic.cpython-311.pyc,,
stdnum/in_/__pycache__/gstin.cpython-311.pyc,,
stdnum/in_/__pycache__/pan.cpython-311.pyc,,
stdnum/in_/aadhaar.py,sha256=PX5pmghfkQBg1EP9rRhRPw3scouNAi5YxD4JuaSyeuQ,3592
stdnum/in_/epic.py,sha256=qSkJks7kns7lPtiG-J86hWtgurRBcac4S39Ll7troIo,2822
stdnum/in_/gstin.py,sha256=nIx6Se-O1G8Pk6dml96z6E_DuuLI41g7Htfi8Bwg8Tk,4820
stdnum/in_/pan.py,sha256=rUY8waAgG7LrLIYEaAol-hcU3yrZWHwgaNCGLuK7LXI,4253
stdnum/is_/__init__.py,sha256=RSuf5S43lHN0D9BsNQfU_c7TuwFqZBkM0PkpsIRPVhw,1001
stdnum/is_/__pycache__/__init__.cpython-311.pyc,,
stdnum/is_/__pycache__/kennitala.cpython-311.pyc,,
stdnum/is_/__pycache__/vsk.cpython-311.pyc,,
stdnum/is_/kennitala.py,sha256=w9cKrUQFcSGO4W0r9XRnf2zY4Gm83HmL2Yic7gOpw5E,3659
stdnum/is_/vsk.py,sha256=gzhhYQ7gOdaxS_7HQeNg7Q9WczZ6kXq-yP88F2P6j58,1974
stdnum/isan.py,sha256=IfNzp7hLNsIpZypeKRBvHJhRICDvg0YLM9i4ctOAm8o,6088
stdnum/isbn.dat,sha256=YQbIQZX3KEErUbaYL2ix-nZ58urvCU5iAqE4mXmtqPA,19270
stdnum/isbn.py,sha256=rVsmf59MDtuzr6rZj3Fx3RN7Cum4XCNcuX_NQkFPnpg,7226
stdnum/isil.dat,sha256=LRATXZiA3aiwpyteizrS2-jAtAwVOl99u3ftz12lWp4,4653
stdnum/isil.py,sha256=pHbRq0eacmmCI6Fq1OwMP4mkx0wvAUhr7khCht3nkRY,3662
stdnum/isin.py,sha256=0RWmz63bC5KR7xqzDoKonL168ZQwEtIxaNAK4MofCas,5479
stdnum/ismn.py,sha256=mH-6Z5FgQnFp7wdazPGC6zT4VtKpjVNcn2c5C3IR5EU,4490
stdnum/iso11649.py,sha256=bBF9HcL2jCRC600Wm1v5Jk5lYSW6C1RS8jw45fUhnmI,2933
stdnum/iso6346.py,sha256=LirOt03MAxPvB8pyqPVH3V0c8Ky_Ntc5mYwqh_X3sBs,3138
stdnum/iso7064/__init__.py,sha256=SEyBlYRMp0ONFnKtwZunDJfCMh7mBWMfulnmQDc45AE,1363
stdnum/iso7064/__pycache__/__init__.cpython-311.pyc,,
stdnum/iso7064/__pycache__/mod_11_10.cpython-311.pyc,,
stdnum/iso7064/__pycache__/mod_11_2.cpython-311.pyc,,
stdnum/iso7064/__pycache__/mod_37_2.cpython-311.pyc,,
stdnum/iso7064/__pycache__/mod_37_36.cpython-311.pyc,,
stdnum/iso7064/__pycache__/mod_97_10.cpython-311.pyc,,
stdnum/iso7064/mod_11_10.py,sha256=InFylTeDnAmNp9Fu5drdMc1MV49CXfHb7vfHTO7sMpA,2116
stdnum/iso7064/mod_11_2.py,sha256=zoERg5p6R_DMXCPZEKNzdtC1cPg4TYtbt-dHRcKkwfw,2144
stdnum/iso7064/mod_37_2.py,sha256=u01zLrl4UNtsWuiI4vzsvYrbtetLtHmR5xaqYroGMcM,2536
stdnum/iso7064/mod_37_36.py,sha256=RIUiTlkdYUw1dgVdhm6io_bvE8hqU-LyfgDlaJ1ETlk,2590
stdnum/iso7064/mod_97_10.py,sha256=Vd1HiYzvfj-0YJwAw_5b3d2Bz-UY8a1rN13kv_E6His,2259
stdnum/iso9362.py,sha256=J2KFDanGK3KiNmdcCwB928ZWCgJcy2LSNPorqIgHMfM,1124
stdnum/isrc.py,sha256=aIwraGeUgnR8s-UBN3mpozX5auj9zX20VQ1LdMnd2mw,3027
stdnum/issn.py,sha256=linT8jULquL3RUeucJJX5xv7edpPo46a8Uj77ZxPPM4,3123
stdnum/it/__init__.py,sha256=NahEE09nAYoawd9eJsYng9CgqSKjNyhtb2S0op7RGuo,942
stdnum/it/__pycache__/__init__.cpython-311.pyc,,
stdnum/it/__pycache__/aic.cpython-311.pyc,,
stdnum/it/__pycache__/codicefiscale.cpython-311.pyc,,
stdnum/it/__pycache__/iva.cpython-311.pyc,,
stdnum/it/aic.py,sha256=uT3LwWYuqgp3REBkvnxcZX078c3rLmzqQKagFFx0h24,4218
stdnum/it/codicefiscale.py,sha256=xrMTZdSx3zF3pGDPYAwku1wKh6OSNlTaeg8_dG4IL_g,5822
stdnum/it/iva.py,sha256=YVZKlfiGoRZkh_984XWIJDyxuz5R1c1KCCjxXMZbibE,2429
stdnum/jp/__init__.py,sha256=SR8jDZjpwA3EkFNQQ2zF1uzza7zgSH4Nlmnn2M5spWQ,916
stdnum/jp/__pycache__/__init__.cpython-311.pyc,,
stdnum/jp/__pycache__/cn.cpython-311.pyc,,
stdnum/jp/cn.py,sha256=694kkLR4bFrk73WISYTQ0d4aAjwm_I7E7z-GcJg-Axg,2772
stdnum/ke/__init__.py,sha256=ZnFokHh7xJjNCFhX2X4pbDfSi8M3CYXOo0Tn5_MgUCM,934
stdnum/ke/__pycache__/__init__.cpython-311.pyc,,
stdnum/ke/__pycache__/pin.cpython-311.pyc,,
stdnum/ke/pin.py,sha256=8OxTwfpp7hSoxbQQZPU0SeKhiofymFCEJaz51ZjQL7I,2973
stdnum/kr/__init__.py,sha256=7eg-lrHBpIm2osy239KHkvsTosB9QbGq7U8ZbV_4l_A,950
stdnum/kr/__pycache__/__init__.cpython-311.pyc,,
stdnum/kr/__pycache__/brn.cpython-311.pyc,,
stdnum/kr/__pycache__/rrn.cpython-311.pyc,,
stdnum/kr/brn.py,sha256=N8FQxnhSSKqC1rzLuZCm7HhlFaWedzWcJLi1eTxa_d0,2708
stdnum/kr/rrn.py,sha256=2Eg37IMtd9exqhEMTO-9A5dlTuuHXTB_TUpPC2ylHis,4484
stdnum/lei.py,sha256=70MULGuWYIPPSt-L0H_98OSMNNi47C0o8i0TjLPmRCc,2180
stdnum/li/__init__.py,sha256=RAjw4xK4qKH7HjUHhSFAvMgADsEtU1uFKmr186gfxtM,881
stdnum/li/__pycache__/__init__.cpython-311.pyc,,
stdnum/li/__pycache__/peid.cpython-311.pyc,,
stdnum/li/peid.py,sha256=xB0PZSpk9FZL1gYr8JAlSe9TFGlHDMUus1k4glCZGa4,2235
stdnum/lt/__init__.py,sha256=NXgHUucXduuBVtsIeyxaRsovxLONAxaKjnpiOE6wLj0,948
stdnum/lt/__pycache__/__init__.cpython-311.pyc,,
stdnum/lt/__pycache__/asmens.cpython-311.pyc,,
stdnum/lt/__pycache__/pvm.cpython-311.pyc,,
stdnum/lt/asmens.py,sha256=z14EAN4bEXipuoOg0tcoCtoduU1w5vw9BvYJc3TyABY,2491
stdnum/lt/pvm.py,sha256=cIBoBfgq6tktzXvz59Fx-ixnQeQmNmzqdY1l0E7aXjE,2990
stdnum/lu/__init__.py,sha256=jZ8XdZUNb_HH4GOgfjS2FNDAb6e7kAQz1tqKQOHjqfk,954
stdnum/lu/__pycache__/__init__.cpython-311.pyc,,
stdnum/lu/__pycache__/tva.cpython-311.pyc,,
stdnum/lu/tva.py,sha256=hetnznVYTdcGubWTd_EgyTGKCcxqwFaQ0l4H_6dLiIM,2311
stdnum/luhn.py,sha256=w69f3opcPijISfIWXa3snRsW_4Pp9G26Er4drTdlIhM,2694
stdnum/lv/__init__.py,sha256=cN2hvA9ONxofo_7gU5Gxo7M7jW8o_51MmRa2F8OVLs8,942
stdnum/lv/__pycache__/__init__.cpython-311.pyc,,
stdnum/lv/__pycache__/pvn.cpython-311.pyc,,
stdnum/lv/pvn.py,sha256=ozxczvt5_owb174MYy9aNGvEuJxnLZBmmuQrM-DPg8U,3876
stdnum/ma/__init__.py,sha256=RO2jXLJKy7fzvAIw18iH5Hk4yad9Jjsja0DqalKckjQ,946
stdnum/ma/__pycache__/__init__.cpython-311.pyc,,
stdnum/ma/__pycache__/ice.cpython-311.pyc,,
stdnum/ma/ice.py,sha256=0yAdtkR0c9h6bTLrolukMfhK-mcDrqHta0e3BwXtT9E,3245
stdnum/mac.py,sha256=OvDvOo7iuFVJHngm37NGbzqikOjaXUre_2JP5-m0epo,4797
stdnum/mc/__init__.py,sha256=AF62Zue4Mx5rV5asscEODbfzdLK1EQeghcLLu48A1rw,942
stdnum/mc/__pycache__/__init__.cpython-311.pyc,,
stdnum/mc/__pycache__/tva.cpython-311.pyc,,
stdnum/mc/tva.py,sha256=xzWNHZv6WtrVYhpPnLbjSQqOI49zD0hxd0CAZ87P4MA,1984
stdnum/md/__init__.py,sha256=ic7n17YHMlJ6IHLZwZN80oEmEDiNWOWn7TG7t8bVykc,872
stdnum/md/__pycache__/__init__.cpython-311.pyc,,
stdnum/md/__pycache__/idno.cpython-311.pyc,,
stdnum/md/idno.py,sha256=53FCj9_ZLnbly0ArQZcO0HwxoQQ7SrBAcibI7iIq9Eg,2435
stdnum/me/__init__.py,sha256=3byMiqL3p60R5Z556JvvII7KRdDmTk7bkAUDDQR26EY,940
stdnum/me/__pycache__/__init__.cpython-311.pyc,,
stdnum/me/__pycache__/iban.cpython-311.pyc,,
stdnum/me/__pycache__/pib.cpython-311.pyc,,
stdnum/me/iban.py,sha256=D8xp9ClWFz0KTvs4N82oagH3WFJSM4z1hY507N1jOXw,2249
stdnum/me/pib.py,sha256=A6eUcWXFja65HfBzr24uyahppDbyU04vk0CxTbMYLDI,2357
stdnum/meid.py,sha256=nituEsOAeNIGgYRDljxOULWHJ44bvvEtx3sFQQ5cb0w,6924
stdnum/mk/__init__.py,sha256=IbnnIs9I-LFvPdaQiPfVEcMqeUbYp63nL3utHLEc5lg,952
stdnum/mk/__pycache__/__init__.cpython-311.pyc,,
stdnum/mk/__pycache__/edb.cpython-311.pyc,,
stdnum/mk/edb.py,sha256=WDTGyJ1ZIFNF5V695_YJQhVcOZE1Rr_1Jcn-C3CqQfU,3030
stdnum/mt/__init__.py,sha256=rLVwjU64pROIUOfHvNWvV4sLSrfLuYXmrQB-HhZJ-ME,868
stdnum/mt/__pycache__/__init__.cpython-311.pyc,,
stdnum/mt/__pycache__/vat.cpython-311.pyc,,
stdnum/mt/vat.py,sha256=UFuhW1EPgNV-w2Vetz8bVpSCS8YWceZy_zttpfZaZWs,2189
stdnum/mu/__init__.py,sha256=6ZHvCTeDRiJTarS8AYiyKaj__IGiIPd48OllNLlsQ3M,872
stdnum/mu/__pycache__/__init__.cpython-311.pyc,,
stdnum/mu/__pycache__/nid.cpython-311.pyc,,
stdnum/mu/nid.py,sha256=dR81Vh8Tew1nwI3gW3VealrkVbhrwEOa2mGn5PMHrlY,2910
stdnum/mx/__init__.py,sha256=dYs1gEYaxAkp5g_4flgrSgYEOfSLKjEDCth1toDPIFA,994
stdnum/mx/__pycache__/__init__.cpython-311.pyc,,
stdnum/mx/__pycache__/curp.cpython-311.pyc,,
stdnum/mx/__pycache__/rfc.cpython-311.pyc,,
stdnum/mx/curp.py,sha256=ZLAimTIx8zwC5QJR5yU-qWA57nMMjZ4RbiweuX4ommw,4305
stdnum/mx/rfc.py,sha256=O1_-nzwTusXC3gYpmKsNyeIe5r-0MhXJAN8ybKRmnR0,5336
stdnum/my/__init__.py,sha256=8I7UPXtTDgbydzOtTnfFOkeznBZetfjT4T1reaV7XIk,872
stdnum/my/__pycache__/__init__.cpython-311.pyc,,
stdnum/my/__pycache__/nric.cpython-311.pyc,,
stdnum/my/bp.dat,sha256=GeimRMnSaqIoyIybnR8msWKTwvdT_NZOoXKU1gYNZ5A,8041
stdnum/my/nric.py,sha256=8ehnkOkaPJ6YNyly_YHXainJbvVq436DN_7_M7DaZ0g,3611
stdnum/nl/__init__.py,sha256=JO3229u67v6Spa-joCaMX_MBTFiffYFUDxx69VBKRq0,995
stdnum/nl/__pycache__/__init__.cpython-311.pyc,,
stdnum/nl/__pycache__/brin.cpython-311.pyc,,
stdnum/nl/__pycache__/bsn.cpython-311.pyc,,
stdnum/nl/__pycache__/btw.cpython-311.pyc,,
stdnum/nl/__pycache__/onderwijsnummer.cpython-311.pyc,,
stdnum/nl/__pycache__/postcode.cpython-311.pyc,,
stdnum/nl/brin.py,sha256=u_3LA2vJXZH04FOF1CGaRVSEMmb__g9v7eIpfo1vN_c,2691
stdnum/nl/bsn.py,sha256=2xeCwj7LKHmwIz_Q_Uf6EwC4H9U0YmHPKl-L18NUcOE,2836
stdnum/nl/btw.py,sha256=Dmi5dvNowvQ43pxDFfTQUdgtvUPApfliZSbNU-fqmI4,3072
stdnum/nl/onderwijsnummer.py,sha256=4okle7uvwM2GhQFxZa9l_H7nj2K92G_fhOKZ1TSUCn4,2348
stdnum/nl/postcode.py,sha256=E_9KwTvMjMjuw73viYr-TlBvGhEqRCpNmi7C9UZRZ08,2481
stdnum/no/__init__.py,sha256=tA0Xho-M7T0pHCgO8N8wGj-527GYDOJfpdXcfQYUo0k,1003
stdnum/no/__pycache__/__init__.cpython-311.pyc,,
stdnum/no/__pycache__/fodselsnummer.cpython-311.pyc,,
stdnum/no/__pycache__/iban.cpython-311.pyc,,
stdnum/no/__pycache__/kontonr.cpython-311.pyc,,
stdnum/no/__pycache__/mva.cpython-311.pyc,,
stdnum/no/__pycache__/orgnr.cpython-311.pyc,,
stdnum/no/fodselsnummer.py,sha256=TRwJBB8zmvxLmj10PuDPQmZdGFcJ17yY0dCoiBIYvgc,4686
stdnum/no/iban.py,sha256=sjUfOg3F7KaoErbe_kdDF134jK_XZ91LuB9fIxIvX14,2453
stdnum/no/kontonr.py,sha256=E3wc_eebXzqI_mjURAG0sgBd2eR1qfwjVHDK9zEzuas,3221
stdnum/no/mva.py,sha256=6w4vr7FX5NNqWm9L6d7o-TGZZWSS4-6SU2rZvlP_hmU,2251
stdnum/no/orgnr.py,sha256=dQKBZv5E3lmK-yQVv-2flhman90CuWsph67DGocT7_w,2548
stdnum/numdb.py,sha256=6uOAogoAuvsItaU-tkfls7uuxEUifDh1dtMvWtoyFHY,6500
stdnum/nz/__init__.py,sha256=BBlSxJ3uOUcGN3BSbMvS2YZs-tExVmZMDgxGjl4TCpM,942
stdnum/nz/__pycache__/__init__.cpython-311.pyc,,
stdnum/nz/__pycache__/bankaccount.cpython-311.pyc,,
stdnum/nz/__pycache__/ird.cpython-311.pyc,,
stdnum/nz/bankaccount.py,sha256=Rrrnt_9Iij-nXfg3gG6tjgaffs20-ICEIVU5wrdljqw,5191
stdnum/nz/banks.dat,sha256=TXNpiO0kB-S50dpqiOTCb5PySwLvRElFGYkvDgwE_mw,71255
stdnum/nz/ird.py,sha256=qWrBrViTMc_LLURZEVbzpLaHhNaaiy_dgg8ohnRoRms,3264
stdnum/oui.dat,sha256=GtM6vrHWW1-i4rG3sidBiWQGPCT0tbT5HA7h1XPZ9Y4,988486
stdnum/pe/__init__.py,sha256=0qRLalTNcEoz8VS_43TZTLq3NhZ6ZwjPjT_RXyTZsm0,917
stdnum/pe/__pycache__/__init__.cpython-311.pyc,,
stdnum/pe/__pycache__/cui.cpython-311.pyc,,
stdnum/pe/__pycache__/ruc.cpython-311.pyc,,
stdnum/pe/cui.py,sha256=QKMcrf6eFlTplqlB47kBDO1NT3gXn2AfkD9PVHeYCaU,2926
stdnum/pe/ruc.py,sha256=DjX_h2h0nVShwkQ5IQwraHw9T2AjoSC9cjnTYaLsBLI,2897
stdnum/pk/__init__.py,sha256=DcWim3F6Lvzx8ykEm7IAtgZ4XFkOgCDkMG__v__ixyU,872
stdnum/pk/__pycache__/__init__.cpython-311.pyc,,
stdnum/pk/__pycache__/cnic.cpython-311.pyc,,
stdnum/pk/cnic.py,sha256=5qdxiuxd3ooTTEKr5RnUopXs6bGcKjsZV-a4tQ9liEY,3284
stdnum/pl/__init__.py,sha256=RQla8ESnfViG8dZPSJFAlownFnncJEv2DxkBiuG5YG0,940
stdnum/pl/__pycache__/__init__.cpython-311.pyc,,
stdnum/pl/__pycache__/nip.cpython-311.pyc,,
stdnum/pl/__pycache__/pesel.cpython-311.pyc,,
stdnum/pl/__pycache__/regon.cpython-311.pyc,,
stdnum/pl/nip.py,sha256=hAsKfqJbNNiWZ_ygNczhj-gQlPk6voJBiiB33QB60sI,2469
stdnum/pl/pesel.py,sha256=tt4eNqBz2jP7RmIG1PIUtnoodi3BjDJVSkOtuOiAeQ0,3593
stdnum/pl/regon.py,sha256=w7JeM7YGDM2sn1fsp9w4jmVg3IUSptTMsENgrVTtkZA,3074
stdnum/pt/__init__.py,sha256=IQXCRSBNXMYiLT1WE5CrljXRarLRQGGgB_p85s-7VIo,948
stdnum/pt/__pycache__/__init__.cpython-311.pyc,,
stdnum/pt/__pycache__/cc.cpython-311.pyc,,
stdnum/pt/__pycache__/nif.cpython-311.pyc,,
stdnum/pt/cc.py,sha256=SYrOufVGRHutPrTtBgEp5Tbemf8slVRwjpiSK6XsWnQ,2927
stdnum/pt/nif.py,sha256=dOWmA3er2SFhsZPt1sT8cXM1ZzUZ3ynGyBg-pAX5fr0,2424
stdnum/py/__init__.py,sha256=m7WSjAlXNfOhSmD_VKNiKMzAADE-myXMJ8m9xXVSIF0,942
stdnum/py/__pycache__/__init__.cpython-311.pyc,,
stdnum/py/__pycache__/ruc.cpython-311.pyc,,
stdnum/py/ruc.py,sha256=3cgJAINs0auoqcFLi-Zn7pi2rfENdffS3Ijjp_j77H8,2920
stdnum/ro/__init__.py,sha256=54hjbMen5kc1Ztxl4ANjeHvNHmzI4VcyDcuyUFQk28I,943
stdnum/ro/__pycache__/__init__.cpython-311.pyc,,
stdnum/ro/__pycache__/cf.cpython-311.pyc,,
stdnum/ro/__pycache__/cnp.cpython-311.pyc,,
stdnum/ro/__pycache__/cui.cpython-311.pyc,,
stdnum/ro/__pycache__/onrc.cpython-311.pyc,,
stdnum/ro/cf.py,sha256=2K-K29YELD4DxxU6TTYkdoB3e9OWz28aoDo6xP1KNu0,2229
stdnum/ro/cnp.py,sha256=NP8LbFTpLPa8scO0ufLtFBtXlsOAue55CRcPO9LoEAY,5129
stdnum/ro/cui.py,sha256=rB6NsERJC7GNsCMHYdQdgUcFgSlJ6jY94c8gwV2RyPQ,2894
stdnum/ro/onrc.py,sha256=i62mgqmUvv6EChqCWexYFwPDz9ZdXHdbgsExpHIoWf0,3341
stdnum/rs/__init__.py,sha256=lttNrejqqK-JE3lNegF-ol3HUHFKwti1MzYpJe77In8,942
stdnum/rs/__pycache__/__init__.cpython-311.pyc,,
stdnum/rs/__pycache__/pib.cpython-311.pyc,,
stdnum/rs/pib.py,sha256=R44gtg9t7fay9FSXZp_Rme22kYTvi8Lf0eNzh2RNLSM,1963
stdnum/ru/__init__.py,sha256=kvFgZJb-BjB5qLbuvu940mygVm8w-USo6_uD4JMrS_A,942
stdnum/ru/__pycache__/__init__.cpython-311.pyc,,
stdnum/ru/__pycache__/inn.cpython-311.pyc,,
stdnum/ru/inn.py,sha256=3JIUjrp5wN8g7sVV8HSHJpfNUZQKOGGDvGiILLuSBOc,2995
stdnum/se/__init__.py,sha256=Fbncg2oG7Y6kBjS1w7I0IiseWrCXMRhNXsKMYxvLOX0,1012
stdnum/se/__pycache__/__init__.cpython-311.pyc,,
stdnum/se/__pycache__/orgnr.cpython-311.pyc,,
stdnum/se/__pycache__/personnummer.cpython-311.pyc,,
stdnum/se/__pycache__/postnummer.cpython-311.pyc,,
stdnum/se/__pycache__/vat.cpython-311.pyc,,
stdnum/se/orgnr.py,sha256=X4l4p3n9er4cxPfpBUN6o5XZb3fAMS3GpzJJEXSRQn4,2363
stdnum/se/personnummer.py,sha256=JOXmJKFxU1ic5XXNVz6_6HP47ZpYNALJG-3oCRm6VdY,3798
stdnum/se/postnummer.py,sha256=JbsyCpwWzsS9GAenupEf0oogC31zEJ7aYGQAlRsPOZw,2346
stdnum/se/vat.py,sha256=kGfkXmUhy6vrfXB8I_vt1oAgT_kjJBY0n5dgbKNsDzA,2127
stdnum/sg/__init__.py,sha256=Djqr04UcVvg5Zj2JRS7V4zUIsaUBHjfIM8CJLEK5_nE,940
stdnum/sg/__pycache__/__init__.cpython-311.pyc,,
stdnum/sg/__pycache__/uen.cpython-311.pyc,,
stdnum/sg/uen.py,sha256=0SNRuwAxsYotO5803tzwZbSB9p0PlaNxrTpr6UCYnwU,5826
stdnum/si/__init__.py,sha256=fviaFXgujbV5dZHp7ux-7zyfb4zEZZY9-qMKTfJSBb4,1085
stdnum/si/__pycache__/__init__.cpython-311.pyc,,
stdnum/si/__pycache__/ddv.cpython-311.pyc,,
stdnum/si/__pycache__/emso.cpython-311.pyc,,
stdnum/si/__pycache__/maticna.cpython-311.pyc,,
stdnum/si/ddv.py,sha256=q-GpIefwDZYeiYlKn5zQsw_vAT_0hWHfcBfh6diLo4k,2494
stdnum/si/emso.py,sha256=h1O6vr_KbD1krxaW2m5IKrJaqvIoIMe9dBnOqmuqIoE,3526
stdnum/si/maticna.py,sha256=r7LiImVpjgGtUizO9erZLGxQYNeX10NlPuviy9kLj6s,3187
stdnum/sk/__init__.py,sha256=C-pj2GD6tiSq7MUryqLRRZFHuwSL0PK74urNk0CRjf8,940
stdnum/sk/__pycache__/__init__.cpython-311.pyc,,
stdnum/sk/__pycache__/dph.cpython-311.pyc,,
stdnum/sk/__pycache__/rc.cpython-311.pyc,,
stdnum/sk/dph.py,sha256=UM_NS5e2urhugJdIaIKJ4PIZ2KYvRCc44DTbtCpohY0,2457
stdnum/sk/rc.py,sha256=pJvIWGxrfFZ7mb0_99yWHi84AoiL8iF9LB3zcJRyzf0,1905
stdnum/sm/__init__.py,sha256=zKM23NN9hlGye6hLrMa1dvpJ22AXg6lBrOELspWhSc4,948
stdnum/sm/__pycache__/__init__.cpython-311.pyc,,
stdnum/sm/__pycache__/coe.cpython-311.pyc,,
stdnum/sm/coe.py,sha256=6lKsux1_1AoXQuW28hKNj_hsqOj_q8wLopOhSBb68G0,2500
stdnum/sv/__init__.py,sha256=T3DnAC7JfJ2p_tFvvpqkkH5RhvVLlp9P4zKumkOQhWk,944
stdnum/sv/__pycache__/__init__.cpython-311.pyc,,
stdnum/sv/__pycache__/nit.cpython-311.pyc,,
stdnum/sv/nit.py,sha256=UTQE1AR3TLIsCHbxBe4SR7amYjH7KKU8wwdIdWTs6MY,4054
stdnum/th/__init__.py,sha256=2JcPxKAJBhxn07_1cJh6nOdA7pmsoKrWzHaOaIm4ASU,865
stdnum/th/__pycache__/__init__.cpython-311.pyc,,
stdnum/th/__pycache__/moa.cpython-311.pyc,,
stdnum/th/__pycache__/pin.cpython-311.pyc,,
stdnum/th/__pycache__/tin.cpython-311.pyc,,
stdnum/th/moa.py,sha256=nZQfgJdUSDUfkqxZDSd6EiaEiI54GgxcNwe-qibGxrE,2982
stdnum/th/pin.py,sha256=g7PKvxEFiu13tnzZ_l4iqWrmaDaoCm73KoPaO6GfAwI,2809
stdnum/th/tin.py,sha256=hGtLi2kMBmhtUL_06ES80W-BwLzDQOisIuPRWdos0gc,2797
stdnum/tn/__init__.py,sha256=WwxRElDvNLn524X8cyyoJaNh2xdGPjz389aHdA1da7c,937
stdnum/tn/__pycache__/__init__.cpython-311.pyc,,
stdnum/tn/__pycache__/mf.cpython-311.pyc,,
stdnum/tn/mf.py,sha256=70QSzPJBCvjMWBJ4PmxdBAhUcpMVZg9uEj5IMxrqlJs,4242
stdnum/tr/__init__.py,sha256=tJIeF1CbAkLw2bZlXsUYukju4IPnEcmVKMv1USJNhJE,915
stdnum/tr/__pycache__/__init__.cpython-311.pyc,,
stdnum/tr/__pycache__/tckimlik.cpython-311.pyc,,
stdnum/tr/__pycache__/vkn.cpython-311.pyc,,
stdnum/tr/tckimlik.py,sha256=c3vB1xNdJ_mjO02IPJIkN9U3ZrcsFiuqXMlFkrHLjEE,3730
stdnum/tr/vkn.py,sha256=DTJqWw-ctS-bFYn7FX-acpj_0Bz7wbQdjiJ3UP24xK8,2595
stdnum/tw/__init__.py,sha256=rMcN8IcPMaRgKIK6TpK7ga5J5JqHv--nh1JYDi1HarQ,940
stdnum/tw/__pycache__/__init__.cpython-311.pyc,,
stdnum/tw/__pycache__/ubn.cpython-311.pyc,,
stdnum/tw/ubn.py,sha256=YVLSglV9RDQTy9-C-97tJ90WJUXfHXv8jvspOEpRumo,2819
stdnum/ua/__init__.py,sha256=KfWle3iWiVnqmD3sTpcw9VJxQGi1PYJrYyyKcx8YaRA,874
stdnum/ua/__pycache__/__init__.cpython-311.pyc,,
stdnum/ua/__pycache__/edrpou.cpython-311.pyc,,
stdnum/ua/__pycache__/rntrc.cpython-311.pyc,,
stdnum/ua/edrpou.py,sha256=MapqxVNXD2Tar417HW2ErVzXuLB3_blnlqUHDNY-hG4,3035
stdnum/ua/rntrc.py,sha256=vAX3z5WkEgjM4oY4JsROh2u7O6jUHEA4gwENHr8nBBo,2712
stdnum/us/__init__.py,sha256=PpQA2ZV75PbafUYxNV7fnZvgS4lkmfZde1_NIHndKa8,880
stdnum/us/__pycache__/__init__.cpython-311.pyc,,
stdnum/us/__pycache__/atin.cpython-311.pyc,,
stdnum/us/__pycache__/ein.cpython-311.pyc,,
stdnum/us/__pycache__/itin.cpython-311.pyc,,
stdnum/us/__pycache__/ptin.cpython-311.pyc,,
stdnum/us/__pycache__/rtn.cpython-311.pyc,,
stdnum/us/__pycache__/ssn.cpython-311.pyc,,
stdnum/us/__pycache__/tin.cpython-311.pyc,,
stdnum/us/atin.py,sha256=evR_KwX1aH4ZjTHn7zoaiKXixa4hhDByknUd70mb7wY,2401
stdnum/us/ein.dat,sha256=Ul74M5TfxwcRhIFYNAP9hAdCTg5qaWkfp4_r9MdFA3E,648
stdnum/us/ein.py,sha256=K8CTCOGua1mseZ4KwKib8FhE8yUoXjQKSgGXJstmvtw,2852
stdnum/us/itin.py,sha256=R_n_iSug0YfNF5-R4v9afmCI8ylMQM1WfJnXt715zlI,3022
stdnum/us/ptin.py,sha256=ZpoeBosxzwixVR0ZYSh_l8rHmdHfOApP0-JgZbQH92Q,2067
stdnum/us/rtn.py,sha256=iwK6s1qJF1zhKlS2bXx1tnZgez9MBiwJsy9OOSnMkoc,2581
stdnum/us/ssn.py,sha256=rRTwy2J4DB_-OOjVm8HFYBpGjO5j5ZN3Ud-tksDnTqI,3807
stdnum/us/tin.py,sha256=OVe5dwa37JrnSFFMOg16gTegsnaVmVfT6ZDLmzmvitg,2921
stdnum/util.py,sha256=nMk-31P01GoGqag3BqkORIthAXsKGhBl6KD0siNmzv8,12126
stdnum/uy/__init__.py,sha256=uQd9k0HjCS98bkkJx2yb4eBEqcFeribeFyfX8iOWb-s,940
stdnum/uy/__pycache__/__init__.cpython-311.pyc,,
stdnum/uy/__pycache__/rut.cpython-311.pyc,,
stdnum/uy/rut.py,sha256=8LcA0xk13ixEXwEWyForcbwuPNaFjXd5EZwWgn8z700,3522
stdnum/vatin.py,sha256=PFSPlogxCd-erSP-EGclUJa-VAv42CJm3eKmDZAVIgs,3070
stdnum/ve/__init__.py,sha256=qEgP7sTxy-sQ6dVsXR_6niwCuy9FFJEYoHW9FCcrjYs,940
stdnum/ve/__pycache__/__init__.cpython-311.pyc,,
stdnum/ve/__pycache__/rif.cpython-311.pyc,,
stdnum/ve/rif.py,sha256=d5RCjlOgvwz-_wG4SUYTczAzNOJZAjidydzAEHtqeHk,2840
stdnum/verhoeff.py,sha256=viPGgtzlaUbbWnV2fY6dd1kFt6urPEX1cQsvCk-fY3k,3654
stdnum/vn/__init__.py,sha256=aahN1Z3e6MEAav-wviIx0_Vh3rAtBYM8lINqu09FYZQ,936
stdnum/vn/__pycache__/__init__.cpython-311.pyc,,
stdnum/vn/__pycache__/mst.cpython-311.pyc,,
stdnum/vn/mst.py,sha256=q-_lPR-xlCdxzPRIf9NMufzvhzmYT-JXvc740lk_ANs,3608
stdnum/za/__init__.py,sha256=GujMEb1Q3xlP-20upOlMoXWJd1HraYMkaE9NFAsEwZA,880
stdnum/za/__pycache__/__init__.cpython-311.pyc,,
stdnum/za/__pycache__/idnr.cpython-311.pyc,,
stdnum/za/__pycache__/tin.cpython-311.pyc,,
stdnum/za/idnr.py,sha256=35dfjwTfVGTXFguIu77HQyTHki2N6AyU_aDKZeTEfrc,3790
stdnum/za/tin.py,sha256=cUWnXyEX86GBTt1MQKzidNeCwbFy_kAPMtcg9yL-34g,2571
