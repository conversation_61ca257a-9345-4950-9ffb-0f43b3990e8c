Metadata-Version: 2.1
Name: literalai
Version: 0.1.201
Summary: An SDK for observability in Python applications
Author: Literal AI
Author-email: <EMAIL>
License: Apache License 2.0
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: packaging>=23.0
Requires-Dist: httpx>=0.23.0
Requires-Dist: pydantic<3,>=1
Requires-Dist: chevron>=0.14.0
Requires-Dist: traceloop-sdk>=0.33.12

# Literal AI client

## Installation

```bash
pip install literalai
```

## Usage

The full documentation is available [here](https://docs.getliteral.ai/python-client).

Create a `.env` file with the `LITERAL_API_KEY` environment variable set to your API key.

```python
from literalai import LiteralClient
from dotenv import load_dotenv

load_dotenv()

literalai_client = LiteralClient()

@literalai_client.step(type="run")
def my_step(input):
    return f"World"


@literalai_client.thread
def main():
    print(my_step("Hello"))


main()
client.flush_and_stop()
print("Done")
```

## Development setup

```bash
pip install -r requirements-dev.txt
```
