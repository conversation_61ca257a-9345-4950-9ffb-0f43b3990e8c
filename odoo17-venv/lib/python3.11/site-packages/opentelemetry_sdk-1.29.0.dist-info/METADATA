Metadata-Version: 2.3
Name: opentelemetry-sdk
Version: 1.29.0
Summary: OpenTelemetry Python SDK
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python/tree/main/opentelemetry-sdk
Author-email: OpenTelemetry Authors <<EMAIL>>
License: Apache-2.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: OpenTelemetry
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Typing :: Typed
Requires-Python: >=3.8
Requires-Dist: opentelemetry-api==1.29.0
Requires-Dist: opentelemetry-semantic-conventions==0.50b0
Requires-Dist: typing-extensions>=3.7.4
Description-Content-Type: text/x-rst

OpenTelemetry Python SDK
============================================================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-sdk.svg
   :target: https://pypi.org/project/opentelemetry-sdk/

Installation
------------

::

    pip install opentelemetry-sdk

References
----------

* `OpenTelemetry Project <https://opentelemetry.io/>`_
