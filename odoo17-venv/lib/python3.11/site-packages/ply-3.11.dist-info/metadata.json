{"classifiers": ["Programming Language :: Python :: 3", "Programming Language :: Python :: 2"], "description_content_type": "UNKNOWN", "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://www.dabeaz.com/ply/"}}}, "generator": "bdist_wheel (0.30.0)", "license": "BSD", "metadata_version": "2.0", "name": "ply", "summary": "Python Lex & Yacc", "version": "3.11"}