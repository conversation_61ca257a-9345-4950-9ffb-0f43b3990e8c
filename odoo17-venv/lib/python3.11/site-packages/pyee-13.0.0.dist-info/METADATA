Metadata-Version: 2.2
Name: pyee
Version: 13.0.0
Summary: A rough port of Node.js's EventEmitter to Python with a few tricks of its own
Author-email: <PERSON> <<EMAIL>>
License: MIT
Project-URL: Repository, https://github.com/jfhbrook/pyee
Project-URL: Documentation, https://pyee.readthedocs.io
Keywords: events,emitter,node.js,node,eventemitter,event_emitter
Classifier: Programming Language :: Python
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Other/Nonlisted Topic
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: typing-extensions
Provides-Extra: dev
Requires-Dist: build; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: flake8-black; extra == "dev"
Requires-Dist: pytest; extra == "dev"
Requires-Dist: pytest-asyncio; python_version >= "3.4" and extra == "dev"
Requires-Dist: pytest-trio; python_version >= "3.7" and extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: isort; extra == "dev"
Requires-Dist: jupyter-console; extra == "dev"
Requires-Dist: mkdocs; extra == "dev"
Requires-Dist: mkdocs-include-markdown-plugin; extra == "dev"
Requires-Dist: mkdocstrings[python]; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Requires-Dist: sphinx; extra == "dev"
Requires-Dist: toml; extra == "dev"
Requires-Dist: tox; extra == "dev"
Requires-Dist: trio; extra == "dev"
Requires-Dist: trio; python_version > "3.6" and extra == "dev"
Requires-Dist: trio-typing; python_version > "3.6" and extra == "dev"
Requires-Dist: twine; extra == "dev"
Requires-Dist: twisted; extra == "dev"
Requires-Dist: validate-pyproject[all]; extra == "dev"

# pyee

[![Documentation Status](https://readthedocs.org/projects/pyee/badge/?version=latest)](https://pyee.readthedocs.io/en/latest/?badge=latest)

pyee supplies a `EventEmitter` object that is similar to the
`EventEmitter` class from Node.js. It also supplies a number of subclasses
with added support for async and threaded programming in python, such as
async/await.

## Docs

Autogenerated API docs, including basic installation directions and examples,
can be found at <https://pyee.readthedocs.io>.

## Development

See [DEVELOPMENT.md](./DEVELOPMENT.md).

## Changelog

See [CHANGELOG.md](./CHANGELOG.md).

## Contributors

See [CONTRIBUTORS.md](./CONTRIBUTORS.md).

## License

MIT/X11, see [LICENSE](./LICENSE).
