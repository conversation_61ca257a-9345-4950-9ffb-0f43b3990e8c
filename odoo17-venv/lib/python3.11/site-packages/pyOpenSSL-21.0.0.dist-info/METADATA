Metadata-Version: 2.1
Name: pyOpenSSL
Version: 21.0.0
Summary: Python wrapper module around the OpenSSL library
Home-page: https://pyopenssl.org/
Author: The pyOpenSSL developers
Author-email: <EMAIL>
License: Apache License, Version 2.0
Platform: UNKNOWN
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Security :: Cryptography
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Networking
Requires-Python: >=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*
Requires-Dist: cryptography (>=3.3)
Requires-Dist: six (>=1.5.2)
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'
Requires-Dist: sphinx-rtd-theme ; extra == 'docs'
Provides-Extra: test
Requires-Dist: flaky ; extra == 'test'
Requires-Dist: pretend ; extra == 'test'
Requires-Dist: pytest (>=3.0.1) ; extra == 'test'

========================================================
pyOpenSSL -- A Python wrapper around the OpenSSL library
========================================================

.. image:: https://readthedocs.org/projects/pyopenssl/badge/?version=stable
   :target: https://pyopenssl.org/en/stable/
   :alt: Stable Docs

.. image:: https://github.com/pyca/pyopenssl/workflows/CI/badge.svg?branch=main
   :target: https://github.com/pyca/pyopenssl/actions?query=workflow%3ACI+branch%3Amain

.. image:: https://codecov.io/github/pyca/pyopenssl/branch/main/graph/badge.svg
   :target: https://codecov.io/github/pyca/pyopenssl
   :alt: Test coverage

**Note:** The Python Cryptographic Authority **strongly suggests** the use of `pyca/cryptography`_
where possible. If you are using pyOpenSSL for anything other than making a TLS connection
**you should move to cryptography and drop your pyOpenSSL dependency**.

High-level wrapper around a subset of the OpenSSL library. Includes

* ``SSL.Connection`` objects, wrapping the methods of Python's portable sockets
* Callbacks written in Python
* Extensive error-handling mechanism, mirroring OpenSSL's error codes

... and much more.

You can find more information in the documentation_.
Development takes place on GitHub_.


Discussion
==========

If you run into bugs, you can file them in our `issue tracker`_.

We maintain a cryptography-dev_ mailing list for both user and development discussions.

You can also join ``#cryptography-dev`` on Freenode to ask questions or get involved.


.. _documentation: https://pyopenssl.org/
.. _`issue tracker`: https://github.com/pyca/pyopenssl/issues
.. _cryptography-dev: https://mail.python.org/mailman/listinfo/cryptography-dev
.. _GitHub: https://github.com/pyca/pyopenssl
.. _`pyca/cryptography`: https://github.com/pyca/cryptography


Release Information
===================

21.0.0 (2020-09-28)
-------------------

Backward-incompatible changes:
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

- The minimum ``cryptography`` version is now 3.3.
- Drop support for Python 3.5

Deprecations:
^^^^^^^^^^^^^

Changes:
^^^^^^^^

- Raise an error when an invalid ALPN value is set.
  `#993 <https://github.com/pyca/pyopenssl/pull/993>`_
- Added ``OpenSSL.SSL.Context.set_min_proto_version`` and ``OpenSSL.SSL.Context.set_max_proto_version``
  to set the minimum and maximum supported TLS version `#985 <https://github.com/pyca/pyopenssl/pull/985>`_.
- Updated ``to_cryptography`` and ``from_cryptography`` methods to support an upcoming release of ``cryptography`` without raising deprecation warnings.
  `#1030 <https://github.com/pyca/pyopenssl/pull/1030>`_

20.0.1 (2020-12-15)
-------------------

Backward-incompatible changes:
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Deprecations:
^^^^^^^^^^^^^

Changes:
^^^^^^^^

- Fixed compatibility with OpenSSL 1.1.0.

20.0.0 (2020-11-27)
-------------------


Backward-incompatible changes:
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

- The minimum ``cryptography`` version is now 3.2.
- Remove deprecated ``OpenSSL.tsafe`` module.
- Removed deprecated ``OpenSSL.SSL.Context.set_npn_advertise_callback``, ``OpenSSL.SSL.Context.set_npn_select_callback``, and ``OpenSSL.SSL.Connection.get_next_proto_negotiated``.
- Drop support for Python 3.4
- Drop support for OpenSSL 1.0.1 and 1.0.2

Deprecations:
^^^^^^^^^^^^^

- Deprecated ``OpenSSL.crypto.loads_pkcs7`` and ``OpenSSL.crypto.loads_pkcs12``.

Changes:
^^^^^^^^

- Added a new optional ``chain`` parameter to ``OpenSSL.crypto.X509StoreContext()``
  where additional untrusted certificates can be specified to help chain building.
  `#948 <https://github.com/pyca/pyopenssl/pull/948>`_
- Added ``OpenSSL.crypto.X509Store.load_locations`` to set trusted
  certificate file bundles and/or directories for verification.
  `#943 <https://github.com/pyca/pyopenssl/pull/943>`_
- Added ``Context.set_keylog_callback`` to log key material.
  `#910 <https://github.com/pyca/pyopenssl/pull/910>`_
- Added ``OpenSSL.SSL.Connection.get_verified_chain`` to retrieve the
  verified certificate chain of the peer.
  `#894 <https://github.com/pyca/pyopenssl/pull/894>`_.
- Make verification callback optional in ``Context.set_verify``.
  If omitted, OpenSSL's default verification is used.
  `#933 <https://github.com/pyca/pyopenssl/pull/933>`_
- Fixed a bug that could truncate or cause a zero-length key error due to a
  null byte in private key passphrase in ``OpenSSL.crypto.load_privatekey``
  and ``OpenSSL.crypto.dump_privatekey``.
  `#947 <https://github.com/pyca/pyopenssl/pull/947>`_

19.1.0 (2019-11-18)
-------------------


Backward-incompatible changes:
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

- Removed deprecated ``ContextType``, ``ConnectionType``, ``PKeyType``, ``X509NameType``, ``X509ReqType``, ``X509Type``, ``X509StoreType``, ``CRLType``, ``PKCS7Type``, ``PKCS12Type``, and ``NetscapeSPKIType`` aliases.
  Use the classes without the ``Type`` suffix instead.
  `#814 <https://github.com/pyca/pyopenssl/pull/814>`_
- The minimum ``cryptography`` version is now 2.8 due to issues on macOS with a transitive dependency.
  `#875 <https://github.com/pyca/pyopenssl/pull/875>`_

Deprecations:
^^^^^^^^^^^^^

- Deprecated ``OpenSSL.SSL.Context.set_npn_advertise_callback``, ``OpenSSL.SSL.Context.set_npn_select_callback``, and ``OpenSSL.SSL.Connection.get_next_proto_negotiated``.
  ALPN should be used instead.
  `#820 <https://github.com/pyca/pyopenssl/pull/820>`_


Changes:
^^^^^^^^

- Support ``bytearray`` in ``SSL.Connection.send()`` by using cffi's from_buffer.
  `#852 <https://github.com/pyca/pyopenssl/pull/852>`_
- The ``OpenSSL.SSL.Context.set_alpn_select_callback`` can return a new ``NO_OVERLAPPING_PROTOCOLS`` sentinel value
  to allow a TLS handshake to complete without an application protocol.

`Full changelog <https://pyopenssl.org/en/stable/changelog.html>`_.



