phonenumbers-8.13.54.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
phonenumbers-8.13.54.dist-info/LICENSE,sha256=psuoW8kuDP96RQsdhzwOqi6fyWv0ct8CR6Jr7He_P_k,10173
phonenumbers-8.13.54.dist-info/METADATA,sha256=WTs4fASHpoIxZUnntdQQm5H_9TerQpm36an5yuiPzfE,11183
phonenumbers-8.13.54.dist-info/RECORD,,
phonenumbers-8.13.54.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
phonenumbers-8.13.54.dist-info/WHEEL,sha256=9Hm2OB-j1QcCUq9Jguht7ayGIIZBRTdOXD1qg9cCgPM,109
phonenumbers-8.13.54.dist-info/top_level.txt,sha256=_MH-Ln0-kKnNk_VSVXJFRlVIr6mp12OAm4RBfSveeQI,13
phonenumbers/__init__.py,sha256=KGWTFICdoEkpU_7hX__mgPnFGJww2AyTVEq1B567Xfo,9803
phonenumbers/__init__.pyi,sha256=D7kOiDRlhBvOLncGzudmGF01NL3pyU_4_jcLuPBKxrY,6367
phonenumbers/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/__pycache__/asyoutypeformatter.cpython-311.pyc,,
phonenumbers/__pycache__/carrier.cpython-311.pyc,,
phonenumbers/__pycache__/geocoder.cpython-311.pyc,,
phonenumbers/__pycache__/phonemetadata.cpython-311.pyc,,
phonenumbers/__pycache__/phonenumber.cpython-311.pyc,,
phonenumbers/__pycache__/phonenumbermatcher.cpython-311.pyc,,
phonenumbers/__pycache__/phonenumberutil.cpython-311.pyc,,
phonenumbers/__pycache__/prefix.cpython-311.pyc,,
phonenumbers/__pycache__/re_util.cpython-311.pyc,,
phonenumbers/__pycache__/shortnumberinfo.cpython-311.pyc,,
phonenumbers/__pycache__/timezone.cpython-311.pyc,,
phonenumbers/__pycache__/unicode_util.cpython-311.pyc,,
phonenumbers/__pycache__/util.cpython-311.pyc,,
phonenumbers/asyoutypeformatter.py,sha256=TV_SOoU6oP3uFk0TedK3aWyYz9teSDKR3Gwz0ks6wf0,33119
phonenumbers/asyoutypeformatter.pyi,sha256=Bk66pd_UXZu0mUM96ymSWWRKFdMJaGqJTUozvFEmjLk,2721
phonenumbers/carrier.py,sha256=QII4ydTz_CqQzq8OVviDjCiWzXSxSoeAE_5e9CZy8mY,6353
phonenumbers/carrier.pyi,sha256=PO6FH4dRWUPO3zbPeAfjNfaGNzXhD1_ZC2VM8G-1cDY,454
phonenumbers/carrierdata/__init__.py,sha256=xeLV8YeMTQk47uRP3iFjbB8y63NDiv5UvvUONruPysw,936
phonenumbers/carrierdata/__init__.pyi,sha256=adDU9Lxip6D4cyRDEEpEP8dg85oCku1ciDnO1VMu6eg,68
phonenumbers/carrierdata/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/carrierdata/__pycache__/data0.cpython-311.pyc,,
phonenumbers/carrierdata/__pycache__/data1.cpython-311.pyc,,
phonenumbers/carrierdata/__pycache__/data2.cpython-311.pyc,,
phonenumbers/carrierdata/data0.py,sha256=WVtwwkR84VASJzau9AK4weM1IQkZRE3xgTlGR1e1Cnk,310294
phonenumbers/carrierdata/data1.py,sha256=soNqSpa5-ur6gtd3t8zyTesm-6HvnYKsU0j1H90Y14I,354906
phonenumbers/carrierdata/data2.py,sha256=dCYTRYgxLX39VQ90LDZbjlmZWXyx63KMQTINiftmEKo,321634
phonenumbers/data/__init__.py,sha256=Q_T6kkHJxS8Zi2n1qZSHaTgP1vWSQrlhZ1c2ApBtSIQ,10227
phonenumbers/data/__init__.pyi,sha256=AYkBu5sCoP7WB7eY9VG-QGxOGjnqW52I4gWMhnXx3rU,2481
phonenumbers/data/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_255.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_27.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_30.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_31.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_34.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_350.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_351.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_352.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_358.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_359.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_36.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_372.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_373.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_380.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_381.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_385.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_39.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_43.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_44.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_49.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_505.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_506.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_52.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_54.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_55.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_58.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_595.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_61.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_62.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_64.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_66.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_675.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_676.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_679.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_7.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_81.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_84.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_855.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_856.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_90.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_91.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_94.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_95.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_971.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_972.cpython-311.pyc,,
phonenumbers/data/__pycache__/alt_format_995.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_800.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_808.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_870.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_878.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_881.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_882.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_883.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_888.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_979.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AX.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_AZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BB.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BJ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BQ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_BZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CV.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CX.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_CZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DJ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_DZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_EC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_EE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_EG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_EH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ER.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ES.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ET.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FJ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_FR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GB.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GP.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GQ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_GY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_HK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_HN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_HR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_HT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_HU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ID.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IQ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_IT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_JE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_JM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_JO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_JP.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KP.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_KZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LB.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LV.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_LY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ME.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ML.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MP.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MQ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MV.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MX.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_MZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NP.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_NZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_OM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_PY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_QA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_RE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_RO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_RS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_RU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_RW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SB.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SJ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ST.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SV.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SX.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_SZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TD.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TH.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TJ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TL.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TO.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TR.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TV.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TW.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_TZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_UA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_UG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_US.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_UY.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_UZ.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VC.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VG.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VI.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VN.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_VU.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_WF.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_WS.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_XK.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_YE.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_YT.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ZA.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ZM.cpython-311.pyc,,
phonenumbers/data/__pycache__/region_ZW.cpython-311.pyc,,
phonenumbers/data/alt_format_255.py,sha256=Fv7jiUxvRndymgIELAOxCEfjBs8kZhQg5Gfri8ODP04,337
phonenumbers/data/alt_format_27.py,sha256=IsbQTsD9yZQ-Ts6OhdtE0RuOwzbVRgUzj2GE8r_umHo,233
phonenumbers/data/alt_format_30.py,sha256=eWm4_rjn0Ek_7_SBre1SYdUMvXNYRZBzKNJ2iEf2zGw,228
phonenumbers/data/alt_format_31.py,sha256=bBOWa_HS4UyZiZcrTan9RV2NnHBmcy4jpFv_oqU31EM,430
phonenumbers/data/alt_format_34.py,sha256=nZQm_vKAhmAKHfTopQUYHS-suUtMnEKUvNCpdUvzbeQ,389
phonenumbers/data/alt_format_350.py,sha256=8AhMCZDuUxTIv41AXYJ8MpxbdzuHpqeHkU0hgVY4QLA,217
phonenumbers/data/alt_format_351.py,sha256=1Co-yQwqLnLe4j5Itb5dPSnXFwMSZl04IO7Y3yESVoo,455
phonenumbers/data/alt_format_352.py,sha256=HbLB5T28rADBoideajJJEPN5VFRayaF9FzFN7kIP3bY,246
phonenumbers/data/alt_format_358.py,sha256=S4mgBK-n-SR-oEKbGZx2mQOQupoCxwqR1QuMU-JRAOQ,521
phonenumbers/data/alt_format_359.py,sha256=9Tf4kqx9W4dR3kNMcNgTtqys6Nv6Q5BhtGbUrtmYTfc,460
phonenumbers/data/alt_format_36.py,sha256=sni9bxLXHWXZ3XEw68qVRhkzrkJvWZBk20HaiU-GJig,296
phonenumbers/data/alt_format_372.py,sha256=K8ljl1O-TOtzv0bvGebowp9NYWGRHahXioubc0-DiJw,440
phonenumbers/data/alt_format_373.py,sha256=I1x1id8g88EWlZ9iG3Iz56FO0Xz_rp6IisGKdcaON1c,353
phonenumbers/data/alt_format_380.py,sha256=9TobH9kB-ECOvRmTxIYS3e-dMxTmtZYUIPHv4CduvqA,527
phonenumbers/data/alt_format_381.py,sha256=Ya20TCubWxs1CiMWBFZm6EKaFNP1OIdlUcfeKw1rTdI,532
phonenumbers/data/alt_format_385.py,sha256=wvjHhq17cKYtTT2Xv6jAvmZVDN1A4aqoWeOd6NgeRIo,549
phonenumbers/data/alt_format_39.py,sha256=qlH24baC_CR_lQNGNdSE16OPfp5SpMPVyys-kXeiUZ0,399
phonenumbers/data/alt_format_43.py,sha256=FMdemOXXGxjd0IW67y8h_MBRO9fEoQZe_LLa1uheSa4,1778
phonenumbers/data/alt_format_44.py,sha256=n6Fe2aAEDtEYfinx15F92t_cH2hvrzAvxYi_9IHRUk4,730
phonenumbers/data/alt_format_49.py,sha256=21xv4wZh9oc6obRMCuFLACIoD-n7YQF9Tfg0RHk7f4E,14642
phonenumbers/data/alt_format_505.py,sha256=gxAFiE6tzqj4JSqj7Upo4iS4emlw23JESAhDZ2Exd88,226
phonenumbers/data/alt_format_506.py,sha256=w4c0pIU9S3SqpfubcdSKmsBAYL3PPW6lJT9pLKsY35c,199
phonenumbers/data/alt_format_52.py,sha256=36Pi-NM877jFsXBDISHJISIRjVu5jPxXI4IaxoyMNmI,411
phonenumbers/data/alt_format_54.py,sha256=SgZbyjtsBl0TrINVWA_Mu-FjiNC3Sk_ZoG6Z2MGR-C0,624
phonenumbers/data/alt_format_55.py,sha256=3f_8uge1aHayO08iLnfZJVWSvS3nmb5cugBd8k-lGw8,248
phonenumbers/data/alt_format_58.py,sha256=GBxIPDAWN52zAGAcyRFBh5uOr35hggRbaD4nO4AkMh4,197
phonenumbers/data/alt_format_595.py,sha256=6SqoNnypheVADJe4gL_q53xbBy3E5i54WkkkyfN8aNs,556
phonenumbers/data/alt_format_61.py,sha256=9DDT5FO_w5FRBeR-X2UOu59DaNozzdrbeN9kp6PQhwI,442
phonenumbers/data/alt_format_62.py,sha256=jBYq1GjDQlWJU66mA9pKuOHbXvmRqdvJSXyrQthSHkw,806
phonenumbers/data/alt_format_64.py,sha256=2v1EU8YJVFMdzfemF3y3dB8xgglWaJ15qSSJ9MQXRSU,342
phonenumbers/data/alt_format_66.py,sha256=9H6jN66E8LGeqQYw8is5VPfjS5TcrQVizm5im_p8zAY,215
phonenumbers/data/alt_format_675.py,sha256=0-rhfMr1WfC3n3gQew2xBf3KuJPTKlfq_IsNQ52FMWA,229
phonenumbers/data/alt_format_676.py,sha256=9hHUvxG0QBDvRd87dZd7WIM036u80u0ziEaszYLDgHg,221
phonenumbers/data/alt_format_679.py,sha256=79Tg_VKJ0wWh2c4Kdh-M638pis58GaSkqEB0Lwj5UZ4,229
phonenumbers/data/alt_format_7.py,sha256=DfVYEpYst5enYkyJoLfik2mydYh1_UlkEwb4z36CaG8,1075
phonenumbers/data/alt_format_81.py,sha256=ecgM32VDBK5Qns01aDJ7MBlvwkPn77rqwnXR5S-fL64,479
phonenumbers/data/alt_format_84.py,sha256=DevNzCVzpEOpMqSdUhi2pZETJ_2V96AyeVXvLCvbPxE,447
phonenumbers/data/alt_format_855.py,sha256=PPZyPrYclXbBXYPuDNpk4PmM8XlHzFGJXP7acqp-Ook,213
phonenumbers/data/alt_format_856.py,sha256=ZtWxVtnOomy7HG7klMP11QefkWo8lna8WJXKK6J39ZQ,331
phonenumbers/data/alt_format_90.py,sha256=Cxp2AEY23oZ8vbQY_Nnx59zBZIrtmRsFmFu8an_ZWPA,371
phonenumbers/data/alt_format_91.py,sha256=MXcdTnQQifmgmnZy-RVRBn5xZ1bsyZHT25xcoPWlwws,2131
phonenumbers/data/alt_format_94.py,sha256=BspiIqjcpU4RXHMDC87r2mDyB-WIPHoPSNH5c-rm40Y,428
phonenumbers/data/alt_format_95.py,sha256=sjaCdwlv5y5SuVUf2tX8GIM-BDJPb4S4ehcGCsC6GyY,225
phonenumbers/data/alt_format_971.py,sha256=Rju2XkzAT4tRxa6qyrMySAqPW0JJRJBgky_7ZQj64uU,229
phonenumbers/data/alt_format_972.py,sha256=CCEg3ek-j9tiPYrr-0QLTdNgWzLT_fxHKqfkswTXrEQ,244
phonenumbers/data/alt_format_995.py,sha256=I96_xU3tMWkXYvcyzIx5zFYd5asHjYQIxSfRaCeIzks,784
phonenumbers/data/region_800.py,sha256=zdpGIOIdCo3ObezTBFNfTPg9-QOOVt407iFPCSWIfOM,571
phonenumbers/data/region_808.py,sha256=AQhT6di67Y8Fc8JSzYQHMBx3uXs8qywTEOj4cr5Kk9U,555
phonenumbers/data/region_870.py,sha256=e5lJ8bUTSWkaZuQl7T9S-hmrOAE80afrucfRamhfjWo,718
phonenumbers/data/region_878.py,sha256=mk2NTf8A-kgwVVwoOAMqxgy0sZd2zAltyTaNmAK2dpg,558
phonenumbers/data/region_881.py,sha256=Hv_Zn-3NipxIlCpcNwQeT0dNC_i0yQ1i52MA18-1Y6U,700
phonenumbers/data/region_882.py,sha256=N_cpoYVX2sjEFld20nAHPTWqs65leVXPUnDqDNVrBAE,1916
phonenumbers/data/region_883.py,sha256=qYP4yelHgCyTmB_31Ja1xUo2oayuIdC9UAkCtzzvqTY,1213
phonenumbers/data/region_888.py,sha256=Vk8sTNHL1kiLFyrInqWXE7G8aBwuqL3s8D--wDRDFQI,522
phonenumbers/data/region_979.py,sha256=WZVbsSzdbw84uJec6Kc-muHllFsxRXz6UWt2l6KwRos,635
phonenumbers/data/region_AC.py,sha256=UdRGNHhSgRe7jSa8BV576mMhD2gqUubq7dTgY5NXB3I,688
phonenumbers/data/region_AD.py,sha256=TS3svGbvpU6GxWd37a-trOsPwCoFxGPQJ1hB8HQzxtA,1255
phonenumbers/data/region_AE.py,sha256=M2-vqRFfifMnQWFXpLfLsNtS6qUIn3uosJgO-sZUNuU,1783
phonenumbers/data/region_AF.py,sha256=bffOlHJy-GxPESW12IlNBITL6bLRwN8GsS3wc30Rx30,1140
phonenumbers/data/region_AG.py,sha256=2cxEQTpAI13yzHzRma1kMkNTs8D5LTm66_OO8J1U6PY,1865
phonenumbers/data/region_AI.py,sha256=Mj-uiU-wN6z7AmDC49o7xWrt_rn-e1nX7vm7tPKAEPA,1707
phonenumbers/data/region_AL.py,sha256=HCue661v0kiDNj6C-fQkkEfPZu_dW0OQ1bGvFWRx5Us,2135
phonenumbers/data/region_AM.py,sha256=oRNKebfRbT-wSBT2QTzdnIz4YCNdR9vfKw-hTKSwei8,1964
phonenumbers/data/region_AO.py,sha256=_m8X6Y5Mbe9YP2LJfqOv5c4h37y8fOjdmaa7JGId1b0,706
phonenumbers/data/region_AR.py,sha256=W8dvv3ri4myIdvPQxIHq3buy7JapxxUsQCk1swr83Ko,10911
phonenumbers/data/region_AS.py,sha256=dfcOV9Z5JD_QtAd5pzuiIrl-Hv1zvyn7nEcdPujzJ2g,1512
phonenumbers/data/region_AT.py,sha256=LXZjoiLQVVYfrXtVB9YSNy4AfywqwgOAXS1RmvOnmAs,4052
phonenumbers/data/region_AU.py,sha256=KRIR8PgnrzPEOoyR2s3RQjTC3liMPrGcSq6lnDX5azI,4212
phonenumbers/data/region_AW.py,sha256=yfJ0UNoweUOJPoSwqM3qLcGum_U9vdRjmYvl4OAxbyM,1126
phonenumbers/data/region_AX.py,sha256=sapmJFilYxzl2Lbp2dum0ZKRmUhGMPzjKg-uuLly0x8,1398
phonenumbers/data/region_AZ.py,sha256=yz6aCLuxWPJnCbRe3Mjocyc1wqHE9iTfvVlM-Sy-qOo,2265
phonenumbers/data/region_BA.py,sha256=GYeBq7ovieMYM5TgMhig3s7wtA2RQkYZfPOFGurlt-g,2404
phonenumbers/data/region_BB.py,sha256=ZNRUfZxJG1T-vFOXCvjLJ6SZpeSHUoT0_r8h4dhQoOI,2130
phonenumbers/data/region_BD.py,sha256=-b5kDd9PFPs7BDzIsL7KlFGXkplPi0mYs_icFQpNaww,3088
phonenumbers/data/region_BE.py,sha256=EeWy3VDibf-syVlqi_Zhx3lWDxkDdvg1_d1FZVII-ig,1981
phonenumbers/data/region_BF.py,sha256=Cn5sUHuafITqD2GEHTzspUaSXXZ65jjQtTmXQLTDWag,801
phonenumbers/data/region_BG.py,sha256=9lVVyY2NYRBbXvhOl3S0QhwOtb-f0Nk8FS8t14KEokg,3603
phonenumbers/data/region_BH.py,sha256=Z2wDX1CBK0WNqaRc8eRw2q_k6eBWptJ3eXBSnxmBNis,1288
phonenumbers/data/region_BI.py,sha256=KsSjIb6RJ_ShZM1Duxg5Ax3GZhv-HFLeY4OQx0Y1QmI,719
phonenumbers/data/region_BJ.py,sha256=TjZtiKwHcjtO-x8QKnOaOmTiOLACc9vA5ZIdOp8Ydhc,1216
phonenumbers/data/region_BL.py,sha256=Qub0rlsjbwzltE2ZwPhrrFfiGQbpdenWXGGmaiZyCPw,1073
phonenumbers/data/region_BM.py,sha256=RNkSk13RsdevQNPdpH9M2Qneepz3k306EE-EXI8u7To,1539
phonenumbers/data/region_BN.py,sha256=daeanS0Ba9i9cgKtCDPRBClvsH_-LiRiuwofuEYRm28,837
phonenumbers/data/region_BO.py,sha256=YAaGeg1ze9zWdXb0Jj9TIbZqjn0mUMnUayxin_hwIbY,1766
phonenumbers/data/region_BQ.py,sha256=NVfFNAMPqdXehEekKbSt4_l-3jYfaKgnThwLLh6W2cI,698
phonenumbers/data/region_BR.py,sha256=7PlmjwFw5mNeELkFoOrum14K_ddLAwLEz7XR7cFSrIA,3420
phonenumbers/data/region_BS.py,sha256=O5BdD8jMp8MMVygn8p3QmGHEENfoLHFHeSLxsZNkfHs,1935
phonenumbers/data/region_BT.py,sha256=z0aciHk731UVKUqiq2J7uBZJ8hTuZNUxKZWhYE_vQmQ,1307
phonenumbers/data/region_BW.py,sha256=EzhlS6SMHb5rjailQ6rbh3r94_s2wKF0Pa8riaBa-3Q,1721
phonenumbers/data/region_BY.py,sha256=Q6OXVb9Fsx2ZZa-o5Z-Iljw89ClghPo77AmZC7vohZ8,2771
phonenumbers/data/region_BZ.py,sha256=tr3w7geTiZJNrU5oTn52YOcUHs9Pjtt9HMCs7gSZfTo,991
phonenumbers/data/region_CA.py,sha256=4c8VJQkiE16fdklQdNDZHLYlovx5AU4lUngACNnBAk8,2000
phonenumbers/data/region_CC.py,sha256=6lTTpMwwWz7rlSZFX9-XdGSrhwZBQQCQHji5mLtej-0,1663
phonenumbers/data/region_CD.py,sha256=K_wezAy8Z-y88ZYjW-xBhya70JCMaZi3JheUEujrm8Q,1641
phonenumbers/data/region_CF.py,sha256=Xi_Q_y-ab00pXIbpDDM70e0qhW4eAH3HTZhQ1MuanUA,829
phonenumbers/data/region_CG.py,sha256=OQqkqJH4FVw_SXVp4ZNOMQIn9sOcV4h3evDz_hvrHEw,995
phonenumbers/data/region_CH.py,sha256=2v12yMsnnFeIjp5wMbZ78oIxaMlC7PvFO00b3GgU6hc,2083
phonenumbers/data/region_CI.py,sha256=TXUGLpKl391HZ0M4TKxFFoulQnyfOQDXN6H9vZorXPs,888
phonenumbers/data/region_CK.py,sha256=aGe6Jm6u_nM2ZxK4LbFEk-_EAPFUUuX_O7s9NwwDZgA,683
phonenumbers/data/region_CL.py,sha256=Hm7ih6wbZizC8YNVIMV4sqq_9aSDPOiFFROpO2uhLRI,4062
phonenumbers/data/region_CM.py,sha256=iegomWL4ABp3nfBL8bl-wfm3gaWYrD5ZobEGcVNkrsM,1027
phonenumbers/data/region_CN.py,sha256=JkJGUTn1EL6pcGE0tDLlngOer-9xyQKFld9A_-F28XQ,16721
phonenumbers/data/region_CO.py,sha256=d-uc5tfpG30BCcsCu06nqALNH8pet4aoifo1J9il7kU,2335
phonenumbers/data/region_CR.py,sha256=vcaTzPpEJEBSqt4UDnrZTGsMxgyb1GLneRtDC6MYu_k,1458
phonenumbers/data/region_CU.py,sha256=eUyI4W1Qh4F49HjnCcwaoMrlzsCHdwtbgK9SAI-owvQ,1650
phonenumbers/data/region_CV.py,sha256=kbwySK-wKCAMX1IKqu-kqcimigaIba0MvPXt1vbDfNg,995
phonenumbers/data/region_CW.py,sha256=A9bHni6Z4MqtPESVn-x_iRE7gEYOGNZhVsS7sLw5wpQ,1235
phonenumbers/data/region_CX.py,sha256=U-f9xFptIz_knbixn8GQzEm08UAXJtS_LL6IGvd76qU,1681
phonenumbers/data/region_CY.py,sha256=GjlN6XvBXiC_nx4Vip9x0Hik8KJW1N3FFhacmIk-owA,1338
phonenumbers/data/region_CZ.py,sha256=f3ijVLAnkWPTCawyQ2kMlC3ccacjTIaFnbUV4NhLTV0,2099
phonenumbers/data/region_DE.py,sha256=oIIk3Z9_4kWo_SkAxOyr2X8iw8ucpfMNzx3IUFBddfo,6120
phonenumbers/data/region_DJ.py,sha256=7Jl9glwTEEGlXJzqjgZGOuLHrl1bt43Y7LOG5rV--ZI,705
phonenumbers/data/region_DK.py,sha256=XkTk10eWzea9mPKjPjLUQdgCvCXDcvSxeG1G32WKDrI,1392
phonenumbers/data/region_DM.py,sha256=xZJj-URfBsxonLy-xpLqol_NMP2YDTOL2IVAHnrwIAU,1574
phonenumbers/data/region_DO.py,sha256=g-VJnWPpFo8uqEKr7c6c5mNNijlNu2xH_DcPjgOOUHQ,1801
phonenumbers/data/region_DZ.py,sha256=LPpZfJnVP6wHAG1AQTT0uh9S2VYSH8T1N0Tr9cjoBXQ,1725
phonenumbers/data/region_EC.py,sha256=J8KvG8n779bZ695dDacy5YfbQUSGf7jWgpHA_yGPZMs,1936
phonenumbers/data/region_EE.py,sha256=KBQrhyA3Viw4eLDs_1AG4298JqyNu9vKrsyU4sX7yUQ,2086
phonenumbers/data/region_EG.py,sha256=_siklGhjGm7aSMcXj7SlvaGEp_mu6Cl7ZP2rYTEPkWM,1695
phonenumbers/data/region_EH.py,sha256=7wakhEBJlG_lBuZpRrWVg3KHGAFY8J7MZe0Z5YWEG9s,1096
phonenumbers/data/region_ER.py,sha256=1r2MWw9UuUSb8UUoLBa8_ab2L10AFiOzuWFCLVvV2u4,892
phonenumbers/data/region_ES.py,sha256=tA0-YGDSSU_OX241VDOIZn4yXCmgtXDLfVOXUMFILFw,2120
phonenumbers/data/region_ET.py,sha256=_xkSG3cBcf-774bvVhItHmMs6QU8ExblzlWIeogul0o,1591
phonenumbers/data/region_FI.py,sha256=K6zgmEQNEaW33Zif_W3G2xFHblCP8Os7HI8GMnumdK4,3365
phonenumbers/data/region_FJ.py,sha256=-BJI_TXDHX9oW09SqpHQ8_-IFiR7bS6vTG91igdQTvY,1034
phonenumbers/data/region_FK.py,sha256=3JCQ7G-8GGLmX5Ivy_M2T_tie9WV1mYuqs14R2nkGNI,547
phonenumbers/data/region_FM.py,sha256=7BBmpsBaqWdxWPN5D5oRAe9KgNE3NLIByqunIlVARI4,830
phonenumbers/data/region_FO.py,sha256=JZXGZ4gW4Bq2vOjd--KuBtoc10BRGr0vreY_1ziXIM0,1178
phonenumbers/data/region_FR.py,sha256=R6e_PPBrreexHs_EAttN6TqppkjDre77ldOCkWlvQo4,2261
phonenumbers/data/region_GA.py,sha256=kPbEi1iryqBIxKzDanzlbxCGTru8zWBYwRFENJKBe9w,1232
phonenumbers/data/region_GB.py,sha256=qy9NyF2OlinnIvqV8DUHr9fhQNbADk7n0AEkH4ggYLY,4795
phonenumbers/data/region_GD.py,sha256=QPcS8jlemr8q5t_bADpOmhQmb2dDv8MvO6gO-fF26sE,1640
phonenumbers/data/region_GE.py,sha256=iRabc3qIAQ2NeJzZi3a9WzWQ6Kr-lCztR_xP1bbKPEQ,2287
phonenumbers/data/region_GF.py,sha256=A_ZB0AGI3ujzIsk8Mo8pzUyH1dKkpkT2tJs7p5AR00M,1368
phonenumbers/data/region_GG.py,sha256=4KT3eyDueFut--b_6THzmp-G-KXMTMsA01f531SK8-s,1697
phonenumbers/data/region_GH.py,sha256=RN1v9hjGBwBKiBRygZS9WoOnMoLD2a-DiA2lNKeizSc,1830
phonenumbers/data/region_GI.py,sha256=Mzk2UC2_H_HMslHGiUOyqf_gUpWOF_lrN4KDQmNs1QA,819
phonenumbers/data/region_GL.py,sha256=o-XlEvaeoAyWMWoNKMi_keknmV8HlwHsIXRwkpMjQ3E,945
phonenumbers/data/region_GM.py,sha256=_4stL1muH4tFpuW4PvDLg8fKyt58AWSMXF_OlWPBLB4,794
phonenumbers/data/region_GN.py,sha256=J7qnoO8eX0GbMHyY9PCm5dctT4WWvg-G7mHCHFvpZEg,1004
phonenumbers/data/region_GP.py,sha256=PyP0QxFRyqEAKhkEnTpd2A2MPPtFcBd3lMrvCbCBN64,1491
phonenumbers/data/region_GQ.py,sha256=Qbs80osy84xMC0uQRbLsqrwESX6ljUp2bOpyiYsIzA0,1104
phonenumbers/data/region_GR.py,sha256=zoLr1LLFKRHn-P44OrwWZ2E_BVyyP488zI20i2R9abg,2163
phonenumbers/data/region_GT.py,sha256=9rqH0ZeYLfFjY3e0W_z2HtnWklcfSMeKLJ5bmSRXX8o,1071
phonenumbers/data/region_GU.py,sha256=-IYfCeul22UfgeqOEP4OIjn6P1Z44PK_kWcZ7E_gLoM,1880
phonenumbers/data/region_GW.py,sha256=KfdpiRcOxl_nNQ_CQmvVUD4DdulD1QrCWyECV_V3myg,910
phonenumbers/data/region_GY.py,sha256=Ka9_tQ83YdH_n8ZjC_YLoCWFA31S7ksEGa3ntJZn8ZE,1173
phonenumbers/data/region_HK.py,sha256=2VH1mVqCx-WF70vf2qWhN98vkv0UpvdTAodVLV8niRs,2644
phonenumbers/data/region_HN.py,sha256=_mzH-tHm9i7-0LAPpxdDAlS5PlFeHeLd9iDE56UxCns,1448
phonenumbers/data/region_HR.py,sha256=sW2gIEJ4ysmqUniNFSwFQLWFeAmH3CsT1r0I4TnY5oM,2477
phonenumbers/data/region_HT.py,sha256=W-8PLzQRNWJeNrHadb0FjEmTfmL8qgJD1liGSzcz06s,976
phonenumbers/data/region_HU.py,sha256=AoKYFS-K81exYXVBs1qzoY51EdP0QRp4D9JGSlXLGBI,1949
phonenumbers/data/region_ID.py,sha256=QagLgDgTeBjGYNTtKj4UQP7dZJnpIdfau8Tb7gz0WFM,4580
phonenumbers/data/region_IE.py,sha256=30z7flZtfrd95exFSayPwwplahX2pelZKIwtomZqScU,3526
phonenumbers/data/region_IL.py,sha256=6Ca8pKlq568yNuBAPDbTzUDkzjy5bTg4oieX3TjfhYs,2918
phonenumbers/data/region_IM.py,sha256=9PyDwBwBUsN0v90i02BUlQHcaS886ELMarfqr2Ro21A,1592
phonenumbers/data/region_IN.py,sha256=BDXTpl8eb4h9oIdOgRPorBX2GP9KQQ-O8Yw3IIv7WQM,11730
phonenumbers/data/region_IO.py,sha256=WIEzOnYjDKeGulxCIYPX2PMFKcwVCfJ0Vt43yCFfHk4,651
phonenumbers/data/region_IQ.py,sha256=zgtozX_KP9Op7wQ46SaUH11WwPOMikMnZcJCvNsj1uc,1229
phonenumbers/data/region_IR.py,sha256=oaC5v3NbdFDCa6zyDNeYhe5JgyK9PNcxpUriScqTgw0,2121
phonenumbers/data/region_IS.py,sha256=M2Zcv5S3Rxt-nOKAcrHKhiuhl5hJVrQxivOqEF49yiw,1849
phonenumbers/data/region_IT.py,sha256=ntNusP6F5M5OlIyoyME2VlUdOsVNEKOY86gm5vY4bM4,4909
phonenumbers/data/region_JE.py,sha256=WojQ3WMm9n1rlRekCS0rG_F_PjvCpNz1ShwAsb2go54,1827
phonenumbers/data/region_JM.py,sha256=078bKQVNpjOuqPNf0KkkFwNXv-OM68AXDheCYvhwfEA,1796
phonenumbers/data/region_JO.py,sha256=lgb6hQXVxPpv6JD9A0-7Y7pjuskx-rWY1BvjHh5quqs,2480
phonenumbers/data/region_JP.py,sha256=FiTiNb9cow2REynNge0uUiYDfJOFAfVayPc4_pbp8gA,9641
phonenumbers/data/region_KE.py,sha256=SzR64maPp8joBQoq2KfWZ60BnB7XDUjKbJVrfnJN3eY,1534
phonenumbers/data/region_KG.py,sha256=vSmuvaTIAbKafueoAfKWFm6rD2i9PBZEnaVd2AVZfnY,1655
phonenumbers/data/region_KH.py,sha256=g1beD-ZjbnMh0w4EuZ7zbLsXd-8IKZhm0znKQVSUP_o,1528
phonenumbers/data/region_KI.py,sha256=Po9wt_BOzaOS3lta6qN18wS9sd8z2sfqr1_YiDLjQXk,1116
phonenumbers/data/region_KM.py,sha256=JHtbYpLgtD6yuiRn9bfMdIgonPL2Ztrab_6bJakNuX8,862
phonenumbers/data/region_KN.py,sha256=tiq1jTVOP6phTEorS4LJPkxmUHrLwcREdfYv_n61LeQ,1554
phonenumbers/data/region_KP.py,sha256=YaljX8PKi6QRdztDjO4B2TKvyGK4u-wERCkBWv3mfK0,1369
phonenumbers/data/region_KR.py,sha256=SBegyTCNNu3S0AAW5LxlOi890H6_ik-cDN5f-UVG-ck,4761
phonenumbers/data/region_KW.py,sha256=gVK76QxsO1QDlUanD_lF2jhXAowacg2Prhv7fsmlwLc,1320
phonenumbers/data/region_KY.py,sha256=3uTO9SiUhUeoyRx2H8PnGDWgIwr0fYq7nxBCGBYgJfE,1703
phonenumbers/data/region_KZ.py,sha256=M0mYRqqrnqSSmrTInRBlyMefB-4JnZEktNsWkt87oUo,1935
phonenumbers/data/region_LA.py,sha256=gAjoz_oNFjnKxQeOrXyZQz6xc6fFC2x8Uu_XHOi7gvk,1409
phonenumbers/data/region_LB.py,sha256=OlJg72tYKrepwn71UOCNxRfndHY8A9jbtMlfI9fRujs,1344
phonenumbers/data/region_LC.py,sha256=rEG3lYQ3nrA2Ogulv0Gs3348eaUTKg-eJnfqtorPPGI,1613
phonenumbers/data/region_LI.py,sha256=Qu3BYWdY7ABnwKBlKgZWcM3ZP7eiWFVAQaDblW100O8,1995
phonenumbers/data/region_LK.py,sha256=5_nvE_Q0_FmCUI8sALegJddsIvSPxBzaLHc0cAVTfuE,1228
phonenumbers/data/region_LR.py,sha256=76Ao-nZ0ny_MDtqpXQOC_27TbbSnm9PotSOjqEaggGc,1323
phonenumbers/data/region_LS.py,sha256=YK0BScReQSRTzlEAiGokWgxONTDkV5M4bpRVHhvYwk8,800
phonenumbers/data/region_LT.py,sha256=jQazHEKe_oS37bYKeGVsUVT7PwBvcQoiUJs4zOmGcLQ,2230
phonenumbers/data/region_LU.py,sha256=gwBA2PBcdD1dgzhzt1fTYWZu_88fc6MBgBYudvOow9E,3124
phonenumbers/data/region_LV.py,sha256=_rj9p_3XQTwyGo1hoRjfS46avinqAm2CB3C3s8ROb5Q,1153
phonenumbers/data/region_LY.py,sha256=ZWNuGWy293Nvx20wRujDIKUW1vbalPCKesUeJc6h-1M,1043
phonenumbers/data/region_MA.py,sha256=VnQj7vw5uQwhqCGNhMBE3vt1j-NzU2jVoxAAsDzhgOc,1882
phonenumbers/data/region_MC.py,sha256=O3g7BjQ_YuX35O8xAvXKmkfLYfmqyIPCSnZEtlLa4G0,1867
phonenumbers/data/region_MD.py,sha256=UrsCEphtNq7prfugtRmB-XOjWjAaZwxdfdwZLIctmNg,1772
phonenumbers/data/region_ME.py,sha256=7GMFu_Cwdb_nU-JbH19pleL3r8CkP4nhZ2AkDnaWz2s,1482
phonenumbers/data/region_MF.py,sha256=jyvGE7PWFzQLr6nbsC-maWQsqGjgax1fk8y0ax7_E84,1092
phonenumbers/data/region_MG.py,sha256=47cObe7a_3mrQ4lxsYrPgDg-iMVpF5jt0s50fHRorto,1098
phonenumbers/data/region_MH.py,sha256=YsS-pGuKV0QaEE2jjp84fYHTO34KisCbHv1QYjTyQKA,891
phonenumbers/data/region_MK.py,sha256=dq4flaDjGhxKufXJNbzTtXFNOW7pc6-FbzUC7U9JwgE,1904
phonenumbers/data/region_ML.py,sha256=Bhrsh1yjBALg1K3eNIEpU5onho---yNGZRa_3UmkKbY,1318
phonenumbers/data/region_MM.py,sha256=Obb0gMhq-KzWeld_98-91-Zp8UkXmEHGV_Fm57g7s3w,3754
phonenumbers/data/region_MN.py,sha256=iCFguuoSwK89gbYg4a8bv8YRw2XFIDCYdBTaxnvN98U,1821
phonenumbers/data/region_MO.py,sha256=u45Btop4A-vOaMF6pNi5fsOQpPbMhrfrfv78K_e1eJI,1018
phonenumbers/data/region_MP.py,sha256=MzHUCAfnO0RxJdse09EY5aoWzM9r1hwvmcsMsSRy5aU,1637
phonenumbers/data/region_MQ.py,sha256=QO1Z1aivu86nKB3U9EJZw6O_w2TbBe3USkjZJht0cGM,1547
phonenumbers/data/region_MR.py,sha256=fuhdhpGnluMFKmBbw2J-NC4d6QHgomUSQIjvs12QhZM,850
phonenumbers/data/region_MS.py,sha256=jaqUQCRH9erN0uDsPfaD6VduBSCECYZ-h5OHImBUBVY,1521
phonenumbers/data/region_MT.py,sha256=NFsgy8rApbpS4J5nU5gUyLLfOGlHJ8NYSg6DfO5hT0s,1485
phonenumbers/data/region_MU.py,sha256=90u5lSYWHKDKT3sbI-WPAg0jrVw-om32Yc_ACUvUsno,1534
phonenumbers/data/region_MV.py,sha256=xJTmRT8GmC8yHxsV50cZ6ypLu0XJu1RFZIiN3GTxvpY,1294
phonenumbers/data/region_MW.py,sha256=geNg21-SYnniwA0r3_X74mWgdJVuuaTsdmROPW83Awg,1163
phonenumbers/data/region_MX.py,sha256=KztVKFYTuPumfXXq5ZhqwBX43df0EvgtPPA8n_Dlflo,2692
phonenumbers/data/region_MY.py,sha256=JN-KE6TdsXgAUVWjykPyID-duoTGvXQlLXbRbKATFzs,2804
phonenumbers/data/region_MZ.py,sha256=wpVrgK_1jmjN-ISlYXUwLOZA4jmPdLu2h4NztktYZYU,951
phonenumbers/data/region_NA.py,sha256=MM5rJYn_6s_JN2rVNpmUexxAPL94HAFhN6YfqJ506zw,1959
phonenumbers/data/region_NC.py,sha256=T6sxWASlnyiAh22ekiRPnfo42SQabIiIlTldS70VDWU,1192
phonenumbers/data/region_NE.py,sha256=kBoQR9YJNVbbt9mwFhT3ydhmOTxmNMHoueVFcxS_T2o,1154
phonenumbers/data/region_NF.py,sha256=csTjXK24740XnMjs_nYVNWKrKJvvsDiG7S__90rP8XI,987
phonenumbers/data/region_NG.py,sha256=sIVlPR5A19QNsStD40SHZ5-ScvTtgIebllZzb7zlMOg,2135
phonenumbers/data/region_NI.py,sha256=6ToIm7nzyAogY38jZcjNWKQ71q9A7IRfJ1muFyAscFM,881
phonenumbers/data/region_NL.py,sha256=qofHX5kpOy3qa6YIev4GF1bHXbS0CbPpFS01MBLBaew,3817
phonenumbers/data/region_NO.py,sha256=VKSIhMKUxSv9fojXvX8iSlE1HlEhE2gaZ1zu-RDEqz4,1869
phonenumbers/data/region_NP.py,sha256=D1KueBJ5sv9ABAqCnc_zzqaivI36JZYsVSgmk4AQC5A,1855
phonenumbers/data/region_NR.py,sha256=nC1qAAp-1eIxp228QYLAbsLfRuo88_K2eocqYyaEXgc,703
phonenumbers/data/region_NU.py,sha256=sBnbfn2o6YQtVsJyIYXopOYofTWY90oYWk2P4rk-Bnk,684
phonenumbers/data/region_NZ.py,sha256=Ka1aAZInm41vSxUizvq6vIF3hOP-1BHLKpsMtsohNa8,2553
phonenumbers/data/region_OM.py,sha256=TJHmBBPHXbxKlxEDtPb2gtlxhQyrt3LA8i-emrkHEsk,1252
phonenumbers/data/region_PA.py,sha256=P3lD1bAtS1LmkXZbM7T6_uRqfWJef95hbdSpAm0PqB8,1736
phonenumbers/data/region_PE.py,sha256=a93lAxg09T8v1ws4rUk3IqXa7RXTk6XwdJHAFj0ncRk,2022
phonenumbers/data/region_PF.py,sha256=VfZgBMeVyuIQ8x9-GWRH04DsD5IO3Oht7_1wEIm_WjY,1413
phonenumbers/data/region_PG.py,sha256=ZeCiMuFAox8cgy6xC2n8PzUYfdHXW3-WfCgbcD7fCn4,1283
phonenumbers/data/region_PH.py,sha256=GoImIaUZgKASzq4BdlvmWXFKQo-9WiYLzO_UEWjgNIE,2428
phonenumbers/data/region_PK.py,sha256=60cFmTYwn-EWsl0TzaaVWQOLc3HAqybq6fzdgHVifGo,3444
phonenumbers/data/region_PL.py,sha256=sBicezGitMBGhgHK4UW40Zj-guT1shclKf4M71HFGoM,2573
phonenumbers/data/region_PM.py,sha256=aH5Gg6oU2Dohrv9Xp1qyRAD7zLwNn_YK0Zrbd9RRb9Q,1390
phonenumbers/data/region_PR.py,sha256=ovmK377INmsFhZ356i7DsVHygWPNUtsauMTX1i4NGPU,1449
phonenumbers/data/region_PS.py,sha256=U-U7y_l7UCJ1Havj-EjwEpsubLKv0wVtg-M2QEBTwFU,1411
phonenumbers/data/region_PT.py,sha256=XO5LHbG-hKr2BdF_O-qjDltN0qypW16y7PaoIIZfGto,2106
phonenumbers/data/region_PW.py,sha256=kyqu7qohA_5Jm3aTm-GAwamKCowBKk8ZDx8krOh94Ss,812
phonenumbers/data/region_PY.py,sha256=EoxmyNewfOJySjGHbZvM-fZlSen9uMTfYMU-Oxz0Hso,2683
phonenumbers/data/region_QA.py,sha256=X-T89_A-Iu_yZkGFeyeH3k5xHPDItI8D-nJJrcQf8ZM,1158
phonenumbers/data/region_RE.py,sha256=zPLD_YpfHNT7gurlT15D0vxwawGg_An7Ps54Seld8Mk,1508
phonenumbers/data/region_RO.py,sha256=MfgJbhbRUYRvgfKGZv5ztysfGUZAUryzPYlJSWU6LJg,1951
phonenumbers/data/region_RS.py,sha256=bded5SJjZbVcgGB2GD5MWuZuUbiC92YkXoIBLv12t-k,1704
phonenumbers/data/region_RU.py,sha256=nebTKAMIsQx083bz61kFq1W7AVHfbQ7mC1RAFwn_MPQ,4035
phonenumbers/data/region_RW.py,sha256=_V0S1N585v93aDvxbAUoY0NFI7dAUxxRTjjEMZdYLK0,1293
phonenumbers/data/region_SA.py,sha256=yBX8CXneXm4W6ThvHL7m3w2A8tif8nYN7SxgMlLqay0,1968
phonenumbers/data/region_SB.py,sha256=UUya6oWCoa3h6lf6UhiwfhFFNAMsadJhr3-d5tYLTpA,1075
phonenumbers/data/region_SC.py,sha256=HoZF6u-y9f7cLcRjpYBn06gr17VPCa7UCEhTeD8AC2A,1110
phonenumbers/data/region_SD.py,sha256=cAU061sVK5LKbTGiW03FXuadTex4Bk7q5fVyBRofBmU,808
phonenumbers/data/region_SE.py,sha256=8pQqwqmDdzWEREF1pUKrgMpSPbAZYlofAOIPwxrvY9U,6086
phonenumbers/data/region_SG.py,sha256=DNTHr_cxXN_auM9QMU-iFn1-1OQVXxsDCBS2fokE_7o,2301
phonenumbers/data/region_SH.py,sha256=hbrzkrUYvYuFlLe_Nv4fj8mR-xBqqlvIUdpfziXQICE,746
phonenumbers/data/region_SI.py,sha256=6M_P1If7Cz7d7qcaHP6aHwgD6XFwqmMcU1rx7kQI0F0,1944
phonenumbers/data/region_SJ.py,sha256=fRAe2VSw58ENJiSeZsL07WQiGwEpGy4PsVDMw1liZhg,1496
phonenumbers/data/region_SK.py,sha256=1zDMkC0Qojfx_SSfGouOwO3vflYlHTbgr7M_B8E_B7w,3329
phonenumbers/data/region_SL.py,sha256=j3L_iYb8bRDRX1rQ5HG7HietJIz6fdgWvNHfxvs417c,888
phonenumbers/data/region_SM.py,sha256=0xs65C64HUvX2KHDf0itvCxmqrdP6sT_ScusiV981FE,1553
phonenumbers/data/region_SN.py,sha256=RvVHpBbMF-xKu2R4RLtp8yiMPlpWyP8apdcHJ4i6Q70,1456
phonenumbers/data/region_SO.py,sha256=bcxw0ICJb5FywgIV8ExNM_ilOXxT1yoBhXrkFtvkklI,1515
phonenumbers/data/region_SR.py,sha256=I5jE-QcpZvcxHm8hANfCJDkci4r0XTJSjjU-sud8mvw,1053
phonenumbers/data/region_SS.py,sha256=gzdYg5YtjWlUVD7bPuWmxrS8sAz806QXo6SA0QIl6_g,792
phonenumbers/data/region_ST.py,sha256=tIdtgy8r0QXi3JHVTjgx_hW2MEIdayrNCtI4-9lQG6E,696
phonenumbers/data/region_SV.py,sha256=YApbpJAWL07p03s0SwuK-puKhs8SHQpkZ7El-DVZBIA,1262
phonenumbers/data/region_SX.py,sha256=TAKB6aHvH37TBC5NlvhNOwCU2aE3-E73jcQa2vto5xk,1544
phonenumbers/data/region_SY.py,sha256=sVOzRglJ2OU3TU_34zINRS0_FzB1E7BJj6MDlUPdpUI,1202
phonenumbers/data/region_SZ.py,sha256=rTzwKaVDe3E2Oz7Tz9J-w7hVsYELjZ_HKSx5F91TmZM,1252
phonenumbers/data/region_TA.py,sha256=k9y-2eYK7E8WpFBQgotMnaDadrH4S1gN8kuSfZpqwVI,449
phonenumbers/data/region_TC.py,sha256=7NfemKCkFOd7L0kNR35wk6uzEyZUpVATZBv9DPpI6aE,1712
phonenumbers/data/region_TD.py,sha256=xQC9MuarDQ6-PcYGjS7tvcvBzyASWjm59K5mAXUu3wc,785
phonenumbers/data/region_TG.py,sha256=i1pmUqKenaqE_LnCdgUeKlmxambLA27_B4-XYeWYOIY,737
phonenumbers/data/region_TH.py,sha256=WtRkxunWuMNhLULuBvPFLx0Q8tefhE0D4x4ocNMvbNo,1585
phonenumbers/data/region_TJ.py,sha256=tjEog8_wLXFsB7hqt593nYghXHYSHl-cycaE63XWPHk,1328
phonenumbers/data/region_TK.py,sha256=lQwQY88V-yrDt0-sh_aGuuAsHwtiEDNQVbab7tw5dow,590
phonenumbers/data/region_TL.py,sha256=7x4Z9YJmqH8Uhq0MjhqDQfwQ8cG0fHQVh_xZYGtMthM,1167
phonenumbers/data/region_TM.py,sha256=Grf6KWK8jjfkeKMkMHxK24TbzfaxAssxShVq_yPAAUY,1262
phonenumbers/data/region_TN.py,sha256=qF0qOWjXXiwEtrgsWsbLMgDGv-pqOuSFTRGp3zSrzuk,1133
phonenumbers/data/region_TO.py,sha256=AsdkStBFn9bBKpOtq1W8gt6LoH5sqyORh9qSdmR-Npw,1275
phonenumbers/data/region_TR.py,sha256=2x8Jucam2i9vbZB5htAfrRf-os5G9HcaC-0o0_enqzI,3421
phonenumbers/data/region_TT.py,sha256=um9UVZ-XCgLn_YyNemzLVL9l9D4EAOcimEFpniCv1yU,1811
phonenumbers/data/region_TV.py,sha256=SJTKJA2SRU08KuImqpb4G1u95RbQpBa1ISCjs7vLv1s,885
phonenumbers/data/region_TW.py,sha256=oWAODWDS7grfIbAEhvTAIHrUPkPLD2hQcR7nQBof9Ds,2798
phonenumbers/data/region_TZ.py,sha256=er6xK09PkM3BEanVmItvCUKSZ6Y0wbap1EZg1POCsis,1833
phonenumbers/data/region_UA.py,sha256=bPG_Cny22BhL5lqmttx9_tbog-pZrCCz8N-kKDUtpng,2139
phonenumbers/data/region_UG.py,sha256=VvFeFECGzWIkcXV-8BM1BVkRukcgPu0ya3IWyEUS2KA,1581
phonenumbers/data/region_US.py,sha256=HyydhjO-9omwu5l9eK5EWkBA9h0Ll-YH33olZntR43w,3697
phonenumbers/data/region_UY.py,sha256=SMokws_ihqJRvwtBQFoKwqG6ptQfam_0wpDdY1NYals,1873
phonenumbers/data/region_UZ.py,sha256=vPXdLfBCJDhScWiEG49pDj_2BkhJMHupTMq17Gr5Nl0,2062
phonenumbers/data/region_VA.py,sha256=JBLR8w2i-KfuKAUl5ruVArym4HB6h73ACSc-gpg9GyA,1629
phonenumbers/data/region_VC.py,sha256=5-81GKceJZWW-y5thA23622KEUjJDcgyw6qwf9JcTNs,1773
phonenumbers/data/region_VE.py,sha256=JNAezrHhd4LhIXAbm3G9SwKry3I97WAc9CLhEEVw8IA,1373
phonenumbers/data/region_VG.py,sha256=Ip1E4-rqQW8S0GguqvGdw45zIOMhJ73LdxAb_i8TLUc,1601
phonenumbers/data/region_VI.py,sha256=qA73yK-4Wfx0eF-Gr8Bv9rp9kFncw0EEeI7YCrKn1UY,1725
phonenumbers/data/region_VN.py,sha256=hxQqDl-jQtsPiB6FNoQkXWTVqXgYxq5cfH5RKjEbITs,3678
phonenumbers/data/region_VU.py,sha256=sNpQXvAurG6HRoKyNzuerL8uorJCpfYlLyCbkdMTgho,1111
phonenumbers/data/region_WF.py,sha256=lJ0ZOoSowcW4KWdk0VbXAmExbEcdq1UfTreFy8EoobU,1178
phonenumbers/data/region_WS.py,sha256=AB8H7lsmUOTgJkv_qkfJhZSGtw1giy6F7yscSJdLhHQ,1068
phonenumbers/data/region_XK.py,sha256=4fbmId71MinNvhgcw8uR1Ks74RUSdgHTOVt1D1L8cWs,1554
phonenumbers/data/region_YE.py,sha256=t7MExZLb-cAOX0y9KnamZW8R1hHtDGF2g1mldqHa4lg,1113
phonenumbers/data/region_YT.py,sha256=oLyn2kAy4JWvaWXk__kX-qcwXUztGBx3O8n4qqc4lUQ,1000
phonenumbers/data/region_ZA.py,sha256=Dj0wID_MEkTrmRwu0zicQgxl4lkXt6b49sBICTmYBNY,2512
phonenumbers/data/region_ZM.py,sha256=NKym8GPYTVNVjrNM-sNrO8CQgQ3eMChMiFzuv9KFLIM,1589
phonenumbers/data/region_ZW.py,sha256=mXWdjDzOzQ7Gi54-G918GbikEJsqSgsS5kZ2TLOcAI0,3645
phonenumbers/geocoder.py,sha256=ZlqyAbMPmAxrRVSVcmEgl3HXv9KQfbueQWZPCRNEyfo,10945
phonenumbers/geocoder.pyi,sha256=atr8nQCRj-7-_XI2Imw0IqedtdeijBDMxsB9kxA-dE4,552
phonenumbers/geodata/__init__.py,sha256=bVl8if_AAUSbc9uF_YFXqiHs2KIUVJd9EVtI3MpbebQ,2255
phonenumbers/geodata/__init__.pyi,sha256=WBp66K_wlAxoL-3tTMgfwn3wXqbES10nsF--0fUEaY0,68
phonenumbers/geodata/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data0.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data1.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data10.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data11.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data12.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data13.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data14.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data15.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data16.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data17.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data18.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data19.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data2.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data20.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data21.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data22.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data23.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data24.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data25.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data26.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data27.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data28.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data3.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data4.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data5.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data6.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data7.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data8.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/data9.cpython-311.pyc,,
phonenumbers/geodata/__pycache__/locale.cpython-311.pyc,,
phonenumbers/geodata/data0.py,sha256=vw7WIXJUcuezjVqd3ma3ELSH3155bqYyv6d69PihtX4,354674
phonenumbers/geodata/data1.py,sha256=7HK5dXE_aMtKyTJfFsRdIdrfVTcoOy08UNcl_HtRE7A,357647
phonenumbers/geodata/data10.py,sha256=Q238hi0uY6cn7d6AhaC-JThbZXE_95obNLe_HpF7Xtc,325086
phonenumbers/geodata/data11.py,sha256=VsnAkgevPtGf3hSFlmuHAUJkO2lONkBcRy-ZZvQtb38,326147
phonenumbers/geodata/data12.py,sha256=i5dqXRxAv8GnvWtHAzNbqh3PnbS6dO8tK5_NdN8so48,331114
phonenumbers/geodata/data13.py,sha256=cjdOm_kQof5Zzc4nFnbwH56DLD_9iB2Jyf46Sk3YtJY,333069
phonenumbers/geodata/data14.py,sha256=GczWqPndUgV0xEW4l9V_Jgrhg_xymu7ruN_G04Wp3ns,673476
phonenumbers/geodata/data15.py,sha256=GTIARPKtIBuzSMtgZcC7ld0qL8wVoc8nvLRzvH0Q7y8,924151
phonenumbers/geodata/data16.py,sha256=ynuUzDeeA4c3mo9OtHirNTuJtYPBJAsBzGFMgcxJ_cQ,916719
phonenumbers/geodata/data17.py,sha256=T7ULjp1NoWc9YhqQhCbQsMVru0oQ-8EvT8aYsUY6PIs,911448
phonenumbers/geodata/data18.py,sha256=LmKhOVrXO7kX6R4m-RWk_EqR3f2MAmfEtUlLLXLaqcY,915421
phonenumbers/geodata/data19.py,sha256=cnuRWzFCzzZlcXrHbApiVPJmc3nVEhcqVrJg7s1j9Q0,922178
phonenumbers/geodata/data2.py,sha256=Om74ixW9gEY_93pbEZG6hMNLewg_XMwvJTKUUwmIncU,357888
phonenumbers/geodata/data20.py,sha256=yGBdmFtotr0l58dqzzC4IHCLOJKLnukhxayLfJDSuik,922207
phonenumbers/geodata/data21.py,sha256=d09sVadkQ5Q80_w_JuKBSAOu-HpYARO3GPSVWWaOylM,918703
phonenumbers/geodata/data22.py,sha256=kMMnZiJbLAPy8PUqk-FS2aP95wYbqYeeM1eyDogX2Ks,924731
phonenumbers/geodata/data23.py,sha256=WEmwBVesyTelmtOmE6DcKGVPBsF4-9n1OLS01fZUbA0,910874
phonenumbers/geodata/data24.py,sha256=HQfufnGwIuXXhlhjK5PCu_g4cMSvoFZWTbHuI4ohw0o,917439
phonenumbers/geodata/data25.py,sha256=hC5K5LqU9fsK9uqLq-6oTUL6CO5cFbZ8c0xKhZWwUkc,919161
phonenumbers/geodata/data26.py,sha256=54H0khvdstlBN9Gt54_PzjHR7QrpGo5qr7_WsOYxRhk,918154
phonenumbers/geodata/data27.py,sha256=ITOjjzmnTdgYC3_dtf_hoAtK3AuIDYL3ktooAP7EeqM,718745
phonenumbers/geodata/data28.py,sha256=VyctviY58yl2UuViReWSQ0mAogzCMIxnn6bL3zQuRPM,175704
phonenumbers/geodata/data3.py,sha256=AynRhXnZzXDvbHqV6j1TeZRju0DqP0AF0g4f5stFL0g,457351
phonenumbers/geodata/data4.py,sha256=RLVQjiYE2RAya4XfpXB8VV3RxAUu3YWW6GlCYTunczY,530746
phonenumbers/geodata/data5.py,sha256=cXXDm9XUeXIF4IV9Kc1wXwYQyfceWnF3Be8fSrs5Iak,869276
phonenumbers/geodata/data6.py,sha256=X7St2OcxmuNlBJKU4T21bmCHNqetmH66nmLmo8fF-sI,686612
phonenumbers/geodata/data7.py,sha256=HkJvko3BUWHEnCly42-STMnTckNJAtAGGKSso25dKU8,554665
phonenumbers/geodata/data8.py,sha256=aEyoUC131YEsJ1bSQCIZ8CF0TK3vOFwGThzwTxtRRSk,338665
phonenumbers/geodata/data9.py,sha256=xlDHVwvsVEbg0A_que54jVyUPv0OE76K9HKIbJUPJK0,328373
phonenumbers/geodata/locale.py,sha256=YfP58BZVBp0gKdSTKqZSutQjim-MmOw2dc0XxblrM-4,613284
phonenumbers/geodata/locale.pyi,sha256=oUE7i9XVtJmiPWJXxVgHGCUMgAbe7jb5lvUNSCBMGsA,39
phonenumbers/phonemetadata.py,sha256=ldvPNEZ5fg3b47WigXCmpnn26Xor5XlL0pJRHOc5Ij0,34110
phonenumbers/phonemetadata.pyi,sha256=3MZ32uSyAnOM7nixFcR_6QxHjvWv9U4RP1a6a7UYRjE,6311
phonenumbers/phonenumber.py,sha256=ruEZ4nZB8638DeJdU_GdkD3Wkqt-dUFhaa61kZmE9_U,13197
phonenumbers/phonenumber.pyi,sha256=4GTQ2pByhKUeXBhqus1Pecf4857SyzWW0mt0TojEVFI,1890
phonenumbers/phonenumbermatcher.py,sha256=rGwPG-qcRHIjGdz681D7wC3jdAK3ZAaLky30YHJuGko,37968
phonenumbers/phonenumbermatcher.pyi,sha256=0eNvzCeQFa9VE8St_tIhAJ3dUg_ueGQFfrfjCT_Y1d0,3656
phonenumbers/phonenumberutil.py,sha256=ITfUrC8UQ7L_Nakr_EAwXkQdIp2uwdptN8kn3-YZkVo,164494
phonenumbers/phonenumberutil.pyi,sha256=zirs8X-k3ewAp_GdxB4O99Z0tXO0p4Xmn9YzNjxgyWk,11104
phonenumbers/prefix.py,sha256=2zQ3AUBbVeOeECLkiWxc25kv8Tfadz1sA8Zr91XWFCg,3650
phonenumbers/prefix.pyi,sha256=TedaWEjdLLC2RDUiCNbX7lNFMn8ol5HUI9rBdT3NMNc,579
phonenumbers/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
phonenumbers/re_util.py,sha256=HaPtSecFblUE56lyakv7IXTgmkskt7Dw-ulVEZkJ7pg,1794
phonenumbers/re_util.pyi,sha256=BwEtl2x-mfwxTW2BboR-6wlxSK2VbfYN2mzrAC5NkIo,121
phonenumbers/shortdata/__init__.py,sha256=DkRPFIHf2RI1YDBR8O7F1kS7nYYy0Y1SeculBkPQNRE,2187
phonenumbers/shortdata/__init__.pyi,sha256=sTjbE6iQx3oDFj2P_NN3Xh5wiTLF_aLwSATFWOk3bmI,77
phonenumbers/shortdata/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AX.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_AZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BB.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BJ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BQ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_BZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CV.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CX.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_CZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DJ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_DZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_EC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_EE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_EG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_EH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ER.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ES.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ET.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FJ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_FR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GB.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GP.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_GY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_HK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_HN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_HR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_HT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_HU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ID.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IQ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_IT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_JE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_JM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_JO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_JP.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KP.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_KZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LB.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LV.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_LY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ME.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ML.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MP.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MQ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MV.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MX.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_MZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NP.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_NZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_OM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_PY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_QA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_RE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_RO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_RS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_RU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_RW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SB.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SJ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ST.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SV.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SX.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_SZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TD.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TH.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TJ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TL.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TO.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TR.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TV.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TW.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_TZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_UA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_UG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_US.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_UY.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_UZ.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VC.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VG.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VI.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VN.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_VU.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_WF.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_WS.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_XK.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_YE.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_YT.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ZA.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ZM.cpython-311.pyc,,
phonenumbers/shortdata/__pycache__/region_ZW.cpython-311.pyc,,
phonenumbers/shortdata/region_AC.py,sha256=AlzbZ9CD4k6sXzKV2ceSuf3UDcfRzZE41RXhzxM8nB8,675
phonenumbers/shortdata/region_AD.py,sha256=FieLY39VXhnzdzfmoU6HkY2PgKyMNPn028jl7qSmsmc,669
phonenumbers/shortdata/region_AE.py,sha256=JUdr3b1p5G1j0p-ziCVgV-knErECheLjdVZS_WbvrBc,809
phonenumbers/shortdata/region_AF.py,sha256=Kt6W9tBKVBwtgOofAw4-3_ELaJFWwjetUmE4oTRx7nk,946
phonenumbers/shortdata/region_AG.py,sha256=Jkv8svmEjmZJF4sSHZgPBflCwkY_LRVDEbFS9KfDYJ4,910
phonenumbers/shortdata/region_AI.py,sha256=mnxHDMK1UXQkdfm13yjZ7ng9nnzXtc8Y5op7LhXuWQU,897
phonenumbers/shortdata/region_AL.py,sha256=1M4bad_IYUfs-zN4vqaCE6M9O3bqD6vjOCAew6YKhck,1151
phonenumbers/shortdata/region_AM.py,sha256=q3sQmZtwFPveD3-gXOFUC_HFcMk-eNMIv9RvBU1Hr3E,937
phonenumbers/shortdata/region_AO.py,sha256=yaxpORWWXIW8UgRlD3v561ap_ffsB_11EMXjy6DZOZc,666
phonenumbers/shortdata/region_AR.py,sha256=LPyy46iXFr3FoxJ-rJ74gIiuRa-mVLjzswdluh0_gr0,1042
phonenumbers/shortdata/region_AS.py,sha256=xbJ6esCDNzmaBckwztot3f16UIv_TX3oBrfbDlV9TwI,809
phonenumbers/shortdata/region_AT.py,sha256=l28ilQ8sniQ7b4hWGOFl2xMK30bi2Eu6a-5_9WFbCWs,984
phonenumbers/shortdata/region_AU.py,sha256=sdaaoFzFRjkCxQRFh9qA0bI8PrlukILd4utO8NHPz94,1407
phonenumbers/shortdata/region_AW.py,sha256=a8iAHXeATtKLnn4eRlpVJ633zm-cNj9YFWn0N1jmRFo,904
phonenumbers/shortdata/region_AX.py,sha256=4dljZSIhcHr0_36eyVGmlbTPrfrcFtEDyeFqBKUp57Q,685
phonenumbers/shortdata/region_AZ.py,sha256=MJ6rHrl5Fa_KqkMofaoTEyJSREQHmd4JyfhQDL12rns,956
phonenumbers/shortdata/region_BA.py,sha256=R33OiBznpdv9tnIaCl1FVthG3oCUQUPpd-HwKIRrpe0,791
phonenumbers/shortdata/region_BB.py,sha256=lhK6iN5xKBPVznDT_2tAITPy7tcEESvuxJzuAaxEZEI,801
phonenumbers/shortdata/region_BD.py,sha256=Wx-9fYvFNcIOsUq7q57E6Zt7gRBN0fv5ip-tTaYXzbQ,1095
phonenumbers/shortdata/region_BE.py,sha256=Qpa0LL4el6uRafjvuLeU8wUah-071m4R5ySz4DYhbLs,1213
phonenumbers/shortdata/region_BF.py,sha256=wzOPr0-pHlq0Mz-2uiOat69GE2sFjhl4EwFmSHjOwJA,654
phonenumbers/shortdata/region_BG.py,sha256=yxTVCp1N2-iO1qo0NuaMcPNNNtn0Ujir_A_Q9c7JHkg,739
phonenumbers/shortdata/region_BH.py,sha256=cPE43jdYSigHSouQ6zcjyWVmjZbFXd9uDQV_3552Owc,1151
phonenumbers/shortdata/region_BI.py,sha256=P22Z6PGMWi73EN7xoWBn6OXa1vr7u4eTHumb_Q2yA5M,977
phonenumbers/shortdata/region_BJ.py,sha256=xtA1BiwaxuH_K9SCHsV5kkhFRASVd-3ImFktBbKXzEY,881
phonenumbers/shortdata/region_BL.py,sha256=NOCrKxQM9hW4qcftRk52YSm8evxGnjzePcZmg0vXFvE,645
phonenumbers/shortdata/region_BM.py,sha256=kYVA29Se2afjeTtJ0k05cyXNa-nWBi0eYSGcCXZc4M4,897
phonenumbers/shortdata/region_BN.py,sha256=zvHZMMHX_2-xU1oEEpaAsQ2hw8jKWEh8o0SuRD81fy4,666
phonenumbers/shortdata/region_BO.py,sha256=fAfpwh7RKdrnxl1_zQd8ZpisQDnHWt4-ArvabEFj4AQ,807
phonenumbers/shortdata/region_BQ.py,sha256=Okw_uo7mtJGQbMDo06Q0kyY1HLjj2Re9to_uKBRcWcI,898
phonenumbers/shortdata/region_BR.py,sha256=0B8WBoXmvbSB0fEmoa7fnYNVFz9sL91JfmAMEUxzkTs,1538
phonenumbers/shortdata/region_BS.py,sha256=V9ztmuaaxH-jS6UL7xYf3V-h0Mk2ARnaRJCSop7A5pw,677
phonenumbers/shortdata/region_BT.py,sha256=KMYc_Rua-ybYFKkMoyie08NAbHQAOcAAJzDvAWxi47Y,807
phonenumbers/shortdata/region_BW.py,sha256=7iwL2AZ6XHZNVewQLAMN2Qn7SgLGm_O8_njHpSh6KS0,965
phonenumbers/shortdata/region_BY.py,sha256=7QiHVqsCFZCMQFl68W3HcjMHBXpPfwbEWA0XkhS0Qvw,729
phonenumbers/shortdata/region_BZ.py,sha256=Ksr5FM8i8cCOl64Garl8AnrbJYJzj9UCvXYwBaMRowk,711
phonenumbers/shortdata/region_CA.py,sha256=YMUQWzsDcbuIPw6wTgVH7OtWcM_OJ3_P5mwR6Ab0iE4,977
phonenumbers/shortdata/region_CC.py,sha256=f_NYuaBcV0TqPwydciosiX-udgmZKrC36ijasJoKgoo,669
phonenumbers/shortdata/region_CD.py,sha256=xecnFYI1zIRNBLdlE_VqNljYS1U5a5kzuYtqV3XigPk,961
phonenumbers/shortdata/region_CF.py,sha256=UKuv2R72XB3bFn7IXSLTAASepHa3B6KRE1VrFRZRRt0,700
phonenumbers/shortdata/region_CG.py,sha256=NY03zdpTTdkDoiSQo_IwtSjuCmXHdtFcWhZ2QBTvPnM,667
phonenumbers/shortdata/region_CH.py,sha256=qBYcWtNnrC25AwJgww-dq-qdsBDMo9s_kAQ1cmXZ3sE,1375
phonenumbers/shortdata/region_CI.py,sha256=y1qJW8Q3qihEAV12l6Y87j7zyExGLwcijsL_laiB-Vc,937
phonenumbers/shortdata/region_CK.py,sha256=lUKOODdq3ZRucrYVVCAtlcjPqAIsJJmaHqk30dwW4i0,666
phonenumbers/shortdata/region_CL.py,sha256=x7YAGgkeH-1RkZNT54APwf4udKHFOUghn7Zzjqk9P3c,1825
phonenumbers/shortdata/region_CM.py,sha256=MmRqdQmGzR9Gxt01C9yV50gvkkNhykKFYC7zX9UfbA0,941
phonenumbers/shortdata/region_CN.py,sha256=PKUADyursJ2CIQQ3SVLHD_MiGV5BHyEbuFoyzJSlE8c,1077
phonenumbers/shortdata/region_CO.py,sha256=qaUYJF-oiHfEf9Jbox217vG0QUuaK0rWwZ692r26Hsw,1088
phonenumbers/shortdata/region_CR.py,sha256=FeIwvgPv_zBJXz2FsNUHuiae6zN-qZW1c0cUeRDrVWU,1000
phonenumbers/shortdata/region_CU.py,sha256=wulQmI4mNeZAgxiYv8olOEhB4-cdxy5eEPJ5oeHUmvw,745
phonenumbers/shortdata/region_CV.py,sha256=_kYqV3FX-lqg4NY4Ip9qPU6cXMLGppnrWxqv3IJDs1s,666
phonenumbers/shortdata/region_CW.py,sha256=PzbYeu3BVUAl5qSakfgay95MGKIS-rC90LQMt2_e8RI,898
phonenumbers/shortdata/region_CX.py,sha256=vKkfEzmqnCBWYLEJBzMJorA5BFvxO0atwiN_0IW4Dkw,669
phonenumbers/shortdata/region_CY.py,sha256=GrAXyg_kNrGzUqArVZQYdkbuzEdvog3muOOT0abcckk,721
phonenumbers/shortdata/region_CZ.py,sha256=-qCgL_vde29Kw8BVVLyk97U074qJXvqlfXSgSwjOWe8,775
phonenumbers/shortdata/region_DE.py,sha256=jU6pZDgOBX3LeEeVjieeqZxOFw2Nwoq0wpy-xAhxwxo,869
phonenumbers/shortdata/region_DJ.py,sha256=0hnjgVKT10pNNiL5C_rbd-WSr51Z9KEN6hZXPz13GQY,654
phonenumbers/shortdata/region_DK.py,sha256=v-jlj_n2i7xD15vdB_cQb1UpUNSRnsFWbxctSD6ZEX0,763
phonenumbers/shortdata/region_DM.py,sha256=RzbCcdJRhQp6emfkdhEjb9Q0qgO5wqjT-uufPTIfHx8,696
phonenumbers/shortdata/region_DO.py,sha256=h99NpUgisHMFje6rsPd6LDopDehi6u2aKSO9Y3U4DMM,683
phonenumbers/shortdata/region_DZ.py,sha256=zy2RzMrNMci5oPx8-WHlIwL5TElV8AMcBbubY4K-xPk,940
phonenumbers/shortdata/region_EC.py,sha256=QzNGj-IRQN3MYfXuJXmygumfbT5cbtPKQDKda5ClnoA,699
phonenumbers/shortdata/region_EE.py,sha256=4oYETHstSFI-gpTIMc3hhGr_2OI3m-JHVGcsM9ADZYI,2474
phonenumbers/shortdata/region_EG.py,sha256=iCKUsua4gRDtUpd5sRNOWahMd9S3B6y_t3mtP5cr9iw,958
phonenumbers/shortdata/region_EH.py,sha256=8xFe9rvMPefRvv_VmLqs5ZEOtpPsrw6xIJUpwz3bMuQ,687
phonenumbers/shortdata/region_ER.py,sha256=5vKz0UzFPTZeOH7GOhzyhm4R12aCh5HauHwuAr1BZHw,814
phonenumbers/shortdata/region_ES.py,sha256=DQkgyxO5HROrFPxpqR8wqz-irGY8dbyTB7QGbWfzjXA,1607
phonenumbers/shortdata/region_ET.py,sha256=vfGDuN2z8tOZ5886DLD-SHLysP4T-GSQZf4lESrC8Xo,720
phonenumbers/shortdata/region_FI.py,sha256=0hKCRdHnMEvPsJXq9gbJddaw32b9EDDj_TecnQ7km4s,872
phonenumbers/shortdata/region_FJ.py,sha256=ZqkxJzh5XDHVsEbm3Chguggo_Y5X17yiTEuFEA_AqxI,873
phonenumbers/shortdata/region_FK.py,sha256=MhiCUsjWo_4U9-HI7-mBCtkdAkzqt_okHYK8pBTruk0,665
phonenumbers/shortdata/region_FM.py,sha256=xRA_Jmq0mo45xOUT2C5sW2LlE3IBZ-vl2dAEThw4O7w,706
phonenumbers/shortdata/region_FO.py,sha256=L1Q7lFI3YQ1RdNsCR2rEWJi-NcVQ367ktyywXfjv17k,715
phonenumbers/shortdata/region_FR.py,sha256=Q7JT3kvrewxON2HjoiE_95vdWsApIva8mmm-sCI5bRc,1488
phonenumbers/shortdata/region_GA.py,sha256=PFtZf7k1HA21FlftmvDnSf7DW8-FPegEE4O5TG2S608,712
phonenumbers/shortdata/region_GB.py,sha256=lawV2coYV48zyIKh2hgX2XBEDSsc4I_ijyyd9EqwS-Y,1343
phonenumbers/shortdata/region_GD.py,sha256=IP5uaxH1rm-wNMI3UFGdGZME94JVspTepS0L4QhxAis,897
phonenumbers/shortdata/region_GE.py,sha256=vN7lGPlWXJRknQX7-i68U8l51Qtb5_9jze679evMFew,983
phonenumbers/shortdata/region_GF.py,sha256=cPgOEStriN2XHeeI_SUl-_YVUGHUqmvK8s8uxxfMiNE,657
phonenumbers/shortdata/region_GG.py,sha256=384ptm0Q9Sqge1OuTo_SGU2itAf6xbGrVOOvtiUdDJY,742
phonenumbers/shortdata/region_GH.py,sha256=EtJxPqEnVACTXOxaSPFfMO5UA6bv9Hv19uZ9F-mwaw8,982
phonenumbers/shortdata/region_GI.py,sha256=BYDkfTzFS-pBedEN_sPjEms6kihyoSTc7lt92TkdI_k,1294
phonenumbers/shortdata/region_GL.py,sha256=2Zymxp6Luivr2KMlpyy473Duc0gG5fmgGfiFwlYOgPM,660
phonenumbers/shortdata/region_GM.py,sha256=6s8zXc9NF3pTSQOQZVqE4D1DLrgGnBS7U9WeL4JJHco,702
phonenumbers/shortdata/region_GN.py,sha256=c825SsCczUt04ZqiThTvFqWY_lIEeoUKM1rzvD0OIm0,706
phonenumbers/shortdata/region_GP.py,sha256=zrtB2ZBfr_rhuPhqDatpRLvUvkhOYgjBJk9A0B44528,657
phonenumbers/shortdata/region_GR.py,sha256=71CYo_KlTRRnLk0lx5ksBzMl2XQ6u0sx09tyKZj56jQ,895
phonenumbers/shortdata/region_GT.py,sha256=_NBNvcbPT_iUe_QHxkJnFrt3buPrX-V4icqYzNM6YHE,951
phonenumbers/shortdata/region_GU.py,sha256=N7LT-WRtJxh62RHnNFMMjZZPCcOCqJEf9men98xRo2I,668
phonenumbers/shortdata/region_GW.py,sha256=I0F3lz4cJl93ErT0l1y3BirxAT94o4-BJZ1ODvI3MBQ,666
phonenumbers/shortdata/region_GY.py,sha256=2Bsj7DQ_WCCaaK_MUEEn_Eb-Mg6SFk5YVh47C5E98Gg,976
phonenumbers/shortdata/region_HK.py,sha256=osdFxCspjpS7_ZUt7HiyaCT2kqbZ_Xg8_hL0rhS7XZQ,1230
phonenumbers/shortdata/region_HN.py,sha256=IPloeFrGaAG_hXlYOhCEYSOvujvva9FNIxkqyZbt3nA,916
phonenumbers/shortdata/region_HR.py,sha256=JE0DLU7IcAIWpwha1E8k7_NfSJ8M86oSiz080xMFbGs,1164
phonenumbers/shortdata/region_HT.py,sha256=MxpZESwOFyzQzIKTTo-9qmjNSmkjiMVqKRaFIk9IlBI,925
phonenumbers/shortdata/region_HU.py,sha256=O1Nf7hEkIfQXYrapJN_S5bXoUqzw05zlDKIq9wihoO0,1229
phonenumbers/shortdata/region_ID.py,sha256=alYHDBRNTs-jeCfVkYessSP9GFBSFYWCf4dDcNV9FPw,976
phonenumbers/shortdata/region_IE.py,sha256=3KMu1jbRmTTcmu-IainZkfsvYpSnoKaZ7-a1KNtXB8U,1253
phonenumbers/shortdata/region_IL.py,sha256=IPuWvoreYoufKt8dRHlahH7y8B-Q3ERDAy2T7-o_Ct4,985
phonenumbers/shortdata/region_IM.py,sha256=bperfn3dqD7igcUUQKb5IO9-MD3cNrOzecYFUb6XKSw,839
phonenumbers/shortdata/region_IN.py,sha256=9ZTKVgz8TKL6yGzIO7b-98zNLVq00cIwe9KTZmygnok,2482
phonenumbers/shortdata/region_IQ.py,sha256=7Yvl3dYlLfhMUJxlYzAc5eg8XgNn0xnr3MyrbQQx9SU,990
phonenumbers/shortdata/region_IR.py,sha256=CrzxFW2G6U8U6bcV_PWMa3IcF0OLLUu_x5VzK9QrkN4,1343
phonenumbers/shortdata/region_IS.py,sha256=fK11CW2Cp_NpnLUcA8wB-zUPTYUPhcZZYPXCFJpinNs,1034
phonenumbers/shortdata/region_IT.py,sha256=h0bCQjZpYGFgd7kCnh685Z0lkbww5IqAW_GUhFTM7Gw,1271
phonenumbers/shortdata/region_JE.py,sha256=m7oDDcNZZkGZaCRKN3hNO2roz5FSp5Ll2cFAQwHR_G8,785
phonenumbers/shortdata/region_JM.py,sha256=QlH589mRM5dk02UDezlZV6mDxMKX6dXJJ28kkAuUXv8,924
phonenumbers/shortdata/region_JO.py,sha256=KUmuOMUbYvst2sWphuduy-ppg5BjL9Pdq6oXMev7R9k,1134
phonenumbers/shortdata/region_JP.py,sha256=GSnD4fBGMGfozsWdsQ_F8NKh6670v-k0MPPmfE7m97g,844
phonenumbers/shortdata/region_KE.py,sha256=r1AbEbf7ydMadEzjovufPBH-ci-R2Uwk7FPb4PWBclo,1537
phonenumbers/shortdata/region_KG.py,sha256=tkY811aJr7IHMTdPUIooAgJy8zKQ6LzMxOR9bDQY07Y,910
phonenumbers/shortdata/region_KH.py,sha256=XZ18kgMx2oNGN7m83mF6LbouRUoCIHBILC_5vIHZb4E,941
phonenumbers/shortdata/region_KI.py,sha256=lQSDEm-AyTiYHrzzc7rQYMjAcGqM74TTKtwNUTQOjMc,839
phonenumbers/shortdata/region_KM.py,sha256=of2qlLgPV30-riboYAwvXoe4CbDn7erxE7rSUvufdNc,654
phonenumbers/shortdata/region_KN.py,sha256=FOg6tjl4rQpDOoM4KjqmVUq3fsmkyP01-qAyHCOXFBk,696
phonenumbers/shortdata/region_KP.py,sha256=AmSVpPfVTatCpjL9N_EdyEDbSRiiDMaTRL77VMnAfe4,678
phonenumbers/shortdata/region_KR.py,sha256=AdY0NCiZ_VHfq05456TH58ZGjdu6ykLfF3enC9xzWgQ,957
phonenumbers/shortdata/region_KW.py,sha256=9YKvRKzO2TtatVtNqh_vEmupjQlem2j_0zIhVQbxNjQ,805
phonenumbers/shortdata/region_KY.py,sha256=oIzKaFr8QcNTDnBL6tbAco3ppZcz7-RKesA3zDobUa8,668
phonenumbers/shortdata/region_KZ.py,sha256=ajOO1sEAnIWcEeJd13mGID2DPz8o7LCsO1zKR7_DJJw,987
phonenumbers/shortdata/region_LA.py,sha256=2fO1LToVm9GX-IQQuYLHGVwyE_Ry5rpI4ZrmaIJOyV4,666
phonenumbers/shortdata/region_LB.py,sha256=cQf9JZWeiTpgnSffQ3CQINvJmK230OsL-UtIfuwsTqY,699
phonenumbers/shortdata/region_LC.py,sha256=gPkOHHk2Z8B1OSsfxQ_-IbMaAw4z8gTy7IUT9GttkPY,681
phonenumbers/shortdata/region_LI.py,sha256=IrSv5TYP-evLnhTZ5sjxve2ShDcDNxwozlXKE5rFFaY,718
phonenumbers/shortdata/region_LK.py,sha256=aB5646bMEY2Dp23RwYtu5854fb7MTQUxTtHEeAfVu8I,839
phonenumbers/shortdata/region_LR.py,sha256=GgVu4SJISJ77ZcWh_iS9EvthuBCYl69d5raZVFXd6Ec,955
phonenumbers/shortdata/region_LS.py,sha256=icF9EEesG-Cckxred9dF6aTs4vJcaI8NG5l6_NeiwZs,666
phonenumbers/shortdata/region_LT.py,sha256=O5EPFTRubCbVVa2zD8-DnGhQBXEi_i_SHlO_sxMM-sM,830
phonenumbers/shortdata/region_LU.py,sha256=fMb2traoF7t_vAjeP9CbhA-V1z-2Uz9xRvyitYLSclY,736
phonenumbers/shortdata/region_LV.py,sha256=76sjnWQwi7WTgZQ6Q3lVZA-XBg9wgDj5181UskrHf1k,1135
phonenumbers/shortdata/region_LY.py,sha256=D-n_cvDcCPCfK6g2vZinOn1w1oS0g81HQIyTHkdcavo,666
phonenumbers/shortdata/region_MA.py,sha256=tYgmlp9PwWPgKKxgphbTzuOz1DQxnUg5RTKW8MdcOFA,687
phonenumbers/shortdata/region_MC.py,sha256=1XNMPZgtxVHwT5u0T0SKQvt3NDjhckHF5IchSznGxrM,693
phonenumbers/shortdata/region_MD.py,sha256=V3LpMuR15nOxeaxO5otnaaNttkCd_oNmTqBdORmGynY,814
phonenumbers/shortdata/region_ME.py,sha256=JSUkXyTfV3lqdb664zzUJVU9fZJUVwQ4ju__jpO1rUQ,803
phonenumbers/shortdata/region_MF.py,sha256=NPUrVt1Gj6wH5TL0bU1jTgTkSbVDkHiwlxrBThY-Xlg,657
phonenumbers/shortdata/region_MG.py,sha256=ibpOEjrMoOv9R57Ef_IYQYWELK8_HuWCF4SkZGhKG7A,696
phonenumbers/shortdata/region_MH.py,sha256=VQp0rMqXOX44S1tbk4dSMHdlQf13GkEDOgMCyN8-DjY,654
phonenumbers/shortdata/region_MK.py,sha256=7S4sffPALlJ860z2sV5vVzg_Yj8J5DYWWZBLvlMWkSo,763
phonenumbers/shortdata/region_ML.py,sha256=MfCTO4rB2rW8FNPlCkPUWWo-_K8QsAus9WZpTXMnQYE,1456
phonenumbers/shortdata/region_MM.py,sha256=Fs-U1iq0TNC3hfNYaO1BzTXqaUDBLpoawDcsxuCV1mo,654
phonenumbers/shortdata/region_MN.py,sha256=8v0zLAR_AEp70xJkVHksnbqBX1rveuUQo_stYzt9IoQ,669
phonenumbers/shortdata/region_MO.py,sha256=XCdQc1wN6aFTDrT-wljX8pEDmbLw0FHUtUiYl_Dv-Ic,654
phonenumbers/shortdata/region_MP.py,sha256=eHOA1hu4DyxK7o9CGRPvjddxApghi_prEZYHaroCEso,668
phonenumbers/shortdata/region_MQ.py,sha256=xnC2DVklqE4zlkw1QdUTONXyY3eBmebCQ9GoEOpKb0s,1012
phonenumbers/shortdata/region_MR.py,sha256=BfgMxvdKKreJVkhM0HT2E97XBUV2A2DNxSKCQEQH7-Y,654
phonenumbers/shortdata/region_MS.py,sha256=RroSozeW64UsmQkyH7NETkpgHQ-THzSx4u4RJzfssF0,688
phonenumbers/shortdata/region_MT.py,sha256=1D70xMrSZNWfWUT2s1EmSMv2tE39wZOVowOuKmaZcO8,707
phonenumbers/shortdata/region_MU.py,sha256=PwEcT6fFMajwmcvkrNIkMjgFOQ8YfkAollKL3WEsz2E,714
phonenumbers/shortdata/region_MV.py,sha256=HFZZ3dGcyZUsKnYMOV-rmG6K5og1ZU09MRXFcoosU94,833
phonenumbers/shortdata/region_MW.py,sha256=S4i06saZcLGAjPH_2jx9-f8kukG-MudEr4U8BWSF_wc,941
phonenumbers/shortdata/region_MX.py,sha256=F8T3LFZr8OKCUbr8-F9CNgSJmMlu7rN1nb1x8GcfTPs,980
phonenumbers/shortdata/region_MY.py,sha256=atcJhWylaUvPDRYlrzXyDSh3bupkDpK_bOtLuVTqkkI,1177
phonenumbers/shortdata/region_MZ.py,sha256=2nJchmKJaG51lW7zsbtDtxOTJKjY7_fxnjosgIa3jJQ,712
phonenumbers/shortdata/region_NA.py,sha256=5hYTdzPuby9a-5QZ4At0BSsgqJUwVBP4OWwC3bYQ4RE,703
phonenumbers/shortdata/region_NC.py,sha256=Rz09JySMEN7D-_ixmZr5ZF4hIM5BSBL-LqHhx4DkKNY,905
phonenumbers/shortdata/region_NE.py,sha256=AkshlcKHoBLTFmaIKjVpXzu-836mTv9mrmqRkIOwwqk,962
phonenumbers/shortdata/region_NF.py,sha256=v2MVDOTTPj2u2oVsp6s2lAztwKPFdfKKp6jGkmKU1V0,684
phonenumbers/shortdata/region_NG.py,sha256=VocH9q7t4GuHn9EWi6JWH17HD71VypQChPgz8ybf5Yc,916
phonenumbers/shortdata/region_NI.py,sha256=oRGUW_yr3Dt8kGNUZVHBGTDkxKohOk10fg4GI9Ev8Oc,755
phonenumbers/shortdata/region_NL.py,sha256=mpLAsBmYuZZkvK2lExP_5hiWvqjP3Yq-k1gudoPq_rw,1045
phonenumbers/shortdata/region_NO.py,sha256=HsHIvcCpKyweAW_N6kxAAlsXaEzzt-QXPzh99f4wCEc,889
phonenumbers/shortdata/region_NP.py,sha256=Dz7ZplCQimVSzDrbAgiRURXAHIgDoJUqx2KRe9tUnWQ,729
phonenumbers/shortdata/region_NR.py,sha256=I0ye_8ygsNbqToCxXYoy4xH-GXuJFUPLoaNSrVjJXFE,676
phonenumbers/shortdata/region_NU.py,sha256=6D-9yWDi82CkNA0QHzVY80KN9J9BO91ixNG5ndIU8Jo,782
phonenumbers/shortdata/region_NZ.py,sha256=RLjh9YT22LG3s7LRoOp1kOXTm8EPeVkvtAsgeUuQTS0,1008
phonenumbers/shortdata/region_OM.py,sha256=JBOSxdYPC0Masct0GsEpYRtQ_HMHJ3SXOBM9EhAxsS4,746
phonenumbers/shortdata/region_PA.py,sha256=CLJIuWUrppok_LqsGP1KxSbksg-xDLpqpBl2Q_hGj4c,665
phonenumbers/shortdata/region_PE.py,sha256=I1GyJO8-u0FTojiJgIJ7TPnZVejRzK7o8K1XPwYzIM8,684
phonenumbers/shortdata/region_PF.py,sha256=7aRNt50cSmBK-mxk61DTR1BzbKvGTg6Hs6ySN8AnLSA,657
phonenumbers/shortdata/region_PG.py,sha256=wSUFy1SRn8VmQ4hNre3bJrhh8gL4gpwZxQp7vr8qkeU,849
phonenumbers/shortdata/region_PH.py,sha256=yq7N3h69QL4zWBAVmswMk4ErN3BWxIslChPBGYUul2c,678
phonenumbers/shortdata/region_PK.py,sha256=h29ooAHEs4aqH67a7500ZTDOyvqBmiCVFplqzxtbNsQ,737
phonenumbers/shortdata/region_PL.py,sha256=Yc3pnd97Po8xThlNSqb10Xr6BsTc-GKkmZfhF93S7dI,775
phonenumbers/shortdata/region_PM.py,sha256=wuzKGPzByFmqvDo4Fg_X8xfSnEvsux4K9mNF3vGBUJo,1117
phonenumbers/shortdata/region_PR.py,sha256=DKg0cnGpteQNBU1IhteAa2TmroXx7m8jKuxx9LGEp8Y,668
phonenumbers/shortdata/region_PS.py,sha256=1j3D2dHFLIAXe6kmTFTk1h5ev5yH_GKu9v7i5pJAx_Q,813
phonenumbers/shortdata/region_PT.py,sha256=_OPFbaQb4zdC5PFAXsL80g86BEx6KE-wLiI41gTkQbA,872
phonenumbers/shortdata/region_PW.py,sha256=uVbcrn1q3NLjIIZuaAlXbM9JAnqNgU1c8rUqpNJ2MeE,654
phonenumbers/shortdata/region_PY.py,sha256=V16mhqu50pFKiIAEDecIDuzhiTxMfvUn_Mz6_htBCJI,675
phonenumbers/shortdata/region_QA.py,sha256=5aDxPLBvEL3A6esxZfMpaoy4afNh_sisAQAZiWE1Ng8,817
phonenumbers/shortdata/region_RE.py,sha256=U0E_58QTFXjIpU5vlTEfDEEVIcO8cnW_KHeDxnuiPnY,690
phonenumbers/shortdata/region_RO.py,sha256=-uRNQcgoJfPdnrpLIGsgwbUzE8Rj7dtVjhvns7SRjR4,1081
phonenumbers/shortdata/region_RS.py,sha256=g9fKpD1GOjQAoSYBdlRoAi1yNcLCfjiDR-7DfWpz2qo,714
phonenumbers/shortdata/region_RU.py,sha256=kUTlXiQ4kn8Jn__Tti5x2ekBoDPsxLnolvJAciUE-1E,705
phonenumbers/shortdata/region_RW.py,sha256=YtbYh-VgmlxSktAVPiYHD3LVUjT8BRKxq3WJ5tMsjJc,699
phonenumbers/shortdata/region_SA.py,sha256=ZLeXWvJlHuBE28r7iW_yAm3oWQSDpd7LGPaTTSmpUgI,1068
phonenumbers/shortdata/region_SB.py,sha256=EKl1Lg19EXMzeZ4hAahJE0fDlK0E8Rri6nJNV5zeVfY,756
phonenumbers/shortdata/region_SC.py,sha256=W1sgLME9ki3DBjUg0PNmqHoAGtWuzss9Q7uK57lzrGc,764
phonenumbers/shortdata/region_SD.py,sha256=zrJp6cFYhe34qPxBS9H5TX3HiLE55rS454nNRKWzLdw,654
phonenumbers/shortdata/region_SE.py,sha256=CI_pfIEjXW10D0nLTyTEe3yrzyE44kAL_gNnDDUKRTk,1334
phonenumbers/shortdata/region_SG.py,sha256=r04zfIkGfL25vPpd8X98ciruarp23WcHJTfKVRPqcjc,870
phonenumbers/shortdata/region_SH.py,sha256=rnWq44BoCsKKwCshfD5RdEsrthKfHquFHaTn0K9GUz8,714
phonenumbers/shortdata/region_SI.py,sha256=EC-9o9tnklq02ZUAE0E5VAgevWud7Pj6lHO4P5-PesQ,829
phonenumbers/shortdata/region_SJ.py,sha256=MvQm4rOBj9CGBReSt5hSVY3QucOmQJEn3Vxx_sh22Vk,809
phonenumbers/shortdata/region_SK.py,sha256=aM6flfRV4WxvIBvPzZUXwF_SVL8dxyQMn2gxYs0L2Gk,756
phonenumbers/shortdata/region_SL.py,sha256=zagcVULnoLB9m0GN3c5AQC9BDYHxMoZfZp_aON3iZl0,938
phonenumbers/shortdata/region_SM.py,sha256=Pa746O1s0l7pcHPT1R_4So1CEa_P0ICerY7thGZGQgI,666
phonenumbers/shortdata/region_SN.py,sha256=u4melPV_HXdV87upokTrJELZnq2h1VonmZv-_kuXhQ0,1276
phonenumbers/shortdata/region_SO.py,sha256=LhvZU8qYZzj6WzelUjDNukbNinfgEP_a1XLcGPjaWIE,687
phonenumbers/shortdata/region_SR.py,sha256=gpmZUsffKkL6yArgLqGmh_ocrpB7wXhWrvYTsilCDQg,666
phonenumbers/shortdata/region_SS.py,sha256=07hPGM4_ewk61qmt1eOSARj3LHT8hOovKY2wb1ZxwVQ,654
phonenumbers/shortdata/region_ST.py,sha256=EQYE1LAWLf0OoTDdpNlX-COhCAAykR1AQagjiVBRtyY,654
phonenumbers/shortdata/region_SV.py,sha256=WgZ-am2HHVMZpAbq6prbDIfH8Qk00Cj3dvNDBVmgyJo,988
phonenumbers/shortdata/region_SX.py,sha256=KIsWgLgOL5Ky12wmXMZdLD2WGzFoJHNDbIgOhrAYVb0,668
phonenumbers/shortdata/region_SY.py,sha256=Mi8Oa8Wf-zXn5H0YWrV5dBucsxTHtTqIdamnkgu4VPM,666
phonenumbers/shortdata/region_SZ.py,sha256=59pNpLmRp-55ijB4aSHb_9R9YembAg7LEV94IbEQrac,654
phonenumbers/shortdata/region_TC.py,sha256=Jx2KfUBOiSqn1XKj9-m4pYZyINg9qBXCVCGOVvhczBM,681
phonenumbers/shortdata/region_TD.py,sha256=GbnrzpDT6F95-VafmAPP4QB-b5NvMKBh8trL1TmgzTo,654
phonenumbers/shortdata/region_TG.py,sha256=xdWtfxhor8tIo6xpVVe5GidiWE3eYMmFMxvqr-TmHkg,707
phonenumbers/shortdata/region_TH.py,sha256=9Rcyljy5_p_1jx1xnSA4csjuCe0Ihb-wEjbEtOqFp2g,1623
phonenumbers/shortdata/region_TJ.py,sha256=6m2YTnP3FfoUzz97rJtrhd7NXpzDvUNipit2BARqX98,687
phonenumbers/shortdata/region_TL.py,sha256=oPXzaz5vWnAnTcxKONM8fEoz_Jj0w3NKv75ZMYCT0GI,690
phonenumbers/shortdata/region_TM.py,sha256=BlS_maAzR1tenCERwB-5WpVtZwSnZBckr1uGLR7YcHc,659
phonenumbers/shortdata/region_TN.py,sha256=vK_XAAAZG_IZ3-I6bGlZEip2X-rGoQIpF0dOfmqt1Bc,666
phonenumbers/shortdata/region_TO.py,sha256=DJ2XK-mh7SrGTGD8eAudRD_VYfBI_c8s6MQVNyEEkWg,693
phonenumbers/shortdata/region_TR.py,sha256=raOFkirrtvQD0ZWnvhuocjGG5OuFlFeTKf7B36UJSxo,1685
phonenumbers/shortdata/region_TT.py,sha256=24XNgj21UyAMM2fWvu-zdjcFnk1_t44EUwY-eQ1lZTU,677
phonenumbers/shortdata/region_TV.py,sha256=-PqGFH2zZQSEf1ixMH9tjRxY21FNRW-50IKVQX9csNg,665
phonenumbers/shortdata/region_TW.py,sha256=RIQD2r0q5VpSAMQcuyV83TvRprXYLpp9qMsvKSwdyt8,1018
phonenumbers/shortdata/region_TZ.py,sha256=wYk72GtAlKE9u9ZOkv8cEbUN8ozGclXB7R0vJAGwafE,962
phonenumbers/shortdata/region_UA.py,sha256=U-nESieUy40Zmdi3QXZ28vKMau0RwMqB4M9Ub1PwxoE,948
phonenumbers/shortdata/region_UG.py,sha256=6X0ln8Suq3moSzZmLHczAzuKgtoR87UUb-5ezuhD0K8,654
phonenumbers/shortdata/region_US.py,sha256=HKcgy1IWKZDJ-7uWpnql3h6as3NeMibB7Ntj-c4hvq8,1649
phonenumbers/shortdata/region_UY.py,sha256=SS-BOvvyNP6fTzQ1lod38Ic7K5lq-jpky4a3mGB7V-A,840
phonenumbers/shortdata/region_UZ.py,sha256=pJ1AY0chHSxaqsE2z8IXwyIl61oLJddZzvaao45J-Lg,979
phonenumbers/shortdata/region_VA.py,sha256=oPX9qimA-VJlt5leRsp4K4n92ueBpbjlG1-vpRQiF3c,669
phonenumbers/shortdata/region_VC.py,sha256=3qBLmhAYY_AfG2j0pzJGMWJ3ksreqnGZnbZe35feN2Y,681
phonenumbers/shortdata/region_VE.py,sha256=usD93FaIjir5ytu9RS_kvic-S04KvnYy22nm43msv9o,690
phonenumbers/shortdata/region_VG.py,sha256=NBWT2KE708oSwaxP61iGcCTlhpJqIHN-jUzJZSrlVcI,681
phonenumbers/shortdata/region_VI.py,sha256=fm80gVBjMUHYrghysCxi0ql5Qaumx6Evb_pdmHuWBVg,668
phonenumbers/shortdata/region_VN.py,sha256=aiTj1oqa_VsPjfvBQYrOBzOjyzIM4H6dHflGFDufNho,666
phonenumbers/shortdata/region_VU.py,sha256=9eR5ukBSvIb_uzXRmCRyxOjlrUZ5_NPDesx7jVdhO3A,654
phonenumbers/shortdata/region_WF.py,sha256=ltQRCPa24-2IHdVwoLSLp7JPUE3iJjOOCJ4hlmq30RQ,657
phonenumbers/shortdata/region_WS.py,sha256=pzJJm3HdQZ96LklNzu8BdQ5BqX1FaZpIm0wRjMoxm6w,834
phonenumbers/shortdata/region_XK.py,sha256=y3UzSniETc3BtgYB0q1JwJ8fAxkn-bCdYkmw0_b7wf8,687
phonenumbers/shortdata/region_YE.py,sha256=JZpDBtDIm73fXC1ATe_fxgdtCBYle3w1eQXGcSoqHF8,669
phonenumbers/shortdata/region_YT.py,sha256=bd-ApQR4fMSm7iwUPAh0xoKXbCqZeayNo8UHyNcpsIk,678
phonenumbers/shortdata/region_ZA.py,sha256=kGdEr1QynrfRkZ7Hfn5e_iM7PR1U5idU5m61MRR7Z5s,1297
phonenumbers/shortdata/region_ZM.py,sha256=FcFCy0ThwY_gCFiczEqvohexazLjIsGastcYFwN5Ddc,681
phonenumbers/shortdata/region_ZW.py,sha256=gFbloUKlzPZOaOvEQBZYjna38t2em9DWNl3UrCfKjRI,1014
phonenumbers/shortnumberinfo.py,sha256=LrxeJQXPTOvSThhiCW0w-I-eqnnrRDsgLJl9LcFLQ2g,20635
phonenumbers/shortnumberinfo.pyi,sha256=Z2XfUXmKQCxy8zX1YRihoFdZyjwW_2otmUC6iwZxRXU,1755
phonenumbers/timezone.py,sha256=crlRLxP1_ksMUhEDXkZIwXk70XozAywK9MXiuW43cMI,4950
phonenumbers/timezone.pyi,sha256=cxHLjqgJ678kore1vOYYKagKK58LmW0nRj5zVLWCTMg,362
phonenumbers/tzdata/__init__.py,sha256=OILvqC2zHqIk-se-HxwcH3i7jeBrU5heA-_ppuaLMbw,824
phonenumbers/tzdata/__init__.pyi,sha256=EKmrM4jAiQ_XtA5es0vHmKu3WGiMWlvm3f1OYWtyKXo,71
phonenumbers/tzdata/__pycache__/__init__.cpython-311.pyc,,
phonenumbers/tzdata/__pycache__/data0.cpython-311.pyc,,
phonenumbers/tzdata/data0.py,sha256=-ZhxlqKr5RC7L6GIe2X1Wk-YdM1Tbqb7eKCu1j_RQJM,116441
phonenumbers/unicode_util.py,sha256=8Y9Ux5aYtfQlddvrLLtE7kMNLXvsRcOYbVcdK5KVLUw,19033
phonenumbers/unicode_util.pyi,sha256=ha6GiGsig2BD_4WiB-39SREEngUIvjI073D0v_SzfP8,8702
phonenumbers/util.py,sha256=3Ivg-e7FpMMevE15UpTzX98ewYqRwnMchVz3AFfHmec,5686
phonenumbers/util.pyi,sha256=2GkM8VPcn-E69KTOhiXxbydu8L2dOdHYZxloYwnkHCY,963
