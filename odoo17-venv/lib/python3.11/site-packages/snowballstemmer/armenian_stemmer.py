# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class ArmenianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"\u0580\u0578\u0580\u0564", -1, 1),
        Among(u"\u0565\u0580\u0578\u0580\u0564", 0, 1),
        Among(u"\u0561\u056C\u056B", -1, 1),
        Among(u"\u0561\u056F\u056B", -1, 1),
        Among(u"\u0578\u0580\u0561\u056F", -1, 1),
        Among(u"\u0565\u0572", -1, 1),
        Among(u"\u0561\u056F\u0561\u0576", -1, 1),
        Among(u"\u0561\u0580\u0561\u0576", -1, 1),
        Among(u"\u0565\u0576", -1, 1),
        Among(u"\u0565\u056F\u0565\u0576", 8, 1),
        Among(u"\u0565\u0580\u0565\u0576", 8, 1),
        Among(u"\u0578\u0580\u0567\u0576", -1, 1),
        Among(u"\u056B\u0576", -1, 1),
        Among(u"\u0563\u056B\u0576", 12, 1),
        Among(u"\u0578\u057E\u056B\u0576", 12, 1),
        Among(u"\u056C\u0561\u0575\u0576", -1, 1),
        Among(u"\u057E\u0578\u0582\u0576", -1, 1),
        Among(u"\u057A\u0565\u057D", -1, 1),
        Among(u"\u056B\u057E", -1, 1),
        Among(u"\u0561\u057F", -1, 1),
        Among(u"\u0561\u057E\u0565\u057F", -1, 1),
        Among(u"\u056F\u0578\u057F", -1, 1),
        Among(u"\u0562\u0561\u0580", -1, 1)
    ]

    a_1 = [
        Among(u"\u0561", -1, 1),
        Among(u"\u0561\u0581\u0561", 0, 1),
        Among(u"\u0565\u0581\u0561", 0, 1),
        Among(u"\u057E\u0565", -1, 1),
        Among(u"\u0561\u0581\u0580\u056B", -1, 1),
        Among(u"\u0561\u0581\u056B", -1, 1),
        Among(u"\u0565\u0581\u056B", -1, 1),
        Among(u"\u057E\u0565\u0581\u056B", 6, 1),
        Among(u"\u0561\u056C", -1, 1),
        Among(u"\u0568\u0561\u056C", 8, 1),
        Among(u"\u0561\u0576\u0561\u056C", 8, 1),
        Among(u"\u0565\u0576\u0561\u056C", 8, 1),
        Among(u"\u0561\u0581\u0576\u0561\u056C", 8, 1),
        Among(u"\u0565\u056C", -1, 1),
        Among(u"\u0568\u0565\u056C", 13, 1),
        Among(u"\u0576\u0565\u056C", 13, 1),
        Among(u"\u0581\u0576\u0565\u056C", 15, 1),
        Among(u"\u0565\u0581\u0576\u0565\u056C", 16, 1),
        Among(u"\u0579\u0565\u056C", 13, 1),
        Among(u"\u057E\u0565\u056C", 13, 1),
        Among(u"\u0561\u0581\u057E\u0565\u056C", 19, 1),
        Among(u"\u0565\u0581\u057E\u0565\u056C", 19, 1),
        Among(u"\u057F\u0565\u056C", 13, 1),
        Among(u"\u0561\u057F\u0565\u056C", 22, 1),
        Among(u"\u0578\u057F\u0565\u056C", 22, 1),
        Among(u"\u056F\u0578\u057F\u0565\u056C", 24, 1),
        Among(u"\u057E\u0561\u056E", -1, 1),
        Among(u"\u0578\u0582\u0574", -1, 1),
        Among(u"\u057E\u0578\u0582\u0574", 27, 1),
        Among(u"\u0561\u0576", -1, 1),
        Among(u"\u0581\u0561\u0576", 29, 1),
        Among(u"\u0561\u0581\u0561\u0576", 30, 1),
        Among(u"\u0561\u0581\u0580\u056B\u0576", -1, 1),
        Among(u"\u0561\u0581\u056B\u0576", -1, 1),
        Among(u"\u0565\u0581\u056B\u0576", -1, 1),
        Among(u"\u057E\u0565\u0581\u056B\u0576", 34, 1),
        Among(u"\u0561\u056C\u056B\u057D", -1, 1),
        Among(u"\u0565\u056C\u056B\u057D", -1, 1),
        Among(u"\u0561\u057E", -1, 1),
        Among(u"\u0561\u0581\u0561\u057E", 38, 1),
        Among(u"\u0565\u0581\u0561\u057E", 38, 1),
        Among(u"\u0561\u056C\u0578\u057E", -1, 1),
        Among(u"\u0565\u056C\u0578\u057E", -1, 1),
        Among(u"\u0561\u0580", -1, 1),
        Among(u"\u0561\u0581\u0561\u0580", 43, 1),
        Among(u"\u0565\u0581\u0561\u0580", 43, 1),
        Among(u"\u0561\u0581\u0580\u056B\u0580", -1, 1),
        Among(u"\u0561\u0581\u056B\u0580", -1, 1),
        Among(u"\u0565\u0581\u056B\u0580", -1, 1),
        Among(u"\u057E\u0565\u0581\u056B\u0580", 48, 1),
        Among(u"\u0561\u0581", -1, 1),
        Among(u"\u0565\u0581", -1, 1),
        Among(u"\u0561\u0581\u0580\u0565\u0581", 51, 1),
        Among(u"\u0561\u056C\u0578\u0582\u0581", -1, 1),
        Among(u"\u0565\u056C\u0578\u0582\u0581", -1, 1),
        Among(u"\u0561\u056C\u0578\u0582", -1, 1),
        Among(u"\u0565\u056C\u0578\u0582", -1, 1),
        Among(u"\u0561\u0584", -1, 1),
        Among(u"\u0581\u0561\u0584", 57, 1),
        Among(u"\u0561\u0581\u0561\u0584", 58, 1),
        Among(u"\u0561\u0581\u0580\u056B\u0584", -1, 1),
        Among(u"\u0561\u0581\u056B\u0584", -1, 1),
        Among(u"\u0565\u0581\u056B\u0584", -1, 1),
        Among(u"\u057E\u0565\u0581\u056B\u0584", 62, 1),
        Among(u"\u0561\u0576\u0584", -1, 1),
        Among(u"\u0581\u0561\u0576\u0584", 64, 1),
        Among(u"\u0561\u0581\u0561\u0576\u0584", 65, 1),
        Among(u"\u0561\u0581\u0580\u056B\u0576\u0584", -1, 1),
        Among(u"\u0561\u0581\u056B\u0576\u0584", -1, 1),
        Among(u"\u0565\u0581\u056B\u0576\u0584", -1, 1),
        Among(u"\u057E\u0565\u0581\u056B\u0576\u0584", 69, 1)
    ]

    a_2 = [
        Among(u"\u0578\u0580\u0564", -1, 1),
        Among(u"\u0578\u0582\u0575\u0569", -1, 1),
        Among(u"\u0578\u0582\u0570\u056B", -1, 1),
        Among(u"\u0581\u056B", -1, 1),
        Among(u"\u056B\u056C", -1, 1),
        Among(u"\u0561\u056F", -1, 1),
        Among(u"\u0575\u0561\u056F", 5, 1),
        Among(u"\u0561\u0576\u0561\u056F", 5, 1),
        Among(u"\u056B\u056F", -1, 1),
        Among(u"\u0578\u0582\u056F", -1, 1),
        Among(u"\u0561\u0576", -1, 1),
        Among(u"\u057A\u0561\u0576", 10, 1),
        Among(u"\u057D\u057F\u0561\u0576", 10, 1),
        Among(u"\u0561\u0580\u0561\u0576", 10, 1),
        Among(u"\u0565\u0572\u0567\u0576", -1, 1),
        Among(u"\u0575\u0578\u0582\u0576", -1, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0578\u0582\u0576", 15, 1),
        Among(u"\u0561\u056E\u0578", -1, 1),
        Among(u"\u056B\u0579", -1, 1),
        Among(u"\u0578\u0582\u057D", -1, 1),
        Among(u"\u0578\u0582\u057D\u057F", -1, 1),
        Among(u"\u0563\u0561\u0580", -1, 1),
        Among(u"\u057E\u0578\u0580", -1, 1),
        Among(u"\u0561\u057E\u0578\u0580", 22, 1),
        Among(u"\u0578\u0581", -1, 1),
        Among(u"\u0561\u0576\u0585\u0581", -1, 1),
        Among(u"\u0578\u0582", -1, 1),
        Among(u"\u0584", -1, 1),
        Among(u"\u0579\u0565\u0584", 27, 1),
        Among(u"\u056B\u0584", 27, 1),
        Among(u"\u0561\u056C\u056B\u0584", 29, 1),
        Among(u"\u0561\u0576\u056B\u0584", 29, 1),
        Among(u"\u057E\u0561\u056E\u0584", 27, 1),
        Among(u"\u0578\u0582\u0575\u0584", 27, 1),
        Among(u"\u0565\u0576\u0584", 27, 1),
        Among(u"\u0578\u0576\u0584", 27, 1),
        Among(u"\u0578\u0582\u0576\u0584", 27, 1),
        Among(u"\u0574\u0578\u0582\u0576\u0584", 36, 1),
        Among(u"\u056B\u0579\u0584", 27, 1),
        Among(u"\u0561\u0580\u0584", 27, 1)
    ]

    a_3 = [
        Among(u"\u057D\u0561", -1, 1),
        Among(u"\u057E\u0561", -1, 1),
        Among(u"\u0561\u0574\u0562", -1, 1),
        Among(u"\u0564", -1, 1),
        Among(u"\u0561\u0576\u0564", 3, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0561\u0576\u0564", 4, 1),
        Among(u"\u057E\u0561\u0576\u0564", 4, 1),
        Among(u"\u0578\u057B\u0564", 3, 1),
        Among(u"\u0565\u0580\u0564", 3, 1),
        Among(u"\u0576\u0565\u0580\u0564", 8, 1),
        Among(u"\u0578\u0582\u0564", 3, 1),
        Among(u"\u0568", -1, 1),
        Among(u"\u0561\u0576\u0568", 11, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0561\u0576\u0568", 12, 1),
        Among(u"\u057E\u0561\u0576\u0568", 12, 1),
        Among(u"\u0578\u057B\u0568", 11, 1),
        Among(u"\u0565\u0580\u0568", 11, 1),
        Among(u"\u0576\u0565\u0580\u0568", 16, 1),
        Among(u"\u056B", -1, 1),
        Among(u"\u057E\u056B", 18, 1),
        Among(u"\u0565\u0580\u056B", 18, 1),
        Among(u"\u0576\u0565\u0580\u056B", 20, 1),
        Among(u"\u0561\u0576\u0578\u0582\u0574", -1, 1),
        Among(u"\u0565\u0580\u0578\u0582\u0574", -1, 1),
        Among(u"\u0576\u0565\u0580\u0578\u0582\u0574", 23, 1),
        Among(u"\u0576", -1, 1),
        Among(u"\u0561\u0576", 25, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0561\u0576", 26, 1),
        Among(u"\u057E\u0561\u0576", 26, 1),
        Among(u"\u056B\u0576", 25, 1),
        Among(u"\u0565\u0580\u056B\u0576", 29, 1),
        Among(u"\u0576\u0565\u0580\u056B\u0576", 30, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0561\u0576\u0576", 25, 1),
        Among(u"\u0565\u0580\u0576", 25, 1),
        Among(u"\u0576\u0565\u0580\u0576", 33, 1),
        Among(u"\u0578\u0582\u0576", 25, 1),
        Among(u"\u0578\u057B", -1, 1),
        Among(u"\u0578\u0582\u0569\u0575\u0561\u0576\u057D", -1, 1),
        Among(u"\u057E\u0561\u0576\u057D", -1, 1),
        Among(u"\u0578\u057B\u057D", -1, 1),
        Among(u"\u0578\u057E", -1, 1),
        Among(u"\u0561\u0576\u0578\u057E", 40, 1),
        Among(u"\u057E\u0578\u057E", 40, 1),
        Among(u"\u0565\u0580\u0578\u057E", 40, 1),
        Among(u"\u0576\u0565\u0580\u0578\u057E", 43, 1),
        Among(u"\u0565\u0580", -1, 1),
        Among(u"\u0576\u0565\u0580", 45, 1),
        Among(u"\u0581", -1, 1),
        Among(u"\u056B\u0581", 47, 1),
        Among(u"\u057E\u0561\u0576\u056B\u0581", 48, 1),
        Among(u"\u0578\u057B\u056B\u0581", 48, 1),
        Among(u"\u057E\u056B\u0581", 48, 1),
        Among(u"\u0565\u0580\u056B\u0581", 48, 1),
        Among(u"\u0576\u0565\u0580\u056B\u0581", 52, 1),
        Among(u"\u0581\u056B\u0581", 48, 1),
        Among(u"\u0578\u0581", 47, 1),
        Among(u"\u0578\u0582\u0581", 47, 1)
    ]

    g_v = [209, 4, 128, 0, 18]

    I_p2 = 0
    I_pV = 0

    def __r_mark_regions(self):
        self.I_pV = self.limit
        self.I_p2 = self.limit
        v_1 = self.cursor
        try:
            if not self.go_out_grouping(ArmenianStemmer.g_v, 1377, 1413):
                raise lab0()
            self.cursor += 1
            self.I_pV = self.cursor
            if not self.go_in_grouping(ArmenianStemmer.g_v, 1377, 1413):
                raise lab0()
            self.cursor += 1
            if not self.go_out_grouping(ArmenianStemmer.g_v, 1377, 1413):
                raise lab0()
            self.cursor += 1
            if not self.go_in_grouping(ArmenianStemmer.g_v, 1377, 1413):
                raise lab0()
            self.cursor += 1
            self.I_p2 = self.cursor
        except lab0: pass
        self.cursor = v_1
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_adjective(self):
        self.ket = self.cursor
        if self.find_among_b(ArmenianStemmer.a_0) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_verb(self):
        self.ket = self.cursor
        if self.find_among_b(ArmenianStemmer.a_1) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_noun(self):
        self.ket = self.cursor
        if self.find_among_b(ArmenianStemmer.a_2) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_ending(self):
        self.ket = self.cursor
        if self.find_among_b(ArmenianStemmer.a_3) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R2():
            return False
        if not self.slice_del():
            return False

        return True

    def _stem(self):
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        if self.cursor < self.I_pV:
            return False
        v_3 = self.limit_backward
        self.limit_backward = self.I_pV
        v_4 = self.limit - self.cursor
        self.__r_ending()
        self.cursor = self.limit - v_4
        v_5 = self.limit - self.cursor
        self.__r_verb()
        self.cursor = self.limit - v_5
        v_6 = self.limit - self.cursor
        self.__r_adjective()
        self.cursor = self.limit - v_6
        v_7 = self.limit - self.cursor
        self.__r_noun()
        self.cursor = self.limit - v_7
        self.limit_backward = v_3
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass
