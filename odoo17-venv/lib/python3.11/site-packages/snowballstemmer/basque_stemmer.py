# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class BasqueStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"idea", -1, 1),
        Among(u"bidea", 0, 1),
        Among(u"kidea", 0, 1),
        Among(u"pidea", 0, 1),
        Among(u"kundea", -1, 1),
        Among(u"galea", -1, 1),
        Among(u"tailea", -1, 1),
        Among(u"tzailea", -1, 1),
        Among(u"gunea", -1, 1),
        Among(u"kunea", -1, 1),
        Among(u"tzaga", -1, 1),
        Among(u"gaia", -1, 1),
        Among(u"aldia", -1, 1),
        Among(u"taldia", 12, 1),
        Among(u"karia", -1, 1),
        Among(u"garria", -1, 2),
        Among(u"karria", -1, 1),
        Among(u"ka", -1, 1),
        Among(u"tzaka", 17, 1),
        Among(u"la", -1, 1),
        Among(u"mena", -1, 1),
        Among(u"pena", -1, 1),
        Among(u"kina", -1, 1),
        Among(u"ezina", -1, 1),
        Among(u"tezina", 23, 1),
        Among(u"kuna", -1, 1),
        Among(u"tuna", -1, 1),
        Among(u"kizuna", -1, 1),
        Among(u"era", -1, 1),
        Among(u"bera", 28, 1),
        Among(u"arabera", 29, 4),
        Among(u"kera", 28, 1),
        Among(u"pera", 28, 1),
        Among(u"orra", -1, 1),
        Among(u"korra", 33, 1),
        Among(u"dura", -1, 1),
        Among(u"gura", -1, 1),
        Among(u"kura", -1, 1),
        Among(u"tura", -1, 1),
        Among(u"eta", -1, 1),
        Among(u"keta", 39, 1),
        Among(u"gailua", -1, 1),
        Among(u"eza", -1, 1),
        Among(u"erreza", 42, 1),
        Among(u"tza", -1, 2),
        Among(u"gaitza", 44, 1),
        Among(u"kaitza", 44, 1),
        Among(u"kuntza", 44, 1),
        Among(u"ide", -1, 1),
        Among(u"bide", 48, 1),
        Among(u"kide", 48, 1),
        Among(u"pide", 48, 1),
        Among(u"kunde", -1, 1),
        Among(u"tzake", -1, 1),
        Among(u"tzeke", -1, 1),
        Among(u"le", -1, 1),
        Among(u"gale", 55, 1),
        Among(u"taile", 55, 1),
        Among(u"tzaile", 55, 1),
        Among(u"gune", -1, 1),
        Among(u"kune", -1, 1),
        Among(u"tze", -1, 1),
        Among(u"atze", 61, 1),
        Among(u"gai", -1, 1),
        Among(u"aldi", -1, 1),
        Among(u"taldi", 64, 1),
        Among(u"ki", -1, 1),
        Among(u"ari", -1, 1),
        Among(u"kari", 67, 1),
        Among(u"lari", 67, 1),
        Among(u"tari", 67, 1),
        Among(u"etari", 70, 1),
        Among(u"garri", -1, 2),
        Among(u"karri", -1, 1),
        Among(u"arazi", -1, 1),
        Among(u"tarazi", 74, 1),
        Among(u"an", -1, 1),
        Among(u"ean", 76, 1),
        Among(u"rean", 77, 1),
        Among(u"kan", 76, 1),
        Among(u"etan", 76, 1),
        Among(u"atseden", -1, 3),
        Among(u"men", -1, 1),
        Among(u"pen", -1, 1),
        Among(u"kin", -1, 1),
        Among(u"rekin", 84, 1),
        Among(u"ezin", -1, 1),
        Among(u"tezin", 86, 1),
        Among(u"tun", -1, 1),
        Among(u"kizun", -1, 1),
        Among(u"go", -1, 1),
        Among(u"ago", 90, 1),
        Among(u"tio", -1, 1),
        Among(u"dako", -1, 1),
        Among(u"or", -1, 1),
        Among(u"kor", 94, 1),
        Among(u"tzat", -1, 1),
        Among(u"du", -1, 1),
        Among(u"gailu", -1, 1),
        Among(u"tu", -1, 1),
        Among(u"atu", 99, 1),
        Among(u"aldatu", 100, 1),
        Among(u"tatu", 100, 1),
        Among(u"baditu", 99, 5),
        Among(u"ez", -1, 1),
        Among(u"errez", 104, 1),
        Among(u"tzez", 104, 1),
        Among(u"gaitz", -1, 1),
        Among(u"kaitz", -1, 1)
    ]

    a_1 = [
        Among(u"ada", -1, 1),
        Among(u"kada", 0, 1),
        Among(u"anda", -1, 1),
        Among(u"denda", -1, 1),
        Among(u"gabea", -1, 1),
        Among(u"kabea", -1, 1),
        Among(u"aldea", -1, 1),
        Among(u"kaldea", 6, 1),
        Among(u"taldea", 6, 1),
        Among(u"ordea", -1, 1),
        Among(u"zalea", -1, 1),
        Among(u"tzalea", 10, 1),
        Among(u"gilea", -1, 1),
        Among(u"emea", -1, 1),
        Among(u"kumea", -1, 1),
        Among(u"nea", -1, 1),
        Among(u"enea", 15, 1),
        Among(u"zionea", 15, 1),
        Among(u"unea", 15, 1),
        Among(u"gunea", 18, 1),
        Among(u"pea", -1, 1),
        Among(u"aurrea", -1, 1),
        Among(u"tea", -1, 1),
        Among(u"kotea", 22, 1),
        Among(u"artea", 22, 1),
        Among(u"ostea", 22, 1),
        Among(u"etxea", -1, 1),
        Among(u"ga", -1, 1),
        Among(u"anga", 27, 1),
        Among(u"gaia", -1, 1),
        Among(u"aldia", -1, 1),
        Among(u"taldia", 30, 1),
        Among(u"handia", -1, 1),
        Among(u"mendia", -1, 1),
        Among(u"geia", -1, 1),
        Among(u"egia", -1, 1),
        Among(u"degia", 35, 1),
        Among(u"tegia", 35, 1),
        Among(u"nahia", -1, 1),
        Among(u"ohia", -1, 1),
        Among(u"kia", -1, 1),
        Among(u"tokia", 40, 1),
        Among(u"oia", -1, 1),
        Among(u"koia", 42, 1),
        Among(u"aria", -1, 1),
        Among(u"karia", 44, 1),
        Among(u"laria", 44, 1),
        Among(u"taria", 44, 1),
        Among(u"eria", -1, 1),
        Among(u"keria", 48, 1),
        Among(u"teria", 48, 1),
        Among(u"garria", -1, 2),
        Among(u"larria", -1, 1),
        Among(u"kirria", -1, 1),
        Among(u"duria", -1, 1),
        Among(u"asia", -1, 1),
        Among(u"tia", -1, 1),
        Among(u"ezia", -1, 1),
        Among(u"bizia", -1, 1),
        Among(u"ontzia", -1, 1),
        Among(u"ka", -1, 1),
        Among(u"joka", 60, 3),
        Among(u"aurka", 60, 10),
        Among(u"ska", 60, 1),
        Among(u"xka", 60, 1),
        Among(u"zka", 60, 1),
        Among(u"gibela", -1, 1),
        Among(u"gela", -1, 1),
        Among(u"kaila", -1, 1),
        Among(u"skila", -1, 1),
        Among(u"tila", -1, 1),
        Among(u"ola", -1, 1),
        Among(u"na", -1, 1),
        Among(u"kana", 72, 1),
        Among(u"ena", 72, 1),
        Among(u"garrena", 74, 1),
        Among(u"gerrena", 74, 1),
        Among(u"urrena", 74, 1),
        Among(u"zaina", 72, 1),
        Among(u"tzaina", 78, 1),
        Among(u"kina", 72, 1),
        Among(u"mina", 72, 1),
        Among(u"garna", 72, 1),
        Among(u"una", 72, 1),
        Among(u"duna", 83, 1),
        Among(u"asuna", 83, 1),
        Among(u"tasuna", 85, 1),
        Among(u"ondoa", -1, 1),
        Among(u"kondoa", 87, 1),
        Among(u"ngoa", -1, 1),
        Among(u"zioa", -1, 1),
        Among(u"koa", -1, 1),
        Among(u"takoa", 91, 1),
        Among(u"zkoa", 91, 1),
        Among(u"noa", -1, 1),
        Among(u"zinoa", 94, 1),
        Among(u"aroa", -1, 1),
        Among(u"taroa", 96, 1),
        Among(u"zaroa", 96, 1),
        Among(u"eroa", -1, 1),
        Among(u"oroa", -1, 1),
        Among(u"osoa", -1, 1),
        Among(u"toa", -1, 1),
        Among(u"ttoa", 102, 1),
        Among(u"ztoa", 102, 1),
        Among(u"txoa", -1, 1),
        Among(u"tzoa", -1, 1),
        Among(u"\u00F1oa", -1, 1),
        Among(u"ra", -1, 1),
        Among(u"ara", 108, 1),
        Among(u"dara", 109, 1),
        Among(u"liara", 109, 1),
        Among(u"tiara", 109, 1),
        Among(u"tara", 109, 1),
        Among(u"etara", 113, 1),
        Among(u"tzara", 109, 1),
        Among(u"bera", 108, 1),
        Among(u"kera", 108, 1),
        Among(u"pera", 108, 1),
        Among(u"ora", 108, 2),
        Among(u"tzarra", 108, 1),
        Among(u"korra", 108, 1),
        Among(u"tra", 108, 1),
        Among(u"sa", -1, 1),
        Among(u"osa", 123, 1),
        Among(u"ta", -1, 1),
        Among(u"eta", 125, 1),
        Among(u"keta", 126, 1),
        Among(u"sta", 125, 1),
        Among(u"dua", -1, 1),
        Among(u"mendua", 129, 1),
        Among(u"ordua", 129, 1),
        Among(u"lekua", -1, 1),
        Among(u"burua", -1, 1),
        Among(u"durua", -1, 1),
        Among(u"tsua", -1, 1),
        Among(u"tua", -1, 1),
        Among(u"mentua", 136, 1),
        Among(u"estua", 136, 1),
        Among(u"txua", -1, 1),
        Among(u"zua", -1, 1),
        Among(u"tzua", 140, 1),
        Among(u"za", -1, 1),
        Among(u"eza", 142, 1),
        Among(u"eroza", 142, 1),
        Among(u"tza", 142, 2),
        Among(u"koitza", 145, 1),
        Among(u"antza", 145, 1),
        Among(u"gintza", 145, 1),
        Among(u"kintza", 145, 1),
        Among(u"kuntza", 145, 1),
        Among(u"gabe", -1, 1),
        Among(u"kabe", -1, 1),
        Among(u"kide", -1, 1),
        Among(u"alde", -1, 1),
        Among(u"kalde", 154, 1),
        Among(u"talde", 154, 1),
        Among(u"orde", -1, 1),
        Among(u"ge", -1, 1),
        Among(u"zale", -1, 1),
        Among(u"tzale", 159, 1),
        Among(u"gile", -1, 1),
        Among(u"eme", -1, 1),
        Among(u"kume", -1, 1),
        Among(u"ne", -1, 1),
        Among(u"zione", 164, 1),
        Among(u"une", 164, 1),
        Among(u"gune", 166, 1),
        Among(u"pe", -1, 1),
        Among(u"aurre", -1, 1),
        Among(u"te", -1, 1),
        Among(u"kote", 170, 1),
        Among(u"arte", 170, 1),
        Among(u"oste", 170, 1),
        Among(u"etxe", -1, 1),
        Among(u"gai", -1, 1),
        Among(u"di", -1, 1),
        Among(u"aldi", 176, 1),
        Among(u"taldi", 177, 1),
        Among(u"geldi", 176, 8),
        Among(u"handi", 176, 1),
        Among(u"mendi", 176, 1),
        Among(u"gei", -1, 1),
        Among(u"egi", -1, 1),
        Among(u"degi", 183, 1),
        Among(u"tegi", 183, 1),
        Among(u"nahi", -1, 1),
        Among(u"ohi", -1, 1),
        Among(u"ki", -1, 1),
        Among(u"toki", 188, 1),
        Among(u"oi", -1, 1),
        Among(u"goi", 190, 1),
        Among(u"koi", 190, 1),
        Among(u"ari", -1, 1),
        Among(u"kari", 193, 1),
        Among(u"lari", 193, 1),
        Among(u"tari", 193, 1),
        Among(u"garri", -1, 2),
        Among(u"larri", -1, 1),
        Among(u"kirri", -1, 1),
        Among(u"duri", -1, 1),
        Among(u"asi", -1, 1),
        Among(u"ti", -1, 1),
        Among(u"ontzi", -1, 1),
        Among(u"\u00F1i", -1, 1),
        Among(u"ak", -1, 1),
        Among(u"ek", -1, 1),
        Among(u"tarik", -1, 1),
        Among(u"gibel", -1, 1),
        Among(u"ail", -1, 1),
        Among(u"kail", 209, 1),
        Among(u"kan", -1, 1),
        Among(u"tan", -1, 1),
        Among(u"etan", 212, 1),
        Among(u"en", -1, 4),
        Among(u"ren", 214, 2),
        Among(u"garren", 215, 1),
        Among(u"gerren", 215, 1),
        Among(u"urren", 215, 1),
        Among(u"ten", 214, 4),
        Among(u"tzen", 214, 4),
        Among(u"zain", -1, 1),
        Among(u"tzain", 221, 1),
        Among(u"kin", -1, 1),
        Among(u"min", -1, 1),
        Among(u"dun", -1, 1),
        Among(u"asun", -1, 1),
        Among(u"tasun", 226, 1),
        Among(u"aizun", -1, 1),
        Among(u"ondo", -1, 1),
        Among(u"kondo", 229, 1),
        Among(u"go", -1, 1),
        Among(u"ngo", 231, 1),
        Among(u"zio", -1, 1),
        Among(u"ko", -1, 1),
        Among(u"trako", 234, 5),
        Among(u"tako", 234, 1),
        Among(u"etako", 236, 1),
        Among(u"eko", 234, 1),
        Among(u"tariko", 234, 1),
        Among(u"sko", 234, 1),
        Among(u"tuko", 234, 1),
        Among(u"minutuko", 241, 6),
        Among(u"zko", 234, 1),
        Among(u"no", -1, 1),
        Among(u"zino", 244, 1),
        Among(u"ro", -1, 1),
        Among(u"aro", 246, 1),
        Among(u"igaro", 247, 9),
        Among(u"taro", 247, 1),
        Among(u"zaro", 247, 1),
        Among(u"ero", 246, 1),
        Among(u"giro", 246, 1),
        Among(u"oro", 246, 1),
        Among(u"oso", -1, 1),
        Among(u"to", -1, 1),
        Among(u"tto", 255, 1),
        Among(u"zto", 255, 1),
        Among(u"txo", -1, 1),
        Among(u"tzo", -1, 1),
        Among(u"gintzo", 259, 1),
        Among(u"\u00F1o", -1, 1),
        Among(u"zp", -1, 1),
        Among(u"ar", -1, 1),
        Among(u"dar", 263, 1),
        Among(u"behar", 263, 1),
        Among(u"zehar", 263, 7),
        Among(u"liar", 263, 1),
        Among(u"tiar", 263, 1),
        Among(u"tar", 263, 1),
        Among(u"tzar", 263, 1),
        Among(u"or", -1, 2),
        Among(u"kor", 271, 1),
        Among(u"os", -1, 1),
        Among(u"ket", -1, 1),
        Among(u"du", -1, 1),
        Among(u"mendu", 275, 1),
        Among(u"ordu", 275, 1),
        Among(u"leku", -1, 1),
        Among(u"buru", -1, 2),
        Among(u"duru", -1, 1),
        Among(u"tsu", -1, 1),
        Among(u"tu", -1, 1),
        Among(u"tatu", 282, 4),
        Among(u"mentu", 282, 1),
        Among(u"estu", 282, 1),
        Among(u"txu", -1, 1),
        Among(u"zu", -1, 1),
        Among(u"tzu", 287, 1),
        Among(u"gintzu", 288, 1),
        Among(u"z", -1, 1),
        Among(u"ez", 290, 1),
        Among(u"eroz", 290, 1),
        Among(u"tz", 290, 1),
        Among(u"koitz", 293, 1)
    ]

    a_2 = [
        Among(u"zlea", -1, 2),
        Among(u"keria", -1, 1),
        Among(u"la", -1, 1),
        Among(u"era", -1, 1),
        Among(u"dade", -1, 1),
        Among(u"tade", -1, 1),
        Among(u"date", -1, 1),
        Among(u"tate", -1, 1),
        Among(u"gi", -1, 1),
        Among(u"ki", -1, 1),
        Among(u"ik", -1, 1),
        Among(u"lanik", 10, 1),
        Among(u"rik", 10, 1),
        Among(u"larik", 12, 1),
        Among(u"ztik", 10, 1),
        Among(u"go", -1, 1),
        Among(u"ro", -1, 1),
        Among(u"ero", 16, 1),
        Among(u"to", -1, 1)
    ]

    g_v = [17, 65, 16]

    I_p2 = 0
    I_p1 = 0
    I_pV = 0

    def __r_mark_regions(self):
        self.I_pV = self.limit
        self.I_p1 = self.limit
        self.I_p2 = self.limit
        v_1 = self.cursor
        try:
            try:
                v_2 = self.cursor
                try:
                    if not self.in_grouping(BasqueStemmer.g_v, 97, 117):
                        raise lab2()
                    try:
                        v_3 = self.cursor
                        try:
                            if not self.out_grouping(BasqueStemmer.g_v, 97, 117):
                                raise lab4()
                            if not self.go_out_grouping(BasqueStemmer.g_v, 97, 117):
                                raise lab4()
                            self.cursor += 1
                            raise lab3()
                        except lab4: pass
                        self.cursor = v_3
                        if not self.in_grouping(BasqueStemmer.g_v, 97, 117):
                            raise lab2()
                        if not self.go_in_grouping(BasqueStemmer.g_v, 97, 117):
                            raise lab2()
                        self.cursor += 1
                    except lab3: pass
                    raise lab1()
                except lab2: pass
                self.cursor = v_2
                if not self.out_grouping(BasqueStemmer.g_v, 97, 117):
                    raise lab0()
                try:
                    v_4 = self.cursor
                    try:
                        if not self.out_grouping(BasqueStemmer.g_v, 97, 117):
                            raise lab6()
                        if not self.go_out_grouping(BasqueStemmer.g_v, 97, 117):
                            raise lab6()
                        self.cursor += 1
                        raise lab5()
                    except lab6: pass
                    self.cursor = v_4
                    if not self.in_grouping(BasqueStemmer.g_v, 97, 117):
                        raise lab0()
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                except lab5: pass
            except lab1: pass
            self.I_pV = self.cursor
        except lab0: pass
        self.cursor = v_1
        v_5 = self.cursor
        try:
            if not self.go_out_grouping(BasqueStemmer.g_v, 97, 117):
                raise lab7()
            self.cursor += 1
            if not self.go_in_grouping(BasqueStemmer.g_v, 97, 117):
                raise lab7()
            self.cursor += 1
            self.I_p1 = self.cursor
            if not self.go_out_grouping(BasqueStemmer.g_v, 97, 117):
                raise lab7()
            self.cursor += 1
            if not self.go_in_grouping(BasqueStemmer.g_v, 97, 117):
                raise lab7()
            self.cursor += 1
            self.I_p2 = self.cursor
        except lab7: pass
        self.cursor = v_5
        return True

    def __r_RV(self):
        if not self.I_pV <= self.cursor:
            return False
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_aditzak(self):
        self.ket = self.cursor
        among_var = self.find_among_b(BasqueStemmer.a_0)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_RV():
                return False
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        elif among_var == 3:
            if not self.slice_from(u"atseden"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"arabera"):
                return False
        else:
            if not self.slice_from(u"baditu"):
                return False
        return True

    def __r_izenak(self):
        self.ket = self.cursor
        among_var = self.find_among_b(BasqueStemmer.a_1)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_RV():
                return False
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        elif among_var == 3:
            if not self.slice_from(u"jok"):
                return False
        elif among_var == 4:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        elif among_var == 5:
            if not self.slice_from(u"tra"):
                return False
        elif among_var == 6:
            if not self.slice_from(u"minutu"):
                return False
        elif among_var == 7:
            if not self.slice_from(u"zehar"):
                return False
        elif among_var == 8:
            if not self.slice_from(u"geldi"):
                return False
        elif among_var == 9:
            if not self.slice_from(u"igaro"):
                return False
        else:
            if not self.slice_from(u"aurka"):
                return False
        return True

    def __r_adjetiboak(self):
        self.ket = self.cursor
        among_var = self.find_among_b(BasqueStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_RV():
                return False
            if not self.slice_del():
                return False

        else:
            if not self.slice_from(u"z"):
                return False
        return True

    def _stem(self):
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        while True:
            v_2 = self.limit - self.cursor
            try:
                if not self.__r_aditzak():
                    raise lab0()
                continue
            except lab0: pass
            self.cursor = self.limit - v_2
            break
        while True:
            v_3 = self.limit - self.cursor
            try:
                if not self.__r_izenak():
                    raise lab1()
                continue
            except lab1: pass
            self.cursor = self.limit - v_3
            break
        v_4 = self.limit - self.cursor
        self.__r_adjetiboak()
        self.cursor = self.limit - v_4
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass
