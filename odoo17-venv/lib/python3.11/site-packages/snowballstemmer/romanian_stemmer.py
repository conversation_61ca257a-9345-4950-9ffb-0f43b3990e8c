# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class RomanianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"", -1, 3),
        Among(u"I", 0, 1),
        Among(u"U", 0, 2)
    ]

    a_1 = [
        Among(u"ea", -1, 3),
        Among(u"a\u0163ia", -1, 7),
        Among(u"aua", -1, 2),
        Among(u"iua", -1, 4),
        Among(u"a\u0163ie", -1, 7),
        Among(u"ele", -1, 3),
        Among(u"ile", -1, 5),
        Among(u"iile", 6, 4),
        Among(u"iei", -1, 4),
        Among(u"atei", -1, 6),
        Among(u"ii", -1, 4),
        Among(u"ului", -1, 1),
        Among(u"ul", -1, 1),
        Among(u"elor", -1, 3),
        Among(u"ilor", -1, 4),
        Among(u"iilor", 14, 4)
    ]

    a_2 = [
        Among(u"icala", -1, 4),
        Among(u"iciva", -1, 4),
        Among(u"ativa", -1, 5),
        Among(u"itiva", -1, 6),
        Among(u"icale", -1, 4),
        Among(u"a\u0163iune", -1, 5),
        Among(u"i\u0163iune", -1, 6),
        Among(u"atoare", -1, 5),
        Among(u"itoare", -1, 6),
        Among(u"\u0103toare", -1, 5),
        Among(u"icitate", -1, 4),
        Among(u"abilitate", -1, 1),
        Among(u"ibilitate", -1, 2),
        Among(u"ivitate", -1, 3),
        Among(u"icive", -1, 4),
        Among(u"ative", -1, 5),
        Among(u"itive", -1, 6),
        Among(u"icali", -1, 4),
        Among(u"atori", -1, 5),
        Among(u"icatori", 18, 4),
        Among(u"itori", -1, 6),
        Among(u"\u0103tori", -1, 5),
        Among(u"icitati", -1, 4),
        Among(u"abilitati", -1, 1),
        Among(u"ivitati", -1, 3),
        Among(u"icivi", -1, 4),
        Among(u"ativi", -1, 5),
        Among(u"itivi", -1, 6),
        Among(u"icit\u0103i", -1, 4),
        Among(u"abilit\u0103i", -1, 1),
        Among(u"ivit\u0103i", -1, 3),
        Among(u"icit\u0103\u0163i", -1, 4),
        Among(u"abilit\u0103\u0163i", -1, 1),
        Among(u"ivit\u0103\u0163i", -1, 3),
        Among(u"ical", -1, 4),
        Among(u"ator", -1, 5),
        Among(u"icator", 35, 4),
        Among(u"itor", -1, 6),
        Among(u"\u0103tor", -1, 5),
        Among(u"iciv", -1, 4),
        Among(u"ativ", -1, 5),
        Among(u"itiv", -1, 6),
        Among(u"ical\u0103", -1, 4),
        Among(u"iciv\u0103", -1, 4),
        Among(u"ativ\u0103", -1, 5),
        Among(u"itiv\u0103", -1, 6)
    ]

    a_3 = [
        Among(u"ica", -1, 1),
        Among(u"abila", -1, 1),
        Among(u"ibila", -1, 1),
        Among(u"oasa", -1, 1),
        Among(u"ata", -1, 1),
        Among(u"ita", -1, 1),
        Among(u"anta", -1, 1),
        Among(u"ista", -1, 3),
        Among(u"uta", -1, 1),
        Among(u"iva", -1, 1),
        Among(u"ic", -1, 1),
        Among(u"ice", -1, 1),
        Among(u"abile", -1, 1),
        Among(u"ibile", -1, 1),
        Among(u"isme", -1, 3),
        Among(u"iune", -1, 2),
        Among(u"oase", -1, 1),
        Among(u"ate", -1, 1),
        Among(u"itate", 17, 1),
        Among(u"ite", -1, 1),
        Among(u"ante", -1, 1),
        Among(u"iste", -1, 3),
        Among(u"ute", -1, 1),
        Among(u"ive", -1, 1),
        Among(u"ici", -1, 1),
        Among(u"abili", -1, 1),
        Among(u"ibili", -1, 1),
        Among(u"iuni", -1, 2),
        Among(u"atori", -1, 1),
        Among(u"osi", -1, 1),
        Among(u"ati", -1, 1),
        Among(u"itati", 30, 1),
        Among(u"iti", -1, 1),
        Among(u"anti", -1, 1),
        Among(u"isti", -1, 3),
        Among(u"uti", -1, 1),
        Among(u"i\u015Fti", -1, 3),
        Among(u"ivi", -1, 1),
        Among(u"it\u0103i", -1, 1),
        Among(u"o\u015Fi", -1, 1),
        Among(u"it\u0103\u0163i", -1, 1),
        Among(u"abil", -1, 1),
        Among(u"ibil", -1, 1),
        Among(u"ism", -1, 3),
        Among(u"ator", -1, 1),
        Among(u"os", -1, 1),
        Among(u"at", -1, 1),
        Among(u"it", -1, 1),
        Among(u"ant", -1, 1),
        Among(u"ist", -1, 3),
        Among(u"ut", -1, 1),
        Among(u"iv", -1, 1),
        Among(u"ic\u0103", -1, 1),
        Among(u"abil\u0103", -1, 1),
        Among(u"ibil\u0103", -1, 1),
        Among(u"oas\u0103", -1, 1),
        Among(u"at\u0103", -1, 1),
        Among(u"it\u0103", -1, 1),
        Among(u"ant\u0103", -1, 1),
        Among(u"ist\u0103", -1, 3),
        Among(u"ut\u0103", -1, 1),
        Among(u"iv\u0103", -1, 1)
    ]

    a_4 = [
        Among(u"ea", -1, 1),
        Among(u"ia", -1, 1),
        Among(u"esc", -1, 1),
        Among(u"\u0103sc", -1, 1),
        Among(u"ind", -1, 1),
        Among(u"\u00E2nd", -1, 1),
        Among(u"are", -1, 1),
        Among(u"ere", -1, 1),
        Among(u"ire", -1, 1),
        Among(u"\u00E2re", -1, 1),
        Among(u"se", -1, 2),
        Among(u"ase", 10, 1),
        Among(u"sese", 10, 2),
        Among(u"ise", 10, 1),
        Among(u"use", 10, 1),
        Among(u"\u00E2se", 10, 1),
        Among(u"e\u015Fte", -1, 1),
        Among(u"\u0103\u015Fte", -1, 1),
        Among(u"eze", -1, 1),
        Among(u"ai", -1, 1),
        Among(u"eai", 19, 1),
        Among(u"iai", 19, 1),
        Among(u"sei", -1, 2),
        Among(u"e\u015Fti", -1, 1),
        Among(u"\u0103\u015Fti", -1, 1),
        Among(u"ui", -1, 1),
        Among(u"ezi", -1, 1),
        Among(u"\u00E2i", -1, 1),
        Among(u"a\u015Fi", -1, 1),
        Among(u"se\u015Fi", -1, 2),
        Among(u"ase\u015Fi", 29, 1),
        Among(u"sese\u015Fi", 29, 2),
        Among(u"ise\u015Fi", 29, 1),
        Among(u"use\u015Fi", 29, 1),
        Among(u"\u00E2se\u015Fi", 29, 1),
        Among(u"i\u015Fi", -1, 1),
        Among(u"u\u015Fi", -1, 1),
        Among(u"\u00E2\u015Fi", -1, 1),
        Among(u"a\u0163i", -1, 2),
        Among(u"ea\u0163i", 38, 1),
        Among(u"ia\u0163i", 38, 1),
        Among(u"e\u0163i", -1, 2),
        Among(u"i\u0163i", -1, 2),
        Among(u"\u00E2\u0163i", -1, 2),
        Among(u"ar\u0103\u0163i", -1, 1),
        Among(u"ser\u0103\u0163i", -1, 2),
        Among(u"aser\u0103\u0163i", 45, 1),
        Among(u"seser\u0103\u0163i", 45, 2),
        Among(u"iser\u0103\u0163i", 45, 1),
        Among(u"user\u0103\u0163i", 45, 1),
        Among(u"\u00E2ser\u0103\u0163i", 45, 1),
        Among(u"ir\u0103\u0163i", -1, 1),
        Among(u"ur\u0103\u0163i", -1, 1),
        Among(u"\u00E2r\u0103\u0163i", -1, 1),
        Among(u"am", -1, 1),
        Among(u"eam", 54, 1),
        Among(u"iam", 54, 1),
        Among(u"em", -1, 2),
        Among(u"asem", 57, 1),
        Among(u"sesem", 57, 2),
        Among(u"isem", 57, 1),
        Among(u"usem", 57, 1),
        Among(u"\u00E2sem", 57, 1),
        Among(u"im", -1, 2),
        Among(u"\u00E2m", -1, 2),
        Among(u"\u0103m", -1, 2),
        Among(u"ar\u0103m", 65, 1),
        Among(u"ser\u0103m", 65, 2),
        Among(u"aser\u0103m", 67, 1),
        Among(u"seser\u0103m", 67, 2),
        Among(u"iser\u0103m", 67, 1),
        Among(u"user\u0103m", 67, 1),
        Among(u"\u00E2ser\u0103m", 67, 1),
        Among(u"ir\u0103m", 65, 1),
        Among(u"ur\u0103m", 65, 1),
        Among(u"\u00E2r\u0103m", 65, 1),
        Among(u"au", -1, 1),
        Among(u"eau", 76, 1),
        Among(u"iau", 76, 1),
        Among(u"indu", -1, 1),
        Among(u"\u00E2ndu", -1, 1),
        Among(u"ez", -1, 1),
        Among(u"easc\u0103", -1, 1),
        Among(u"ar\u0103", -1, 1),
        Among(u"ser\u0103", -1, 2),
        Among(u"aser\u0103", 84, 1),
        Among(u"seser\u0103", 84, 2),
        Among(u"iser\u0103", 84, 1),
        Among(u"user\u0103", 84, 1),
        Among(u"\u00E2ser\u0103", 84, 1),
        Among(u"ir\u0103", -1, 1),
        Among(u"ur\u0103", -1, 1),
        Among(u"\u00E2r\u0103", -1, 1),
        Among(u"eaz\u0103", -1, 1)
    ]

    a_5 = [
        Among(u"a", -1, 1),
        Among(u"e", -1, 1),
        Among(u"ie", 1, 1),
        Among(u"i", -1, 1),
        Among(u"\u0103", -1, 1)
    ]

    g_v = [17, 65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 32, 0, 0, 4]

    B_standard_suffix_removed = False
    I_p2 = 0
    I_p1 = 0
    I_pV = 0

    def __r_prelude(self):
        while True:
            v_1 = self.cursor
            try:
                try:
                    while True:
                        v_2 = self.cursor
                        try:
                            if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                                raise lab2()
                            self.bra = self.cursor
                            try:
                                v_3 = self.cursor
                                try:
                                    if not self.eq_s(u"u"):
                                        raise lab4()
                                    self.ket = self.cursor
                                    if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                                        raise lab4()
                                    if not self.slice_from(u"U"):
                                        return False
                                    raise lab3()
                                except lab4: pass
                                self.cursor = v_3
                                if not self.eq_s(u"i"):
                                    raise lab2()
                                self.ket = self.cursor
                                if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                                    raise lab2()
                                if not self.slice_from(u"I"):
                                    return False
                            except lab3: pass
                            self.cursor = v_2
                            raise lab1()
                        except lab2: pass
                        self.cursor = v_2
                        if self.cursor >= self.limit:
                            raise lab0()
                        self.cursor += 1
                except lab1: pass
                continue
            except lab0: pass
            self.cursor = v_1
            break
        return True

    def __r_mark_regions(self):
        self.I_pV = self.limit
        self.I_p1 = self.limit
        self.I_p2 = self.limit
        v_1 = self.cursor
        try:
            try:
                v_2 = self.cursor
                try:
                    if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                        raise lab2()
                    try:
                        v_3 = self.cursor
                        try:
                            if not self.out_grouping(RomanianStemmer.g_v, 97, 259):
                                raise lab4()
                            if not self.go_out_grouping(RomanianStemmer.g_v, 97, 259):
                                raise lab4()
                            self.cursor += 1
                            raise lab3()
                        except lab4: pass
                        self.cursor = v_3
                        if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                            raise lab2()
                        if not self.go_in_grouping(RomanianStemmer.g_v, 97, 259):
                            raise lab2()
                        self.cursor += 1
                    except lab3: pass
                    raise lab1()
                except lab2: pass
                self.cursor = v_2
                if not self.out_grouping(RomanianStemmer.g_v, 97, 259):
                    raise lab0()
                try:
                    v_4 = self.cursor
                    try:
                        if not self.out_grouping(RomanianStemmer.g_v, 97, 259):
                            raise lab6()
                        if not self.go_out_grouping(RomanianStemmer.g_v, 97, 259):
                            raise lab6()
                        self.cursor += 1
                        raise lab5()
                    except lab6: pass
                    self.cursor = v_4
                    if not self.in_grouping(RomanianStemmer.g_v, 97, 259):
                        raise lab0()
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                except lab5: pass
            except lab1: pass
            self.I_pV = self.cursor
        except lab0: pass
        self.cursor = v_1
        v_5 = self.cursor
        try:
            if not self.go_out_grouping(RomanianStemmer.g_v, 97, 259):
                raise lab7()
            self.cursor += 1
            if not self.go_in_grouping(RomanianStemmer.g_v, 97, 259):
                raise lab7()
            self.cursor += 1
            self.I_p1 = self.cursor
            if not self.go_out_grouping(RomanianStemmer.g_v, 97, 259):
                raise lab7()
            self.cursor += 1
            if not self.go_in_grouping(RomanianStemmer.g_v, 97, 259):
                raise lab7()
            self.cursor += 1
            self.I_p2 = self.cursor
        except lab7: pass
        self.cursor = v_5
        return True

    def __r_postlude(self):
        while True:
            v_1 = self.cursor
            try:
                self.bra = self.cursor
                among_var = self.find_among(RomanianStemmer.a_0)
                if among_var == 0:
                    raise lab0()
                self.ket = self.cursor
                if among_var == 1:
                    if not self.slice_from(u"i"):
                        return False
                elif among_var == 2:
                    if not self.slice_from(u"u"):
                        return False
                else:
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                continue
            except lab0: pass
            self.cursor = v_1
            break
        return True

    def __r_RV(self):
        if not self.I_pV <= self.cursor:
            return False
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_step_0(self):
        self.ket = self.cursor
        among_var = self.find_among_b(RomanianStemmer.a_1)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"a"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"e"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"i"):
                return False
        elif among_var == 5:
            v_1 = self.limit - self.cursor
            try:
                if not self.eq_s_b(u"ab"):
                    raise lab0()
                return False
            except lab0: pass
            self.cursor = self.limit - v_1
            if not self.slice_from(u"i"):
                return False
        elif among_var == 6:
            if not self.slice_from(u"at"):
                return False
        else:
            if not self.slice_from(u"a\u0163i"):
                return False
        return True

    def __r_combo_suffix(self):
        v_1 = self.limit - self.cursor
        self.ket = self.cursor
        among_var = self.find_among_b(RomanianStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_from(u"abil"):
                return False
        elif among_var == 2:
            if not self.slice_from(u"ibil"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"iv"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"ic"):
                return False
        elif among_var == 5:
            if not self.slice_from(u"at"):
                return False
        else:
            if not self.slice_from(u"it"):
                return False
        self.B_standard_suffix_removed = True
        self.cursor = self.limit - v_1
        return True

    def __r_standard_suffix(self):
        self.B_standard_suffix_removed = False
        while True:
            v_1 = self.limit - self.cursor
            try:
                if not self.__r_combo_suffix():
                    raise lab0()
                continue
            except lab0: pass
            self.cursor = self.limit - v_1
            break
        self.ket = self.cursor
        among_var = self.find_among_b(RomanianStemmer.a_3)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R2():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.eq_s_b(u"\u0163"):
                return False
            self.bra = self.cursor
            if not self.slice_from(u"t"):
                return False
        else:
            if not self.slice_from(u"ist"):
                return False
        self.B_standard_suffix_removed = True
        return True

    def __r_verb_suffix(self):
        if self.cursor < self.I_pV:
            return False
        v_2 = self.limit_backward
        self.limit_backward = self.I_pV
        self.ket = self.cursor
        among_var = self.find_among_b(RomanianStemmer.a_4)
        if among_var == 0:
            self.limit_backward = v_2
            return False
        self.bra = self.cursor
        if among_var == 1:
            try:
                v_3 = self.limit - self.cursor
                try:
                    if not self.out_grouping_b(RomanianStemmer.g_v, 97, 259):
                        raise lab1()
                    raise lab0()
                except lab1: pass
                self.cursor = self.limit - v_3
                if not self.eq_s_b(u"u"):
                    self.limit_backward = v_2
                    return False
            except lab0: pass
            if not self.slice_del():
                return False

        else:
            if not self.slice_del():
                return False

        self.limit_backward = v_2
        return True

    def __r_vowel_suffix(self):
        self.ket = self.cursor
        if self.find_among_b(RomanianStemmer.a_5) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_RV():
            return False
        if not self.slice_del():
            return False

        return True

    def _stem(self):
        v_1 = self.cursor
        self.__r_prelude()
        self.cursor = v_1
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_3 = self.limit - self.cursor
        self.__r_step_0()
        self.cursor = self.limit - v_3
        v_4 = self.limit - self.cursor
        self.__r_standard_suffix()
        self.cursor = self.limit - v_4
        v_5 = self.limit - self.cursor
        try:
            try:
                v_6 = self.limit - self.cursor
                try:
                    if not self.B_standard_suffix_removed:
                        raise lab2()
                    raise lab1()
                except lab2: pass
                self.cursor = self.limit - v_6
                if not self.__r_verb_suffix():
                    raise lab0()
            except lab1: pass
        except lab0: pass
        self.cursor = self.limit - v_5
        v_7 = self.limit - self.cursor
        self.__r_vowel_suffix()
        self.cursor = self.limit - v_7
        self.cursor = self.limit_backward
        v_8 = self.cursor
        self.__r_postlude()
        self.cursor = v_8
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass
