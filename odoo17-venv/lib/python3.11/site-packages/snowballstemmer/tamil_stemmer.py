# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class TamilStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B99", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9E", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BA8", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BAE", -1, -1),
        Among(u"\u0BAF", -1, -1),
        Among(u"\u0BB5", -1, -1)
    ]

    a_1 = [
        Among(u"\u0BA8\u0BCD\u0BA4", -1, -1),
        Among(u"\u0BA8\u0BCD\u0BA4\u0BCD", -1, -1),
        Among(u"\u0BA8\u0BCD", -1, -1)
    ]

    a_2 = [
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_3 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_4 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_5 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_6 = [
        Among(u"\u0BAF", -1, -1),
        Among(u"\u0BB0", -1, -1),
        Among(u"\u0BB2", -1, -1),
        Among(u"\u0BB3", -1, -1),
        Among(u"\u0BB4", -1, -1),
        Among(u"\u0BB5", -1, -1)
    ]

    a_7 = [
        Among(u"\u0B99", -1, -1),
        Among(u"\u0B9E", -1, -1),
        Among(u"\u0BA3", -1, -1),
        Among(u"\u0BA8", -1, -1),
        Among(u"\u0BA9", -1, -1),
        Among(u"\u0BAE", -1, -1)
    ]

    a_8 = [
        Among(u"\u0BAF", -1, -1),
        Among(u"\u0BB5", -1, -1),
        Among(u"\u0BB5\u0BCD", -1, -1)
    ]

    a_9 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_10 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_11 = [
        Among(u"\u0B85", -1, -1),
        Among(u"\u0B87", -1, -1),
        Among(u"\u0B89", -1, -1)
    ]

    a_12 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B99", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9E", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BA8", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BAE", -1, -1),
        Among(u"\u0BAF", -1, -1),
        Among(u"\u0BB5", -1, -1)
    ]

    a_13 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_14 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BCB", -1, -1)
    ]

    a_15 = [
        Among(u"\u0BAA\u0BBF", -1, -1),
        Among(u"\u0BB5\u0BBF", -1, -1)
    ]

    a_16 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_17 = [
        Among(u"\u0BAA\u0B9F\u0BCD\u0B9F", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BCD\u0B9F\u0BA3", -1, -1),
        Among(u"\u0BA4\u0BBE\u0BA9", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BBF\u0BA4\u0BBE\u0BA9", 2, -1),
        Among(u"\u0B95\u0BC1\u0BB0\u0BBF\u0BAF", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BBF", -1, -1),
        Among(u"\u0BAA\u0BB1\u0BCD\u0BB1\u0BBF", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BC1", -1, -1),
        Among(u"\u0BB5\u0BBF\u0B9F\u0BC1", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BCD\u0B9F\u0BC1", -1, -1),
        Among(u"\u0BB5\u0BBF\u0B9F\u0BCD\u0B9F\u0BC1", -1, -1),
        Among(u"\u0BAA\u0B9F\u0BCD\u0B9F\u0BA4\u0BC1", -1, -1),
        Among(u"\u0BC6\u0BB2\u0BCD\u0BB2\u0BBE\u0BAE\u0BCD", -1, -1)
    ]

    a_18 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_19 = [
        Among(u"\u0B95", -1, -1),
        Among(u"\u0B9A", -1, -1),
        Among(u"\u0B9F", -1, -1),
        Among(u"\u0BA4", -1, -1),
        Among(u"\u0BAA", -1, -1),
        Among(u"\u0BB1", -1, -1)
    ]

    a_20 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_21 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_22 = [
        Among(u"\u0BAA\u0B9F\u0BC1", -1, -1),
        Among(u"\u0B95\u0BCA\u0BA3\u0BCD\u0B9F\u0BBF\u0BB0\u0BCD", -1, -1)
    ]

    a_23 = [
        Among(u"\u0B85", -1, -1),
        Among(u"\u0B86", -1, -1),
        Among(u"\u0B87", -1, -1),
        Among(u"\u0B88", -1, -1),
        Among(u"\u0B89", -1, -1),
        Among(u"\u0B8A", -1, -1),
        Among(u"\u0B8E", -1, -1),
        Among(u"\u0B8F", -1, -1),
        Among(u"\u0B90", -1, -1),
        Among(u"\u0B92", -1, -1),
        Among(u"\u0B93", -1, -1),
        Among(u"\u0B94", -1, -1)
    ]

    a_24 = [
        Among(u"\u0BBE", -1, -1),
        Among(u"\u0BBF", -1, -1),
        Among(u"\u0BC0", -1, -1),
        Among(u"\u0BC1", -1, -1),
        Among(u"\u0BC2", -1, -1),
        Among(u"\u0BC6", -1, -1),
        Among(u"\u0BC7", -1, -1),
        Among(u"\u0BC8", -1, -1)
    ]

    a_25 = [
        Among(u"\u0B95\u0BBF\u0BB1", -1, -1),
        Among(u"\u0B95\u0BBF\u0BA9\u0BCD\u0BB1", -1, -1),
        Among(u"\u0BBE\u0BA8\u0BBF\u0BA9\u0BCD\u0BB1", -1, -1),
        Among(u"\u0B95\u0BBF\u0BB1\u0BCD", -1, -1),
        Among(u"\u0B95\u0BBF\u0BA9\u0BCD\u0BB1\u0BCD", -1, -1),
        Among(u"\u0BBE\u0BA8\u0BBF\u0BA9\u0BCD\u0BB1\u0BCD", -1, -1)
    ]

    B_found_vetrumai_urupu = False
    B_found_a_match = False

    def __r_has_min_length(self):
        if not len(self.current) > 4:
            return False
        return True

    def __r_fix_va_start(self):
        try:
            v_1 = self.cursor
            try:
                v_2 = self.cursor
                v_3 = self.cursor
                try:
                    if not self.eq_s(u"\u0BB5\u0BCB"):
                        self.cursor = v_3
                        raise lab2()
                except lab2: pass
                self.cursor = v_2
                self.bra = self.cursor
                if not self.eq_s(u"\u0BB5\u0BCB"):
                    raise lab1()
                self.ket = self.cursor
                if not self.slice_from(u"\u0B93"):
                    return False
                raise lab0()
            except lab1: pass
            self.cursor = v_1
            try:
                v_4 = self.cursor
                v_5 = self.cursor
                try:
                    if not self.eq_s(u"\u0BB5\u0BCA"):
                        self.cursor = v_5
                        raise lab4()
                except lab4: pass
                self.cursor = v_4
                self.bra = self.cursor
                if not self.eq_s(u"\u0BB5\u0BCA"):
                    raise lab3()
                self.ket = self.cursor
                if not self.slice_from(u"\u0B92"):
                    return False
                raise lab0()
            except lab3: pass
            self.cursor = v_1
            try:
                v_6 = self.cursor
                v_7 = self.cursor
                try:
                    if not self.eq_s(u"\u0BB5\u0BC1"):
                        self.cursor = v_7
                        raise lab6()
                except lab6: pass
                self.cursor = v_6
                self.bra = self.cursor
                if not self.eq_s(u"\u0BB5\u0BC1"):
                    raise lab5()
                self.ket = self.cursor
                if not self.slice_from(u"\u0B89"):
                    return False
                raise lab0()
            except lab5: pass
            self.cursor = v_1
            v_8 = self.cursor
            v_9 = self.cursor
            try:
                if not self.eq_s(u"\u0BB5\u0BC2"):
                    self.cursor = v_9
                    raise lab7()
            except lab7: pass
            self.cursor = v_8
            self.bra = self.cursor
            if not self.eq_s(u"\u0BB5\u0BC2"):
                return False
            self.ket = self.cursor
            if not self.slice_from(u"\u0B8A"):
                return False
        except lab0: pass
        return True

    def __r_fix_endings(self):
        v_1 = self.cursor
        try:
            while True:
                v_2 = self.cursor
                try:
                    if not self.__r_fix_ending():
                        raise lab1()
                    continue
                except lab1: pass
                self.cursor = v_2
                break
        except lab0: pass
        self.cursor = v_1
        return True

    def __r_remove_question_prefixes(self):
        self.bra = self.cursor
        if not self.eq_s(u"\u0B8E"):
            return False
        if self.find_among(TamilStemmer.a_0) == 0:
            return False
        if not self.eq_s(u"\u0BCD"):
            return False
        self.ket = self.cursor
        if not self.slice_del():
            return False

        v_1 = self.cursor
        self.__r_fix_va_start()
        self.cursor = v_1
        return True

    def __r_fix_ending(self):
        if not len(self.current) > 3:
            return False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        try:
            v_1 = self.limit - self.cursor
            try:
                self.ket = self.cursor
                if self.find_among_b(TamilStemmer.a_1) == 0:
                    raise lab1()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab1: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BAF\u0BCD"):
                    raise lab2()
                v_2 = self.limit - self.cursor
                if self.find_among_b(TamilStemmer.a_2) == 0:
                    raise lab2()
                self.cursor = self.limit - v_2
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab2: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                try:
                    v_3 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0B9F\u0BCD\u0BAA\u0BCD"):
                            raise lab5()
                        raise lab4()
                    except lab5: pass
                    self.cursor = self.limit - v_3
                    if not self.eq_s_b(u"\u0B9F\u0BCD\u0B95\u0BCD"):
                        raise lab3()
                except lab4: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u0BB3\u0BCD"):
                    return False
                raise lab0()
            except lab3: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BA9\u0BCD\u0BB1\u0BCD"):
                    raise lab6()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BB2\u0BCD"):
                    return False
                raise lab0()
            except lab6: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BB1\u0BCD\u0B95\u0BCD"):
                    raise lab7()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BB2\u0BCD"):
                    return False
                raise lab0()
            except lab7: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0B9F\u0BCD\u0B9F\u0BCD"):
                    raise lab8()
                self.bra = self.cursor
                if not self.slice_from(u"\u0B9F\u0BC1"):
                    return False
                raise lab0()
            except lab8: pass
            self.cursor = self.limit - v_1
            try:
                if not self.B_found_vetrumai_urupu:
                    raise lab9()
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BA4\u0BCD\u0BA4\u0BCD"):
                    raise lab9()
                v_4 = self.limit - self.cursor
                v_5 = self.limit - self.cursor
                try:
                    if not self.eq_s_b(u"\u0BC8"):
                        raise lab10()
                    raise lab9()
                except lab10: pass
                self.cursor = self.limit - v_5
                self.cursor = self.limit - v_4
                self.bra = self.cursor
                if not self.slice_from(u"\u0BAE\u0BCD"):
                    return False
                self.bra = self.cursor
                raise lab0()
            except lab9: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                try:
                    v_6 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0B95\u0BCD"):
                            raise lab13()
                        raise lab12()
                    except lab13: pass
                    self.cursor = self.limit - v_6
                    if not self.eq_s_b(u"\u0BC1\u0B95\u0BCD\u0B95\u0BCD"):
                        raise lab11()
                except lab12: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                raise lab0()
            except lab11: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab14()
                if self.find_among_b(TamilStemmer.a_3) == 0:
                    raise lab14()
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab14()
                if self.find_among_b(TamilStemmer.a_4) == 0:
                    raise lab14()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab14: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BC1\u0B95\u0BCD"):
                    raise lab15()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                raise lab0()
            except lab15: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab16()
                if self.find_among_b(TamilStemmer.a_5) == 0:
                    raise lab16()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab16: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab17()
                try:
                    v_7 = self.limit - self.cursor
                    try:
                        if self.find_among_b(TamilStemmer.a_6) == 0:
                            raise lab19()
                        raise lab18()
                    except lab19: pass
                    self.cursor = self.limit - v_7
                    if self.find_among_b(TamilStemmer.a_7) == 0:
                        raise lab17()
                except lab18: pass
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab17()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                raise lab0()
            except lab17: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if self.find_among_b(TamilStemmer.a_8) == 0:
                    raise lab20()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab20: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BA9\u0BC1"):
                    raise lab21()
                v_8 = self.limit - self.cursor
                v_9 = self.limit - self.cursor
                try:
                    if self.find_among_b(TamilStemmer.a_9) == 0:
                        raise lab22()
                    raise lab21()
                except lab22: pass
                self.cursor = self.limit - v_9
                self.cursor = self.limit - v_8
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab21: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0B99\u0BCD"):
                    raise lab23()
                v_10 = self.limit - self.cursor
                v_11 = self.limit - self.cursor
                try:
                    if not self.eq_s_b(u"\u0BC8"):
                        raise lab24()
                    raise lab23()
                except lab24: pass
                self.cursor = self.limit - v_11
                self.cursor = self.limit - v_10
                self.bra = self.cursor
                if not self.slice_from(u"\u0BAE\u0BCD"):
                    return False
                raise lab0()
            except lab23: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0B99\u0BCD"):
                    raise lab25()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                raise lab0()
            except lab25: pass
            self.cursor = self.limit - v_1
            self.ket = self.cursor
            if not self.eq_s_b(u"\u0BCD"):
                return False
            v_12 = self.limit - self.cursor
            try:
                v_13 = self.limit - self.cursor
                try:
                    if self.find_among_b(TamilStemmer.a_10) == 0:
                        raise lab27()
                    raise lab26()
                except lab27: pass
                self.cursor = self.limit - v_13
                if not self.eq_s_b(u"\u0BCD"):
                    return False
            except lab26: pass
            self.cursor = self.limit - v_12
            self.bra = self.cursor
            if not self.slice_del():
                return False

        except lab0: pass
        self.cursor = self.limit_backward
        return True

    def __r_remove_pronoun_prefixes(self):
        self.B_found_a_match = False
        self.bra = self.cursor
        if self.find_among(TamilStemmer.a_11) == 0:
            return False
        if self.find_among(TamilStemmer.a_12) == 0:
            return False
        if not self.eq_s(u"\u0BCD"):
            return False
        self.ket = self.cursor
        if not self.slice_del():
            return False

        self.B_found_a_match = True
        v_1 = self.cursor
        self.__r_fix_va_start()
        self.cursor = v_1
        return True

    def __r_remove_plural_suffix(self):
        self.B_found_a_match = False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        try:
            v_1 = self.limit - self.cursor
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BC1\u0B99\u0BCD\u0B95\u0BB3\u0BCD"):
                    raise lab1()
                v_2 = self.limit - self.cursor
                v_3 = self.limit - self.cursor
                try:
                    if self.find_among_b(TamilStemmer.a_13) == 0:
                        raise lab2()
                    raise lab1()
                except lab2: pass
                self.cursor = self.limit - v_3
                self.cursor = self.limit - v_2
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                raise lab0()
            except lab1: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BB1\u0BCD\u0B95\u0BB3\u0BCD"):
                    raise lab3()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BB2\u0BCD"):
                    return False
                raise lab0()
            except lab3: pass
            self.cursor = self.limit - v_1
            try:
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0B9F\u0BCD\u0B95\u0BB3\u0BCD"):
                    raise lab4()
                self.bra = self.cursor
                if not self.slice_from(u"\u0BB3\u0BCD"):
                    return False
                raise lab0()
            except lab4: pass
            self.cursor = self.limit - v_1
            self.ket = self.cursor
            if not self.eq_s_b(u"\u0B95\u0BB3\u0BCD"):
                return False
            self.bra = self.cursor
            if not self.slice_del():
                return False

        except lab0: pass
        self.B_found_a_match = True
        self.cursor = self.limit_backward
        return True

    def __r_remove_question_suffixes(self):
        if not self.__r_has_min_length():
            return False
        self.B_found_a_match = False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_1 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            if self.find_among_b(TamilStemmer.a_14) == 0:
                raise lab0()
            self.bra = self.cursor
            if not self.slice_from(u"\u0BCD"):
                return False
            self.B_found_a_match = True
        except lab0: pass
        self.cursor = self.limit - v_1
        self.cursor = self.limit_backward
        self.__r_fix_endings()
        return True

    def __r_remove_command_suffixes(self):
        if not self.__r_has_min_length():
            return False
        self.B_found_a_match = False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        self.ket = self.cursor
        if self.find_among_b(TamilStemmer.a_15) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        self.B_found_a_match = True
        self.cursor = self.limit_backward
        return True

    def __r_remove_um(self):
        self.B_found_a_match = False
        if not self.__r_has_min_length():
            return False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        self.ket = self.cursor
        if not self.eq_s_b(u"\u0BC1\u0BAE\u0BCD"):
            return False
        self.bra = self.cursor
        if not self.slice_from(u"\u0BCD"):
            return False
        self.B_found_a_match = True
        self.cursor = self.limit_backward
        v_1 = self.cursor
        self.__r_fix_ending()
        self.cursor = v_1
        return True

    def __r_remove_common_word_endings(self):
        self.B_found_a_match = False
        if not self.__r_has_min_length():
            return False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        try:
            v_1 = self.limit - self.cursor
            try:
                v_2 = self.limit - self.cursor
                self.ket = self.cursor
                try:
                    v_3 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0B9F\u0BA9\u0BCD"):
                            raise lab3()
                        raise lab2()
                    except lab3: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BB2\u0BCD\u0BB2\u0BC8"):
                            raise lab4()
                        raise lab2()
                    except lab4: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0B9F\u0BAE\u0BCD"):
                            raise lab5()
                        raise lab2()
                    except lab5: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BA9\u0BCD\u0BB1\u0BBF"):
                            raise lab6()
                        raise lab2()
                    except lab6: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BBE\u0B95\u0BBF"):
                            raise lab7()
                        raise lab2()
                    except lab7: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BBE\u0B95\u0BBF\u0BAF"):
                            raise lab8()
                        raise lab2()
                    except lab8: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC6\u0BA9\u0BCD\u0BB1\u0BC1"):
                            raise lab9()
                        raise lab2()
                    except lab9: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0BB3\u0BCD\u0BB3"):
                            raise lab10()
                        raise lab2()
                    except lab10: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0B9F\u0BC8\u0BAF"):
                            raise lab11()
                        raise lab2()
                    except lab11: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0B9F\u0BC8"):
                            raise lab12()
                        raise lab2()
                    except lab12: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC6\u0BA9\u0BC1\u0BAE\u0BCD"):
                            raise lab13()
                        raise lab2()
                    except lab13: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BB2\u0BCD\u0BB2"):
                            raise lab14()
                        v_4 = self.limit - self.cursor
                        v_5 = self.limit - self.cursor
                        try:
                            if self.find_among_b(TamilStemmer.a_16) == 0:
                                raise lab15()
                            raise lab14()
                        except lab15: pass
                        self.cursor = self.limit - v_5
                        self.cursor = self.limit - v_4
                        raise lab2()
                    except lab14: pass
                    self.cursor = self.limit - v_3
                    try:
                        if not self.eq_s_b(u"\u0BC6\u0BA9"):
                            raise lab16()
                        raise lab2()
                    except lab16: pass
                    self.cursor = self.limit - v_3
                    if not self.eq_s_b(u"\u0BBE\u0B95\u0BBF"):
                        raise lab1()
                except lab2: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                self.B_found_a_match = True
                self.cursor = self.limit - v_2
                raise lab0()
            except lab1: pass
            self.cursor = self.limit - v_1
            v_6 = self.limit - self.cursor
            self.ket = self.cursor
            if self.find_among_b(TamilStemmer.a_17) == 0:
                return False
            self.bra = self.cursor
            if not self.slice_del():
                return False

            self.B_found_a_match = True
            self.cursor = self.limit - v_6
        except lab0: pass
        self.cursor = self.limit_backward
        self.__r_fix_endings()
        return True

    def __r_remove_vetrumai_urupukal(self):
        self.B_found_a_match = False
        self.B_found_vetrumai_urupu = False
        if not self.__r_has_min_length():
            return False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        try:
            v_1 = self.limit - self.cursor
            try:
                v_2 = self.limit - self.cursor
                self.ket = self.cursor
                if not self.eq_s_b(u"\u0BA9\u0BC8"):
                    raise lab1()
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                self.cursor = self.limit - v_2
                raise lab0()
            except lab1: pass
            self.cursor = self.limit - v_1
            try:
                v_3 = self.limit - self.cursor
                self.ket = self.cursor
                try:
                    v_4 = self.limit - self.cursor
                    try:
                        try:
                            v_5 = self.limit - self.cursor
                            try:
                                if not self.eq_s_b(u"\u0BBF\u0BA9\u0BC8"):
                                    raise lab6()
                                raise lab5()
                            except lab6: pass
                            self.cursor = self.limit - v_5
                            if not self.eq_s_b(u"\u0BC8"):
                                raise lab4()
                        except lab5: pass
                        v_6 = self.limit - self.cursor
                        v_7 = self.limit - self.cursor
                        try:
                            if self.find_among_b(TamilStemmer.a_18) == 0:
                                raise lab7()
                            raise lab4()
                        except lab7: pass
                        self.cursor = self.limit - v_7
                        self.cursor = self.limit - v_6
                        raise lab3()
                    except lab4: pass
                    self.cursor = self.limit - v_4
                    if not self.eq_s_b(u"\u0BC8"):
                        raise lab2()
                    v_8 = self.limit - self.cursor
                    if self.find_among_b(TamilStemmer.a_19) == 0:
                        raise lab2()
                    if not self.eq_s_b(u"\u0BCD"):
                        raise lab2()
                    self.cursor = self.limit - v_8
                except lab3: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                self.cursor = self.limit - v_3
                raise lab0()
            except lab2: pass
            self.cursor = self.limit - v_1
            try:
                v_9 = self.limit - self.cursor
                self.ket = self.cursor
                try:
                    v_10 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0BCA\u0B9F\u0BC1"):
                            raise lab10()
                        raise lab9()
                    except lab10: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BCB\u0B9F\u0BC1"):
                            raise lab11()
                        raise lab9()
                    except lab11: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BB2\u0BCD"):
                            raise lab12()
                        raise lab9()
                    except lab12: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BB1\u0BCD"):
                            raise lab13()
                        raise lab9()
                    except lab13: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BA9\u0BCD"):
                            raise lab14()
                        v_11 = self.limit - self.cursor
                        v_12 = self.limit - self.cursor
                        try:
                            if not self.eq_s_b(u"\u0BAE"):
                                raise lab15()
                            raise lab14()
                        except lab15: pass
                        self.cursor = self.limit - v_12
                        self.cursor = self.limit - v_11
                        raise lab9()
                    except lab14: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BA9\u0BCD\u0BB1\u0BC1"):
                            raise lab16()
                        raise lab9()
                    except lab16: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBF\u0BB0\u0BC1\u0BA8\u0BCD\u0BA4\u0BC1"):
                            raise lab17()
                        raise lab9()
                    except lab17: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BB5\u0BBF\u0B9F"):
                            raise lab18()
                        raise lab9()
                    except lab18: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not len(self.current) >= 7:
                            raise lab19()
                        if not self.eq_s_b(u"\u0BBF\u0B9F\u0BAE\u0BCD"):
                            raise lab19()
                        raise lab9()
                    except lab19: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBE\u0BB2\u0BCD"):
                            raise lab20()
                        raise lab9()
                    except lab20: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BC1\u0B9F\u0BC8"):
                            raise lab21()
                        raise lab9()
                    except lab21: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BBE\u0BAE\u0BB2\u0BCD"):
                            raise lab22()
                        raise lab9()
                    except lab22: pass
                    self.cursor = self.limit - v_10
                    try:
                        if not self.eq_s_b(u"\u0BB2\u0BCD"):
                            raise lab23()
                        v_13 = self.limit - self.cursor
                        v_14 = self.limit - self.cursor
                        try:
                            if self.find_among_b(TamilStemmer.a_20) == 0:
                                raise lab24()
                            raise lab23()
                        except lab24: pass
                        self.cursor = self.limit - v_14
                        self.cursor = self.limit - v_13
                        raise lab9()
                    except lab23: pass
                    self.cursor = self.limit - v_10
                    if not self.eq_s_b(u"\u0BC1\u0BB3\u0BCD"):
                        raise lab8()
                except lab9: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u0BCD"):
                    return False
                self.cursor = self.limit - v_9
                raise lab0()
            except lab8: pass
            self.cursor = self.limit - v_1
            try:
                v_15 = self.limit - self.cursor
                self.ket = self.cursor
                try:
                    v_16 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0B95\u0BA3\u0BCD"):
                            raise lab27()
                        raise lab26()
                    except lab27: pass
                    self.cursor = self.limit - v_16
                    try:
                        if not self.eq_s_b(u"\u0BAE\u0BC1\u0BA9\u0BCD"):
                            raise lab28()
                        raise lab26()
                    except lab28: pass
                    self.cursor = self.limit - v_16
                    try:
                        if not self.eq_s_b(u"\u0BAE\u0BC7\u0BB2\u0BCD"):
                            raise lab29()
                        raise lab26()
                    except lab29: pass
                    self.cursor = self.limit - v_16
                    try:
                        if not self.eq_s_b(u"\u0BAE\u0BC7\u0BB1\u0BCD"):
                            raise lab30()
                        raise lab26()
                    except lab30: pass
                    self.cursor = self.limit - v_16
                    try:
                        if not self.eq_s_b(u"\u0B95\u0BC0\u0BB4\u0BCD"):
                            raise lab31()
                        raise lab26()
                    except lab31: pass
                    self.cursor = self.limit - v_16
                    try:
                        if not self.eq_s_b(u"\u0BAA\u0BBF\u0BA9\u0BCD"):
                            raise lab32()
                        raise lab26()
                    except lab32: pass
                    self.cursor = self.limit - v_16
                    if not self.eq_s_b(u"\u0BA4\u0BC1"):
                        raise lab25()
                    v_17 = self.limit - self.cursor
                    v_18 = self.limit - self.cursor
                    try:
                        if self.find_among_b(TamilStemmer.a_21) == 0:
                            raise lab33()
                        raise lab25()
                    except lab33: pass
                    self.cursor = self.limit - v_18
                    self.cursor = self.limit - v_17
                except lab26: pass
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                self.cursor = self.limit - v_15
                raise lab0()
            except lab25: pass
            self.cursor = self.limit - v_1
            v_19 = self.limit - self.cursor
            self.ket = self.cursor
            if not self.eq_s_b(u"\u0BC0"):
                return False
            self.bra = self.cursor
            if not self.slice_from(u"\u0BBF"):
                return False
            self.cursor = self.limit - v_19
        except lab0: pass
        self.B_found_a_match = True
        self.B_found_vetrumai_urupu = True
        v_20 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            if not self.eq_s_b(u"\u0BBF\u0BA9\u0BCD"):
                raise lab34()
            self.bra = self.cursor
            if not self.slice_from(u"\u0BCD"):
                return False
        except lab34: pass
        self.cursor = self.limit - v_20
        self.cursor = self.limit_backward
        self.__r_fix_endings()
        return True

    def __r_remove_tense_suffixes(self):
        self.B_found_a_match = True
        while True:
            v_1 = self.cursor
            try:
                if not self.B_found_a_match:
                    raise lab0()
                v_2 = self.cursor
                self.__r_remove_tense_suffix()
                self.cursor = v_2
                continue
            except lab0: pass
            self.cursor = v_1
            break
        return True

    def __r_remove_tense_suffix(self):
        self.B_found_a_match = False
        if not self.__r_has_min_length():
            return False
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_1 = self.limit - self.cursor
        try:
            try:
                v_2 = self.limit - self.cursor
                try:
                    v_3 = self.limit - self.cursor
                    self.ket = self.cursor
                    if self.find_among_b(TamilStemmer.a_22) == 0:
                        raise lab2()
                    self.bra = self.cursor
                    if not self.slice_del():
                        return False

                    self.B_found_a_match = True
                    self.cursor = self.limit - v_3
                    raise lab1()
                except lab2: pass
                self.cursor = self.limit - v_2
                try:
                    v_4 = self.limit - self.cursor
                    self.ket = self.cursor
                    try:
                        v_5 = self.limit - self.cursor
                        try:
                            if not self.eq_s_b(u"\u0BAE\u0BBE\u0BB0\u0BCD"):
                                raise lab5()
                            raise lab4()
                        except lab5: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAE\u0BBF\u0BA9\u0BCD"):
                                raise lab6()
                            raise lab4()
                        except lab6: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BA9\u0BCD"):
                                raise lab7()
                            raise lab4()
                        except lab7: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BBE\u0BA9\u0BCD"):
                                raise lab8()
                            raise lab4()
                        except lab8: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BBE\u0BB3\u0BCD"):
                                raise lab9()
                            raise lab4()
                        except lab9: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BBE\u0BB0\u0BCD"):
                                raise lab10()
                            raise lab4()
                        except lab10: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BB5\u0BA9\u0BCD"):
                                raise lab11()
                            v_6 = self.limit - self.cursor
                            v_7 = self.limit - self.cursor
                            try:
                                if self.find_among_b(TamilStemmer.a_23) == 0:
                                    raise lab12()
                                raise lab11()
                            except lab12: pass
                            self.cursor = self.limit - v_7
                            self.cursor = self.limit - v_6
                            raise lab4()
                        except lab11: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BB3\u0BCD"):
                                raise lab13()
                            raise lab4()
                        except lab13: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BB5\u0BB3\u0BCD"):
                                raise lab14()
                            raise lab4()
                        except lab14: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BB0\u0BCD"):
                                raise lab15()
                            raise lab4()
                        except lab15: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BB5\u0BB0\u0BCD"):
                                raise lab16()
                            raise lab4()
                        except lab16: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9"):
                                raise lab17()
                            raise lab4()
                        except lab17: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAA"):
                                raise lab18()
                            raise lab4()
                        except lab18: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0B95"):
                                raise lab19()
                            raise lab4()
                        except lab19: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA4"):
                                raise lab20()
                            raise lab4()
                        except lab20: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAF"):
                                raise lab21()
                            raise lab4()
                        except lab21: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAA\u0BA9\u0BCD"):
                                raise lab22()
                            raise lab4()
                        except lab22: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAA\u0BB3\u0BCD"):
                                raise lab23()
                            raise lab4()
                        except lab23: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAA\u0BB0\u0BCD"):
                                raise lab24()
                            raise lab4()
                        except lab24: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA4\u0BC1"):
                                raise lab25()
                            v_8 = self.limit - self.cursor
                            v_9 = self.limit - self.cursor
                            try:
                                if self.find_among_b(TamilStemmer.a_24) == 0:
                                    raise lab26()
                                raise lab25()
                            except lab26: pass
                            self.cursor = self.limit - v_9
                            self.cursor = self.limit - v_8
                            raise lab4()
                        except lab25: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BBF\u0BB1\u0BCD\u0BB1\u0BC1"):
                                raise lab27()
                            raise lab4()
                        except lab27: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BAA\u0BAE\u0BCD"):
                                raise lab28()
                            raise lab4()
                        except lab28: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BAE\u0BCD"):
                                raise lab29()
                            raise lab4()
                        except lab29: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA4\u0BC1\u0BAE\u0BCD"):
                                raise lab30()
                            raise lab4()
                        except lab30: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BB1\u0BC1\u0BAE\u0BCD"):
                                raise lab31()
                            raise lab4()
                        except lab31: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0B95\u0BC1\u0BAE\u0BCD"):
                                raise lab32()
                            raise lab4()
                        except lab32: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BC6\u0BA9\u0BCD"):
                                raise lab33()
                            raise lab4()
                        except lab33: pass
                        self.cursor = self.limit - v_5
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BC8"):
                                raise lab34()
                            raise lab4()
                        except lab34: pass
                        self.cursor = self.limit - v_5
                        if not self.eq_s_b(u"\u0BB5\u0BC8"):
                            raise lab3()
                    except lab4: pass
                    self.bra = self.cursor
                    if not self.slice_del():
                        return False

                    self.B_found_a_match = True
                    self.cursor = self.limit - v_4
                    raise lab1()
                except lab3: pass
                self.cursor = self.limit - v_2
                try:
                    v_10 = self.limit - self.cursor
                    self.ket = self.cursor
                    try:
                        v_11 = self.limit - self.cursor
                        try:
                            if not self.eq_s_b(u"\u0BBE\u0BA9\u0BCD"):
                                raise lab37()
                            v_12 = self.limit - self.cursor
                            v_13 = self.limit - self.cursor
                            try:
                                if not self.eq_s_b(u"\u0B9A"):
                                    raise lab38()
                                raise lab37()
                            except lab38: pass
                            self.cursor = self.limit - v_13
                            self.cursor = self.limit - v_12
                            raise lab36()
                        except lab37: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BBE\u0BB3\u0BCD"):
                                raise lab39()
                            raise lab36()
                        except lab39: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BBE\u0BB0\u0BCD"):
                                raise lab40()
                            raise lab36()
                        except lab40: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BC7\u0BA9\u0BCD"):
                                raise lab41()
                            raise lab36()
                        except lab41: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BBE"):
                                raise lab42()
                            raise lab36()
                        except lab42: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BBE\u0BAE\u0BCD"):
                                raise lab43()
                            raise lab36()
                        except lab43: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BC6\u0BAE\u0BCD"):
                                raise lab44()
                            raise lab36()
                        except lab44: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BC7\u0BAE\u0BCD"):
                                raise lab45()
                            raise lab36()
                        except lab45: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BCB\u0BAE\u0BCD"):
                                raise lab46()
                            raise lab36()
                        except lab46: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0B95\u0BC1\u0BAE\u0BCD"):
                                raise lab47()
                            raise lab36()
                        except lab47: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BA4\u0BC1\u0BAE\u0BCD"):
                                raise lab48()
                            raise lab36()
                        except lab48: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0B9F\u0BC1\u0BAE\u0BCD"):
                                raise lab49()
                            raise lab36()
                        except lab49: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BB1\u0BC1\u0BAE\u0BCD"):
                                raise lab50()
                            raise lab36()
                        except lab50: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BBE\u0BAF\u0BCD"):
                                raise lab51()
                            raise lab36()
                        except lab51: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BC6\u0BA9\u0BCD"):
                                raise lab52()
                            raise lab36()
                        except lab52: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BA9\u0BBF\u0BB0\u0BCD"):
                                raise lab53()
                            raise lab36()
                        except lab53: pass
                        self.cursor = self.limit - v_11
                        try:
                            if not self.eq_s_b(u"\u0BC0\u0BB0\u0BCD"):
                                raise lab54()
                            raise lab36()
                        except lab54: pass
                        self.cursor = self.limit - v_11
                        if not self.eq_s_b(u"\u0BC0\u0BAF\u0BB0\u0BCD"):
                            raise lab35()
                    except lab36: pass
                    self.bra = self.cursor
                    if not self.slice_from(u"\u0BCD"):
                        return False
                    self.B_found_a_match = True
                    self.cursor = self.limit - v_10
                    raise lab1()
                except lab35: pass
                self.cursor = self.limit - v_2
                v_14 = self.limit - self.cursor
                self.ket = self.cursor
                try:
                    v_15 = self.limit - self.cursor
                    try:
                        if not self.eq_s_b(u"\u0B95\u0BC1"):
                            raise lab56()
                        raise lab55()
                    except lab56: pass
                    self.cursor = self.limit - v_15
                    if not self.eq_s_b(u"\u0BA4\u0BC1"):
                        raise lab0()
                except lab55: pass
                v_16 = self.limit - self.cursor
                if not self.eq_s_b(u"\u0BCD"):
                    raise lab0()
                self.cursor = self.limit - v_16
                self.bra = self.cursor
                if not self.slice_del():
                    return False

                self.B_found_a_match = True
                self.cursor = self.limit - v_14
            except lab1: pass
        except lab0: pass
        self.cursor = self.limit - v_1
        v_17 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            if self.find_among_b(TamilStemmer.a_25) == 0:
                raise lab57()
            self.bra = self.cursor
            if not self.slice_del():
                return False

            self.B_found_a_match = True
        except lab57: pass
        self.cursor = self.limit - v_17
        self.cursor = self.limit_backward
        self.__r_fix_endings()
        return True

    def _stem(self):
        self.B_found_vetrumai_urupu = False
        v_1 = self.cursor
        self.__r_fix_ending()
        self.cursor = v_1
        if not self.__r_has_min_length():
            return False
        v_2 = self.cursor
        self.__r_remove_question_prefixes()
        self.cursor = v_2
        v_3 = self.cursor
        self.__r_remove_pronoun_prefixes()
        self.cursor = v_3
        v_4 = self.cursor
        self.__r_remove_question_suffixes()
        self.cursor = v_4
        v_5 = self.cursor
        self.__r_remove_um()
        self.cursor = v_5
        v_6 = self.cursor
        self.__r_remove_common_word_endings()
        self.cursor = v_6
        v_7 = self.cursor
        self.__r_remove_vetrumai_urupukal()
        self.cursor = v_7
        v_8 = self.cursor
        self.__r_remove_plural_suffix()
        self.cursor = v_8
        v_9 = self.cursor
        self.__r_remove_command_suffixes()
        self.cursor = v_9
        v_10 = self.cursor
        self.__r_remove_tense_suffixes()
        self.cursor = v_10
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass


class lab8(BaseException): pass


class lab9(BaseException): pass


class lab10(BaseException): pass


class lab11(BaseException): pass


class lab12(BaseException): pass


class lab13(BaseException): pass


class lab14(BaseException): pass


class lab15(BaseException): pass


class lab16(BaseException): pass


class lab17(BaseException): pass


class lab18(BaseException): pass


class lab19(BaseException): pass


class lab20(BaseException): pass


class lab21(BaseException): pass


class lab22(BaseException): pass


class lab23(BaseException): pass


class lab24(BaseException): pass


class lab25(BaseException): pass


class lab26(BaseException): pass


class lab27(BaseException): pass


class lab28(BaseException): pass


class lab29(BaseException): pass


class lab30(BaseException): pass


class lab31(BaseException): pass


class lab32(BaseException): pass


class lab33(BaseException): pass


class lab34(BaseException): pass


class lab35(BaseException): pass


class lab36(BaseException): pass


class lab37(BaseException): pass


class lab38(BaseException): pass


class lab39(BaseException): pass


class lab40(BaseException): pass


class lab41(BaseException): pass


class lab42(BaseException): pass


class lab43(BaseException): pass


class lab44(BaseException): pass


class lab45(BaseException): pass


class lab46(BaseException): pass


class lab47(BaseException): pass


class lab48(BaseException): pass


class lab49(BaseException): pass


class lab50(BaseException): pass


class lab51(BaseException): pass


class lab52(BaseException): pass


class lab53(BaseException): pass


class lab54(BaseException): pass


class lab55(BaseException): pass


class lab56(BaseException): pass


class lab57(BaseException): pass
