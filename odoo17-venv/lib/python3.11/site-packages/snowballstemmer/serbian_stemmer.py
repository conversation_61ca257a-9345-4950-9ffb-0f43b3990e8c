# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class SerbianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"\u0430", -1, 1),
        Among(u"\u0431", -1, 2),
        Among(u"\u0432", -1, 3),
        Among(u"\u0433", -1, 4),
        Among(u"\u0434", -1, 5),
        Among(u"\u0435", -1, 7),
        Among(u"\u0436", -1, 8),
        Among(u"\u0437", -1, 9),
        Among(u"\u0438", -1, 10),
        Among(u"\u043A", -1, 12),
        Among(u"\u043B", -1, 13),
        Among(u"\u043C", -1, 15),
        Among(u"\u043D", -1, 16),
        Among(u"\u043E", -1, 18),
        Among(u"\u043F", -1, 19),
        Among(u"\u0440", -1, 20),
        Among(u"\u0441", -1, 21),
        Among(u"\u0442", -1, 22),
        Among(u"\u0443", -1, 24),
        Among(u"\u0444", -1, 25),
        Among(u"\u0445", -1, 26),
        Among(u"\u0446", -1, 27),
        Among(u"\u0447", -1, 28),
        Among(u"\u0448", -1, 30),
        Among(u"\u0452", -1, 6),
        Among(u"\u0458", -1, 11),
        Among(u"\u0459", -1, 14),
        Among(u"\u045A", -1, 17),
        Among(u"\u045B", -1, 23),
        Among(u"\u045F", -1, 29)
    ]

    a_1 = [
        Among(u"daba", -1, 73),
        Among(u"ajaca", -1, 12),
        Among(u"ejaca", -1, 14),
        Among(u"ljaca", -1, 13),
        Among(u"njaca", -1, 85),
        Among(u"ojaca", -1, 15),
        Among(u"alaca", -1, 82),
        Among(u"elaca", -1, 83),
        Among(u"olaca", -1, 84),
        Among(u"maca", -1, 75),
        Among(u"naca", -1, 76),
        Among(u"raca", -1, 81),
        Among(u"saca", -1, 80),
        Among(u"vaca", -1, 79),
        Among(u"\u0161aca", -1, 18),
        Among(u"aoca", -1, 82),
        Among(u"acaka", -1, 55),
        Among(u"ajaka", -1, 16),
        Among(u"ojaka", -1, 17),
        Among(u"anaka", -1, 78),
        Among(u"ataka", -1, 58),
        Among(u"etaka", -1, 59),
        Among(u"itaka", -1, 60),
        Among(u"otaka", -1, 61),
        Among(u"utaka", -1, 62),
        Among(u"a\u010Daka", -1, 54),
        Among(u"esama", -1, 67),
        Among(u"izama", -1, 87),
        Among(u"jacima", -1, 5),
        Among(u"nicima", -1, 23),
        Among(u"ticima", -1, 24),
        Among(u"teticima", 30, 21),
        Among(u"zicima", -1, 25),
        Among(u"atcima", -1, 58),
        Among(u"utcima", -1, 62),
        Among(u"\u010Dcima", -1, 74),
        Among(u"pesima", -1, 2),
        Among(u"inzima", -1, 19),
        Among(u"lozima", -1, 1),
        Among(u"metara", -1, 68),
        Among(u"centara", -1, 69),
        Among(u"istara", -1, 70),
        Among(u"ekata", -1, 86),
        Among(u"anata", -1, 53),
        Among(u"nstava", -1, 22),
        Among(u"kustava", -1, 29),
        Among(u"ajac", -1, 12),
        Among(u"ejac", -1, 14),
        Among(u"ljac", -1, 13),
        Among(u"njac", -1, 85),
        Among(u"anjac", 49, 11),
        Among(u"ojac", -1, 15),
        Among(u"alac", -1, 82),
        Among(u"elac", -1, 83),
        Among(u"olac", -1, 84),
        Among(u"mac", -1, 75),
        Among(u"nac", -1, 76),
        Among(u"rac", -1, 81),
        Among(u"sac", -1, 80),
        Among(u"vac", -1, 79),
        Among(u"\u0161ac", -1, 18),
        Among(u"jebe", -1, 88),
        Among(u"olce", -1, 84),
        Among(u"kuse", -1, 27),
        Among(u"rave", -1, 42),
        Among(u"save", -1, 52),
        Among(u"\u0161ave", -1, 51),
        Among(u"baci", -1, 89),
        Among(u"jaci", -1, 5),
        Among(u"tvenici", -1, 20),
        Among(u"snici", -1, 26),
        Among(u"tetici", -1, 21),
        Among(u"bojci", -1, 4),
        Among(u"vojci", -1, 3),
        Among(u"ojsci", -1, 66),
        Among(u"atci", -1, 58),
        Among(u"itci", -1, 60),
        Among(u"utci", -1, 62),
        Among(u"\u010Dci", -1, 74),
        Among(u"pesi", -1, 2),
        Among(u"inzi", -1, 19),
        Among(u"lozi", -1, 1),
        Among(u"acak", -1, 55),
        Among(u"usak", -1, 57),
        Among(u"atak", -1, 58),
        Among(u"etak", -1, 59),
        Among(u"itak", -1, 60),
        Among(u"otak", -1, 61),
        Among(u"utak", -1, 62),
        Among(u"a\u010Dak", -1, 54),
        Among(u"u\u0161ak", -1, 56),
        Among(u"izam", -1, 87),
        Among(u"tican", -1, 65),
        Among(u"cajan", -1, 7),
        Among(u"\u010Dajan", -1, 6),
        Among(u"voljan", -1, 77),
        Among(u"eskan", -1, 63),
        Among(u"alan", -1, 40),
        Among(u"bilan", -1, 33),
        Among(u"gilan", -1, 37),
        Among(u"nilan", -1, 39),
        Among(u"rilan", -1, 38),
        Among(u"silan", -1, 36),
        Among(u"tilan", -1, 34),
        Among(u"avilan", -1, 35),
        Among(u"laran", -1, 9),
        Among(u"eran", -1, 8),
        Among(u"asan", -1, 91),
        Among(u"esan", -1, 10),
        Among(u"dusan", -1, 31),
        Among(u"kusan", -1, 28),
        Among(u"atan", -1, 47),
        Among(u"pletan", -1, 50),
        Among(u"tetan", -1, 49),
        Among(u"antan", -1, 32),
        Among(u"pravan", -1, 44),
        Among(u"stavan", -1, 43),
        Among(u"sivan", -1, 46),
        Among(u"tivan", -1, 45),
        Among(u"ozan", -1, 41),
        Among(u"ti\u010Dan", -1, 64),
        Among(u"a\u0161an", -1, 90),
        Among(u"du\u0161an", -1, 30),
        Among(u"metar", -1, 68),
        Among(u"centar", -1, 69),
        Among(u"istar", -1, 70),
        Among(u"ekat", -1, 86),
        Among(u"enat", -1, 48),
        Among(u"oscu", -1, 72),
        Among(u"o\u0161\u0107u", -1, 71)
    ]

    a_2 = [
        Among(u"aca", -1, 124),
        Among(u"eca", -1, 125),
        Among(u"uca", -1, 126),
        Among(u"ga", -1, 20),
        Among(u"acega", 3, 124),
        Among(u"ecega", 3, 125),
        Among(u"ucega", 3, 126),
        Among(u"anjijega", 3, 84),
        Among(u"enjijega", 3, 85),
        Among(u"snjijega", 3, 122),
        Among(u"\u0161njijega", 3, 86),
        Among(u"kijega", 3, 95),
        Among(u"skijega", 11, 1),
        Among(u"\u0161kijega", 11, 2),
        Among(u"elijega", 3, 83),
        Among(u"nijega", 3, 13),
        Among(u"osijega", 3, 123),
        Among(u"atijega", 3, 120),
        Among(u"evitijega", 3, 92),
        Among(u"ovitijega", 3, 93),
        Among(u"astijega", 3, 94),
        Among(u"avijega", 3, 77),
        Among(u"evijega", 3, 78),
        Among(u"ivijega", 3, 79),
        Among(u"ovijega", 3, 80),
        Among(u"o\u0161ijega", 3, 91),
        Among(u"anjega", 3, 84),
        Among(u"enjega", 3, 85),
        Among(u"snjega", 3, 122),
        Among(u"\u0161njega", 3, 86),
        Among(u"kega", 3, 95),
        Among(u"skega", 30, 1),
        Among(u"\u0161kega", 30, 2),
        Among(u"elega", 3, 83),
        Among(u"nega", 3, 13),
        Among(u"anega", 34, 10),
        Among(u"enega", 34, 87),
        Among(u"snega", 34, 159),
        Among(u"\u0161nega", 34, 88),
        Among(u"osega", 3, 123),
        Among(u"atega", 3, 120),
        Among(u"evitega", 3, 92),
        Among(u"ovitega", 3, 93),
        Among(u"astega", 3, 94),
        Among(u"avega", 3, 77),
        Among(u"evega", 3, 78),
        Among(u"ivega", 3, 79),
        Among(u"ovega", 3, 80),
        Among(u"a\u0107ega", 3, 14),
        Among(u"e\u0107ega", 3, 15),
        Among(u"u\u0107ega", 3, 16),
        Among(u"o\u0161ega", 3, 91),
        Among(u"acoga", 3, 124),
        Among(u"ecoga", 3, 125),
        Among(u"ucoga", 3, 126),
        Among(u"anjoga", 3, 84),
        Among(u"enjoga", 3, 85),
        Among(u"snjoga", 3, 122),
        Among(u"\u0161njoga", 3, 86),
        Among(u"koga", 3, 95),
        Among(u"skoga", 59, 1),
        Among(u"\u0161koga", 59, 2),
        Among(u"loga", 3, 19),
        Among(u"eloga", 62, 83),
        Among(u"noga", 3, 13),
        Among(u"cinoga", 64, 137),
        Among(u"\u010Dinoga", 64, 89),
        Among(u"osoga", 3, 123),
        Among(u"atoga", 3, 120),
        Among(u"evitoga", 3, 92),
        Among(u"ovitoga", 3, 93),
        Among(u"astoga", 3, 94),
        Among(u"avoga", 3, 77),
        Among(u"evoga", 3, 78),
        Among(u"ivoga", 3, 79),
        Among(u"ovoga", 3, 80),
        Among(u"a\u0107oga", 3, 14),
        Among(u"e\u0107oga", 3, 15),
        Among(u"u\u0107oga", 3, 16),
        Among(u"o\u0161oga", 3, 91),
        Among(u"uga", 3, 18),
        Among(u"aja", -1, 109),
        Among(u"caja", 81, 26),
        Among(u"laja", 81, 30),
        Among(u"raja", 81, 31),
        Among(u"\u0107aja", 81, 28),
        Among(u"\u010Daja", 81, 27),
        Among(u"\u0111aja", 81, 29),
        Among(u"bija", -1, 32),
        Among(u"cija", -1, 33),
        Among(u"dija", -1, 34),
        Among(u"fija", -1, 40),
        Among(u"gija", -1, 39),
        Among(u"anjija", -1, 84),
        Among(u"enjija", -1, 85),
        Among(u"snjija", -1, 122),
        Among(u"\u0161njija", -1, 86),
        Among(u"kija", -1, 95),
        Among(u"skija", 97, 1),
        Among(u"\u0161kija", 97, 2),
        Among(u"lija", -1, 24),
        Among(u"elija", 100, 83),
        Among(u"mija", -1, 37),
        Among(u"nija", -1, 13),
        Among(u"ganija", 103, 9),
        Among(u"manija", 103, 6),
        Among(u"panija", 103, 7),
        Among(u"ranija", 103, 8),
        Among(u"tanija", 103, 5),
        Among(u"pija", -1, 41),
        Among(u"rija", -1, 42),
        Among(u"rarija", 110, 21),
        Among(u"sija", -1, 23),
        Among(u"osija", 112, 123),
        Among(u"tija", -1, 44),
        Among(u"atija", 114, 120),
        Among(u"evitija", 114, 92),
        Among(u"ovitija", 114, 93),
        Among(u"otija", 114, 22),
        Among(u"astija", 114, 94),
        Among(u"avija", -1, 77),
        Among(u"evija", -1, 78),
        Among(u"ivija", -1, 79),
        Among(u"ovija", -1, 80),
        Among(u"zija", -1, 45),
        Among(u"o\u0161ija", -1, 91),
        Among(u"\u017Eija", -1, 38),
        Among(u"anja", -1, 84),
        Among(u"enja", -1, 85),
        Among(u"snja", -1, 122),
        Among(u"\u0161nja", -1, 86),
        Among(u"ka", -1, 95),
        Among(u"ska", 131, 1),
        Among(u"\u0161ka", 131, 2),
        Among(u"ala", -1, 104),
        Among(u"acala", 134, 128),
        Among(u"astajala", 134, 106),
        Among(u"istajala", 134, 107),
        Among(u"ostajala", 134, 108),
        Among(u"ijala", 134, 47),
        Among(u"injala", 134, 114),
        Among(u"nala", 134, 46),
        Among(u"irala", 134, 100),
        Among(u"urala", 134, 105),
        Among(u"tala", 134, 113),
        Among(u"astala", 144, 110),
        Among(u"istala", 144, 111),
        Among(u"ostala", 144, 112),
        Among(u"avala", 134, 97),
        Among(u"evala", 134, 96),
        Among(u"ivala", 134, 98),
        Among(u"ovala", 134, 76),
        Among(u"uvala", 134, 99),
        Among(u"a\u010Dala", 134, 102),
        Among(u"ela", -1, 83),
        Among(u"ila", -1, 116),
        Among(u"acila", 155, 124),
        Among(u"lucila", 155, 121),
        Among(u"nila", 155, 103),
        Among(u"astanila", 158, 110),
        Among(u"istanila", 158, 111),
        Among(u"ostanila", 158, 112),
        Among(u"rosila", 155, 127),
        Among(u"jetila", 155, 118),
        Among(u"ozila", 155, 48),
        Among(u"a\u010Dila", 155, 101),
        Among(u"lu\u010Dila", 155, 117),
        Among(u"ro\u0161ila", 155, 90),
        Among(u"ola", -1, 50),
        Among(u"asla", -1, 115),
        Among(u"nula", -1, 13),
        Among(u"gama", -1, 20),
        Among(u"logama", 171, 19),
        Among(u"ugama", 171, 18),
        Among(u"ajama", -1, 109),
        Among(u"cajama", 174, 26),
        Among(u"lajama", 174, 30),
        Among(u"rajama", 174, 31),
        Among(u"\u0107ajama", 174, 28),
        Among(u"\u010Dajama", 174, 27),
        Among(u"\u0111ajama", 174, 29),
        Among(u"bijama", -1, 32),
        Among(u"cijama", -1, 33),
        Among(u"dijama", -1, 34),
        Among(u"fijama", -1, 40),
        Among(u"gijama", -1, 39),
        Among(u"lijama", -1, 35),
        Among(u"mijama", -1, 37),
        Among(u"nijama", -1, 36),
        Among(u"ganijama", 188, 9),
        Among(u"manijama", 188, 6),
        Among(u"panijama", 188, 7),
        Among(u"ranijama", 188, 8),
        Among(u"tanijama", 188, 5),
        Among(u"pijama", -1, 41),
        Among(u"rijama", -1, 42),
        Among(u"sijama", -1, 43),
        Among(u"tijama", -1, 44),
        Among(u"zijama", -1, 45),
        Among(u"\u017Eijama", -1, 38),
        Among(u"alama", -1, 104),
        Among(u"ijalama", 200, 47),
        Among(u"nalama", 200, 46),
        Among(u"elama", -1, 119),
        Among(u"ilama", -1, 116),
        Among(u"ramama", -1, 52),
        Among(u"lemama", -1, 51),
        Among(u"inama", -1, 11),
        Among(u"cinama", 207, 137),
        Among(u"\u010Dinama", 207, 89),
        Among(u"rama", -1, 52),
        Among(u"arama", 210, 53),
        Among(u"drama", 210, 54),
        Among(u"erama", 210, 55),
        Among(u"orama", 210, 56),
        Among(u"basama", -1, 135),
        Among(u"gasama", -1, 131),
        Among(u"jasama", -1, 129),
        Among(u"kasama", -1, 133),
        Among(u"nasama", -1, 132),
        Among(u"tasama", -1, 130),
        Among(u"vasama", -1, 134),
        Among(u"esama", -1, 152),
        Among(u"isama", -1, 154),
        Among(u"etama", -1, 70),
        Among(u"estama", -1, 71),
        Among(u"istama", -1, 72),
        Among(u"kstama", -1, 73),
        Among(u"ostama", -1, 74),
        Among(u"avama", -1, 77),
        Among(u"evama", -1, 78),
        Among(u"ivama", -1, 79),
        Among(u"ba\u0161ama", -1, 63),
        Among(u"ga\u0161ama", -1, 64),
        Among(u"ja\u0161ama", -1, 61),
        Among(u"ka\u0161ama", -1, 62),
        Among(u"na\u0161ama", -1, 60),
        Among(u"ta\u0161ama", -1, 59),
        Among(u"va\u0161ama", -1, 65),
        Among(u"e\u0161ama", -1, 66),
        Among(u"i\u0161ama", -1, 67),
        Among(u"lema", -1, 51),
        Among(u"acima", -1, 124),
        Among(u"ecima", -1, 125),
        Among(u"ucima", -1, 126),
        Among(u"ajima", -1, 109),
        Among(u"cajima", 245, 26),
        Among(u"lajima", 245, 30),
        Among(u"rajima", 245, 31),
        Among(u"\u0107ajima", 245, 28),
        Among(u"\u010Dajima", 245, 27),
        Among(u"\u0111ajima", 245, 29),
        Among(u"bijima", -1, 32),
        Among(u"cijima", -1, 33),
        Among(u"dijima", -1, 34),
        Among(u"fijima", -1, 40),
        Among(u"gijima", -1, 39),
        Among(u"anjijima", -1, 84),
        Among(u"enjijima", -1, 85),
        Among(u"snjijima", -1, 122),
        Among(u"\u0161njijima", -1, 86),
        Among(u"kijima", -1, 95),
        Among(u"skijima", 261, 1),
        Among(u"\u0161kijima", 261, 2),
        Among(u"lijima", -1, 35),
        Among(u"elijima", 264, 83),
        Among(u"mijima", -1, 37),
        Among(u"nijima", -1, 13),
        Among(u"ganijima", 267, 9),
        Among(u"manijima", 267, 6),
        Among(u"panijima", 267, 7),
        Among(u"ranijima", 267, 8),
        Among(u"tanijima", 267, 5),
        Among(u"pijima", -1, 41),
        Among(u"rijima", -1, 42),
        Among(u"sijima", -1, 43),
        Among(u"osijima", 275, 123),
        Among(u"tijima", -1, 44),
        Among(u"atijima", 277, 120),
        Among(u"evitijima", 277, 92),
        Among(u"ovitijima", 277, 93),
        Among(u"astijima", 277, 94),
        Among(u"avijima", -1, 77),
        Among(u"evijima", -1, 78),
        Among(u"ivijima", -1, 79),
        Among(u"ovijima", -1, 80),
        Among(u"zijima", -1, 45),
        Among(u"o\u0161ijima", -1, 91),
        Among(u"\u017Eijima", -1, 38),
        Among(u"anjima", -1, 84),
        Among(u"enjima", -1, 85),
        Among(u"snjima", -1, 122),
        Among(u"\u0161njima", -1, 86),
        Among(u"kima", -1, 95),
        Among(u"skima", 293, 1),
        Among(u"\u0161kima", 293, 2),
        Among(u"alima", -1, 104),
        Among(u"ijalima", 296, 47),
        Among(u"nalima", 296, 46),
        Among(u"elima", -1, 83),
        Among(u"ilima", -1, 116),
        Among(u"ozilima", 300, 48),
        Among(u"olima", -1, 50),
        Among(u"lemima", -1, 51),
        Among(u"nima", -1, 13),
        Among(u"anima", 304, 10),
        Among(u"inima", 304, 11),
        Among(u"cinima", 306, 137),
        Among(u"\u010Dinima", 306, 89),
        Among(u"onima", 304, 12),
        Among(u"arima", -1, 53),
        Among(u"drima", -1, 54),
        Among(u"erima", -1, 55),
        Among(u"orima", -1, 56),
        Among(u"basima", -1, 135),
        Among(u"gasima", -1, 131),
        Among(u"jasima", -1, 129),
        Among(u"kasima", -1, 133),
        Among(u"nasima", -1, 132),
        Among(u"tasima", -1, 130),
        Among(u"vasima", -1, 134),
        Among(u"esima", -1, 57),
        Among(u"isima", -1, 58),
        Among(u"osima", -1, 123),
        Among(u"atima", -1, 120),
        Among(u"ikatima", 324, 68),
        Among(u"latima", 324, 69),
        Among(u"etima", -1, 70),
        Among(u"evitima", -1, 92),
        Among(u"ovitima", -1, 93),
        Among(u"astima", -1, 94),
        Among(u"estima", -1, 71),
        Among(u"istima", -1, 72),
        Among(u"kstima", -1, 73),
        Among(u"ostima", -1, 74),
        Among(u"i\u0161tima", -1, 75),
        Among(u"avima", -1, 77),
        Among(u"evima", -1, 78),
        Among(u"ajevima", 337, 109),
        Among(u"cajevima", 338, 26),
        Among(u"lajevima", 338, 30),
        Among(u"rajevima", 338, 31),
        Among(u"\u0107ajevima", 338, 28),
        Among(u"\u010Dajevima", 338, 27),
        Among(u"\u0111ajevima", 338, 29),
        Among(u"ivima", -1, 79),
        Among(u"ovima", -1, 80),
        Among(u"govima", 346, 20),
        Among(u"ugovima", 347, 17),
        Among(u"lovima", 346, 82),
        Among(u"olovima", 349, 49),
        Among(u"movima", 346, 81),
        Among(u"onovima", 346, 12),
        Among(u"stvima", -1, 3),
        Among(u"\u0161tvima", -1, 4),
        Among(u"a\u0107ima", -1, 14),
        Among(u"e\u0107ima", -1, 15),
        Among(u"u\u0107ima", -1, 16),
        Among(u"ba\u0161ima", -1, 63),
        Among(u"ga\u0161ima", -1, 64),
        Among(u"ja\u0161ima", -1, 61),
        Among(u"ka\u0161ima", -1, 62),
        Among(u"na\u0161ima", -1, 60),
        Among(u"ta\u0161ima", -1, 59),
        Among(u"va\u0161ima", -1, 65),
        Among(u"e\u0161ima", -1, 66),
        Among(u"i\u0161ima", -1, 67),
        Among(u"o\u0161ima", -1, 91),
        Among(u"na", -1, 13),
        Among(u"ana", 368, 10),
        Among(u"acana", 369, 128),
        Among(u"urana", 369, 105),
        Among(u"tana", 369, 113),
        Among(u"avana", 369, 97),
        Among(u"evana", 369, 96),
        Among(u"ivana", 369, 98),
        Among(u"uvana", 369, 99),
        Among(u"a\u010Dana", 369, 102),
        Among(u"acena", 368, 124),
        Among(u"lucena", 368, 121),
        Among(u"a\u010Dena", 368, 101),
        Among(u"lu\u010Dena", 368, 117),
        Among(u"ina", 368, 11),
        Among(u"cina", 382, 137),
        Among(u"anina", 382, 10),
        Among(u"\u010Dina", 382, 89),
        Among(u"ona", 368, 12),
        Among(u"ara", -1, 53),
        Among(u"dra", -1, 54),
        Among(u"era", -1, 55),
        Among(u"ora", -1, 56),
        Among(u"basa", -1, 135),
        Among(u"gasa", -1, 131),
        Among(u"jasa", -1, 129),
        Among(u"kasa", -1, 133),
        Among(u"nasa", -1, 132),
        Among(u"tasa", -1, 130),
        Among(u"vasa", -1, 134),
        Among(u"esa", -1, 57),
        Among(u"isa", -1, 58),
        Among(u"osa", -1, 123),
        Among(u"ata", -1, 120),
        Among(u"ikata", 401, 68),
        Among(u"lata", 401, 69),
        Among(u"eta", -1, 70),
        Among(u"evita", -1, 92),
        Among(u"ovita", -1, 93),
        Among(u"asta", -1, 94),
        Among(u"esta", -1, 71),
        Among(u"ista", -1, 72),
        Among(u"ksta", -1, 73),
        Among(u"osta", -1, 74),
        Among(u"nuta", -1, 13),
        Among(u"i\u0161ta", -1, 75),
        Among(u"ava", -1, 77),
        Among(u"eva", -1, 78),
        Among(u"ajeva", 415, 109),
        Among(u"cajeva", 416, 26),
        Among(u"lajeva", 416, 30),
        Among(u"rajeva", 416, 31),
        Among(u"\u0107ajeva", 416, 28),
        Among(u"\u010Dajeva", 416, 27),
        Among(u"\u0111ajeva", 416, 29),
        Among(u"iva", -1, 79),
        Among(u"ova", -1, 80),
        Among(u"gova", 424, 20),
        Among(u"ugova", 425, 17),
        Among(u"lova", 424, 82),
        Among(u"olova", 427, 49),
        Among(u"mova", 424, 81),
        Among(u"onova", 424, 12),
        Among(u"stva", -1, 3),
        Among(u"\u0161tva", -1, 4),
        Among(u"a\u0107a", -1, 14),
        Among(u"e\u0107a", -1, 15),
        Among(u"u\u0107a", -1, 16),
        Among(u"ba\u0161a", -1, 63),
        Among(u"ga\u0161a", -1, 64),
        Among(u"ja\u0161a", -1, 61),
        Among(u"ka\u0161a", -1, 62),
        Among(u"na\u0161a", -1, 60),
        Among(u"ta\u0161a", -1, 59),
        Among(u"va\u0161a", -1, 65),
        Among(u"e\u0161a", -1, 66),
        Among(u"i\u0161a", -1, 67),
        Among(u"o\u0161a", -1, 91),
        Among(u"ace", -1, 124),
        Among(u"ece", -1, 125),
        Among(u"uce", -1, 126),
        Among(u"luce", 448, 121),
        Among(u"astade", -1, 110),
        Among(u"istade", -1, 111),
        Among(u"ostade", -1, 112),
        Among(u"ge", -1, 20),
        Among(u"loge", 453, 19),
        Among(u"uge", 453, 18),
        Among(u"aje", -1, 104),
        Among(u"caje", 456, 26),
        Among(u"laje", 456, 30),
        Among(u"raje", 456, 31),
        Among(u"astaje", 456, 106),
        Among(u"istaje", 456, 107),
        Among(u"ostaje", 456, 108),
        Among(u"\u0107aje", 456, 28),
        Among(u"\u010Daje", 456, 27),
        Among(u"\u0111aje", 456, 29),
        Among(u"ije", -1, 116),
        Among(u"bije", 466, 32),
        Among(u"cije", 466, 33),
        Among(u"dije", 466, 34),
        Among(u"fije", 466, 40),
        Among(u"gije", 466, 39),
        Among(u"anjije", 466, 84),
        Among(u"enjije", 466, 85),
        Among(u"snjije", 466, 122),
        Among(u"\u0161njije", 466, 86),
        Among(u"kije", 466, 95),
        Among(u"skije", 476, 1),
        Among(u"\u0161kije", 476, 2),
        Among(u"lije", 466, 35),
        Among(u"elije", 479, 83),
        Among(u"mije", 466, 37),
        Among(u"nije", 466, 13),
        Among(u"ganije", 482, 9),
        Among(u"manije", 482, 6),
        Among(u"panije", 482, 7),
        Among(u"ranije", 482, 8),
        Among(u"tanije", 482, 5),
        Among(u"pije", 466, 41),
        Among(u"rije", 466, 42),
        Among(u"sije", 466, 43),
        Among(u"osije", 490, 123),
        Among(u"tije", 466, 44),
        Among(u"atije", 492, 120),
        Among(u"evitije", 492, 92),
        Among(u"ovitije", 492, 93),
        Among(u"astije", 492, 94),
        Among(u"avije", 466, 77),
        Among(u"evije", 466, 78),
        Among(u"ivije", 466, 79),
        Among(u"ovije", 466, 80),
        Among(u"zije", 466, 45),
        Among(u"o\u0161ije", 466, 91),
        Among(u"\u017Eije", 466, 38),
        Among(u"anje", -1, 84),
        Among(u"enje", -1, 85),
        Among(u"snje", -1, 122),
        Among(u"\u0161nje", -1, 86),
        Among(u"uje", -1, 25),
        Among(u"lucuje", 508, 121),
        Among(u"iruje", 508, 100),
        Among(u"lu\u010Duje", 508, 117),
        Among(u"ke", -1, 95),
        Among(u"ske", 512, 1),
        Among(u"\u0161ke", 512, 2),
        Among(u"ale", -1, 104),
        Among(u"acale", 515, 128),
        Among(u"astajale", 515, 106),
        Among(u"istajale", 515, 107),
        Among(u"ostajale", 515, 108),
        Among(u"ijale", 515, 47),
        Among(u"injale", 515, 114),
        Among(u"nale", 515, 46),
        Among(u"irale", 515, 100),
        Among(u"urale", 515, 105),
        Among(u"tale", 515, 113),
        Among(u"astale", 525, 110),
        Among(u"istale", 525, 111),
        Among(u"ostale", 525, 112),
        Among(u"avale", 515, 97),
        Among(u"evale", 515, 96),
        Among(u"ivale", 515, 98),
        Among(u"ovale", 515, 76),
        Among(u"uvale", 515, 99),
        Among(u"a\u010Dale", 515, 102),
        Among(u"ele", -1, 83),
        Among(u"ile", -1, 116),
        Among(u"acile", 536, 124),
        Among(u"lucile", 536, 121),
        Among(u"nile", 536, 103),
        Among(u"rosile", 536, 127),
        Among(u"jetile", 536, 118),
        Among(u"ozile", 536, 48),
        Among(u"a\u010Dile", 536, 101),
        Among(u"lu\u010Dile", 536, 117),
        Among(u"ro\u0161ile", 536, 90),
        Among(u"ole", -1, 50),
        Among(u"asle", -1, 115),
        Among(u"nule", -1, 13),
        Among(u"rame", -1, 52),
        Among(u"leme", -1, 51),
        Among(u"acome", -1, 124),
        Among(u"ecome", -1, 125),
        Among(u"ucome", -1, 126),
        Among(u"anjome", -1, 84),
        Among(u"enjome", -1, 85),
        Among(u"snjome", -1, 122),
        Among(u"\u0161njome", -1, 86),
        Among(u"kome", -1, 95),
        Among(u"skome", 558, 1),
        Among(u"\u0161kome", 558, 2),
        Among(u"elome", -1, 83),
        Among(u"nome", -1, 13),
        Among(u"cinome", 562, 137),
        Among(u"\u010Dinome", 562, 89),
        Among(u"osome", -1, 123),
        Among(u"atome", -1, 120),
        Among(u"evitome", -1, 92),
        Among(u"ovitome", -1, 93),
        Among(u"astome", -1, 94),
        Among(u"avome", -1, 77),
        Among(u"evome", -1, 78),
        Among(u"ivome", -1, 79),
        Among(u"ovome", -1, 80),
        Among(u"a\u0107ome", -1, 14),
        Among(u"e\u0107ome", -1, 15),
        Among(u"u\u0107ome", -1, 16),
        Among(u"o\u0161ome", -1, 91),
        Among(u"ne", -1, 13),
        Among(u"ane", 578, 10),
        Among(u"acane", 579, 128),
        Among(u"urane", 579, 105),
        Among(u"tane", 579, 113),
        Among(u"astane", 582, 110),
        Among(u"istane", 582, 111),
        Among(u"ostane", 582, 112),
        Among(u"avane", 579, 97),
        Among(u"evane", 579, 96),
        Among(u"ivane", 579, 98),
        Among(u"uvane", 579, 99),
        Among(u"a\u010Dane", 579, 102),
        Among(u"acene", 578, 124),
        Among(u"lucene", 578, 121),
        Among(u"a\u010Dene", 578, 101),
        Among(u"lu\u010Dene", 578, 117),
        Among(u"ine", 578, 11),
        Among(u"cine", 595, 137),
        Among(u"anine", 595, 10),
        Among(u"\u010Dine", 595, 89),
        Among(u"one", 578, 12),
        Among(u"are", -1, 53),
        Among(u"dre", -1, 54),
        Among(u"ere", -1, 55),
        Among(u"ore", -1, 56),
        Among(u"ase", -1, 161),
        Among(u"base", 604, 135),
        Among(u"acase", 604, 128),
        Among(u"gase", 604, 131),
        Among(u"jase", 604, 129),
        Among(u"astajase", 608, 138),
        Among(u"istajase", 608, 139),
        Among(u"ostajase", 608, 140),
        Among(u"injase", 608, 150),
        Among(u"kase", 604, 133),
        Among(u"nase", 604, 132),
        Among(u"irase", 604, 155),
        Among(u"urase", 604, 156),
        Among(u"tase", 604, 130),
        Among(u"vase", 604, 134),
        Among(u"avase", 618, 144),
        Among(u"evase", 618, 145),
        Among(u"ivase", 618, 146),
        Among(u"ovase", 618, 148),
        Among(u"uvase", 618, 147),
        Among(u"ese", -1, 57),
        Among(u"ise", -1, 58),
        Among(u"acise", 625, 124),
        Among(u"lucise", 625, 121),
        Among(u"rosise", 625, 127),
        Among(u"jetise", 625, 149),
        Among(u"ose", -1, 123),
        Among(u"astadose", 630, 141),
        Among(u"istadose", 630, 142),
        Among(u"ostadose", 630, 143),
        Among(u"ate", -1, 104),
        Among(u"acate", 634, 128),
        Among(u"ikate", 634, 68),
        Among(u"late", 634, 69),
        Among(u"irate", 634, 100),
        Among(u"urate", 634, 105),
        Among(u"tate", 634, 113),
        Among(u"avate", 634, 97),
        Among(u"evate", 634, 96),
        Among(u"ivate", 634, 98),
        Among(u"uvate", 634, 99),
        Among(u"a\u010Date", 634, 102),
        Among(u"ete", -1, 70),
        Among(u"astadete", 646, 110),
        Among(u"istadete", 646, 111),
        Among(u"ostadete", 646, 112),
        Among(u"astajete", 646, 106),
        Among(u"istajete", 646, 107),
        Among(u"ostajete", 646, 108),
        Among(u"ijete", 646, 116),
        Among(u"injete", 646, 114),
        Among(u"ujete", 646, 25),
        Among(u"lucujete", 655, 121),
        Among(u"irujete", 655, 100),
        Among(u"lu\u010Dujete", 655, 117),
        Among(u"nete", 646, 13),
        Among(u"astanete", 659, 110),
        Among(u"istanete", 659, 111),
        Among(u"ostanete", 659, 112),
        Among(u"astete", 646, 115),
        Among(u"ite", -1, 116),
        Among(u"acite", 664, 124),
        Among(u"lucite", 664, 121),
        Among(u"nite", 664, 13),
        Among(u"astanite", 667, 110),
        Among(u"istanite", 667, 111),
        Among(u"ostanite", 667, 112),
        Among(u"rosite", 664, 127),
        Among(u"jetite", 664, 118),
        Among(u"astite", 664, 115),
        Among(u"evite", 664, 92),
        Among(u"ovite", 664, 93),
        Among(u"a\u010Dite", 664, 101),
        Among(u"lu\u010Dite", 664, 117),
        Among(u"ro\u0161ite", 664, 90),
        Among(u"ajte", -1, 104),
        Among(u"urajte", 679, 105),
        Among(u"tajte", 679, 113),
        Among(u"astajte", 681, 106),
        Among(u"istajte", 681, 107),
        Among(u"ostajte", 681, 108),
        Among(u"avajte", 679, 97),
        Among(u"evajte", 679, 96),
        Among(u"ivajte", 679, 98),
        Among(u"uvajte", 679, 99),
        Among(u"ijte", -1, 116),
        Among(u"lucujte", -1, 121),
        Among(u"irujte", -1, 100),
        Among(u"lu\u010Dujte", -1, 117),
        Among(u"aste", -1, 94),
        Among(u"acaste", 693, 128),
        Among(u"astajaste", 693, 106),
        Among(u"istajaste", 693, 107),
        Among(u"ostajaste", 693, 108),
        Among(u"injaste", 693, 114),
        Among(u"iraste", 693, 100),
        Among(u"uraste", 693, 105),
        Among(u"taste", 693, 113),
        Among(u"avaste", 693, 97),
        Among(u"evaste", 693, 96),
        Among(u"ivaste", 693, 98),
        Among(u"ovaste", 693, 76),
        Among(u"uvaste", 693, 99),
        Among(u"a\u010Daste", 693, 102),
        Among(u"este", -1, 71),
        Among(u"iste", -1, 72),
        Among(u"aciste", 709, 124),
        Among(u"luciste", 709, 121),
        Among(u"niste", 709, 103),
        Among(u"rosiste", 709, 127),
        Among(u"jetiste", 709, 118),
        Among(u"a\u010Diste", 709, 101),
        Among(u"lu\u010Diste", 709, 117),
        Among(u"ro\u0161iste", 709, 90),
        Among(u"kste", -1, 73),
        Among(u"oste", -1, 74),
        Among(u"astadoste", 719, 110),
        Among(u"istadoste", 719, 111),
        Among(u"ostadoste", 719, 112),
        Among(u"nuste", -1, 13),
        Among(u"i\u0161te", -1, 75),
        Among(u"ave", -1, 77),
        Among(u"eve", -1, 78),
        Among(u"ajeve", 726, 109),
        Among(u"cajeve", 727, 26),
        Among(u"lajeve", 727, 30),
        Among(u"rajeve", 727, 31),
        Among(u"\u0107ajeve", 727, 28),
        Among(u"\u010Dajeve", 727, 27),
        Among(u"\u0111ajeve", 727, 29),
        Among(u"ive", -1, 79),
        Among(u"ove", -1, 80),
        Among(u"gove", 735, 20),
        Among(u"ugove", 736, 17),
        Among(u"love", 735, 82),
        Among(u"olove", 738, 49),
        Among(u"move", 735, 81),
        Among(u"onove", 735, 12),
        Among(u"a\u0107e", -1, 14),
        Among(u"e\u0107e", -1, 15),
        Among(u"u\u0107e", -1, 16),
        Among(u"a\u010De", -1, 101),
        Among(u"lu\u010De", -1, 117),
        Among(u"a\u0161e", -1, 104),
        Among(u"ba\u0161e", 747, 63),
        Among(u"ga\u0161e", 747, 64),
        Among(u"ja\u0161e", 747, 61),
        Among(u"astaja\u0161e", 750, 106),
        Among(u"istaja\u0161e", 750, 107),
        Among(u"ostaja\u0161e", 750, 108),
        Among(u"inja\u0161e", 750, 114),
        Among(u"ka\u0161e", 747, 62),
        Among(u"na\u0161e", 747, 60),
        Among(u"ira\u0161e", 747, 100),
        Among(u"ura\u0161e", 747, 105),
        Among(u"ta\u0161e", 747, 59),
        Among(u"va\u0161e", 747, 65),
        Among(u"ava\u0161e", 760, 97),
        Among(u"eva\u0161e", 760, 96),
        Among(u"iva\u0161e", 760, 98),
        Among(u"ova\u0161e", 760, 76),
        Among(u"uva\u0161e", 760, 99),
        Among(u"a\u010Da\u0161e", 747, 102),
        Among(u"e\u0161e", -1, 66),
        Among(u"i\u0161e", -1, 67),
        Among(u"jeti\u0161e", 768, 118),
        Among(u"a\u010Di\u0161e", 768, 101),
        Among(u"lu\u010Di\u0161e", 768, 117),
        Among(u"ro\u0161i\u0161e", 768, 90),
        Among(u"o\u0161e", -1, 91),
        Among(u"astado\u0161e", 773, 110),
        Among(u"istado\u0161e", 773, 111),
        Among(u"ostado\u0161e", 773, 112),
        Among(u"aceg", -1, 124),
        Among(u"eceg", -1, 125),
        Among(u"uceg", -1, 126),
        Among(u"anjijeg", -1, 84),
        Among(u"enjijeg", -1, 85),
        Among(u"snjijeg", -1, 122),
        Among(u"\u0161njijeg", -1, 86),
        Among(u"kijeg", -1, 95),
        Among(u"skijeg", 784, 1),
        Among(u"\u0161kijeg", 784, 2),
        Among(u"elijeg", -1, 83),
        Among(u"nijeg", -1, 13),
        Among(u"osijeg", -1, 123),
        Among(u"atijeg", -1, 120),
        Among(u"evitijeg", -1, 92),
        Among(u"ovitijeg", -1, 93),
        Among(u"astijeg", -1, 94),
        Among(u"avijeg", -1, 77),
        Among(u"evijeg", -1, 78),
        Among(u"ivijeg", -1, 79),
        Among(u"ovijeg", -1, 80),
        Among(u"o\u0161ijeg", -1, 91),
        Among(u"anjeg", -1, 84),
        Among(u"enjeg", -1, 85),
        Among(u"snjeg", -1, 122),
        Among(u"\u0161njeg", -1, 86),
        Among(u"keg", -1, 95),
        Among(u"eleg", -1, 83),
        Among(u"neg", -1, 13),
        Among(u"aneg", 805, 10),
        Among(u"eneg", 805, 87),
        Among(u"sneg", 805, 159),
        Among(u"\u0161neg", 805, 88),
        Among(u"oseg", -1, 123),
        Among(u"ateg", -1, 120),
        Among(u"aveg", -1, 77),
        Among(u"eveg", -1, 78),
        Among(u"iveg", -1, 79),
        Among(u"oveg", -1, 80),
        Among(u"a\u0107eg", -1, 14),
        Among(u"e\u0107eg", -1, 15),
        Among(u"u\u0107eg", -1, 16),
        Among(u"o\u0161eg", -1, 91),
        Among(u"acog", -1, 124),
        Among(u"ecog", -1, 125),
        Among(u"ucog", -1, 126),
        Among(u"anjog", -1, 84),
        Among(u"enjog", -1, 85),
        Among(u"snjog", -1, 122),
        Among(u"\u0161njog", -1, 86),
        Among(u"kog", -1, 95),
        Among(u"skog", 827, 1),
        Among(u"\u0161kog", 827, 2),
        Among(u"elog", -1, 83),
        Among(u"nog", -1, 13),
        Among(u"cinog", 831, 137),
        Among(u"\u010Dinog", 831, 89),
        Among(u"osog", -1, 123),
        Among(u"atog", -1, 120),
        Among(u"evitog", -1, 92),
        Among(u"ovitog", -1, 93),
        Among(u"astog", -1, 94),
        Among(u"avog", -1, 77),
        Among(u"evog", -1, 78),
        Among(u"ivog", -1, 79),
        Among(u"ovog", -1, 80),
        Among(u"a\u0107og", -1, 14),
        Among(u"e\u0107og", -1, 15),
        Among(u"u\u0107og", -1, 16),
        Among(u"o\u0161og", -1, 91),
        Among(u"ah", -1, 104),
        Among(u"acah", 847, 128),
        Among(u"astajah", 847, 106),
        Among(u"istajah", 847, 107),
        Among(u"ostajah", 847, 108),
        Among(u"injah", 847, 114),
        Among(u"irah", 847, 100),
        Among(u"urah", 847, 105),
        Among(u"tah", 847, 113),
        Among(u"avah", 847, 97),
        Among(u"evah", 847, 96),
        Among(u"ivah", 847, 98),
        Among(u"ovah", 847, 76),
        Among(u"uvah", 847, 99),
        Among(u"a\u010Dah", 847, 102),
        Among(u"ih", -1, 116),
        Among(u"acih", 862, 124),
        Among(u"ecih", 862, 125),
        Among(u"ucih", 862, 126),
        Among(u"lucih", 865, 121),
        Among(u"anjijih", 862, 84),
        Among(u"enjijih", 862, 85),
        Among(u"snjijih", 862, 122),
        Among(u"\u0161njijih", 862, 86),
        Among(u"kijih", 862, 95),
        Among(u"skijih", 871, 1),
        Among(u"\u0161kijih", 871, 2),
        Among(u"elijih", 862, 83),
        Among(u"nijih", 862, 13),
        Among(u"osijih", 862, 123),
        Among(u"atijih", 862, 120),
        Among(u"evitijih", 862, 92),
        Among(u"ovitijih", 862, 93),
        Among(u"astijih", 862, 94),
        Among(u"avijih", 862, 77),
        Among(u"evijih", 862, 78),
        Among(u"ivijih", 862, 79),
        Among(u"ovijih", 862, 80),
        Among(u"o\u0161ijih", 862, 91),
        Among(u"anjih", 862, 84),
        Among(u"enjih", 862, 85),
        Among(u"snjih", 862, 122),
        Among(u"\u0161njih", 862, 86),
        Among(u"kih", 862, 95),
        Among(u"skih", 890, 1),
        Among(u"\u0161kih", 890, 2),
        Among(u"elih", 862, 83),
        Among(u"nih", 862, 13),
        Among(u"cinih", 894, 137),
        Among(u"\u010Dinih", 894, 89),
        Among(u"osih", 862, 123),
        Among(u"rosih", 897, 127),
        Among(u"atih", 862, 120),
        Among(u"jetih", 862, 118),
        Among(u"evitih", 862, 92),
        Among(u"ovitih", 862, 93),
        Among(u"astih", 862, 94),
        Among(u"avih", 862, 77),
        Among(u"evih", 862, 78),
        Among(u"ivih", 862, 79),
        Among(u"ovih", 862, 80),
        Among(u"a\u0107ih", 862, 14),
        Among(u"e\u0107ih", 862, 15),
        Among(u"u\u0107ih", 862, 16),
        Among(u"a\u010Dih", 862, 101),
        Among(u"lu\u010Dih", 862, 117),
        Among(u"o\u0161ih", 862, 91),
        Among(u"ro\u0161ih", 913, 90),
        Among(u"astadoh", -1, 110),
        Among(u"istadoh", -1, 111),
        Among(u"ostadoh", -1, 112),
        Among(u"acuh", -1, 124),
        Among(u"ecuh", -1, 125),
        Among(u"ucuh", -1, 126),
        Among(u"a\u0107uh", -1, 14),
        Among(u"e\u0107uh", -1, 15),
        Among(u"u\u0107uh", -1, 16),
        Among(u"aci", -1, 124),
        Among(u"aceci", -1, 124),
        Among(u"ieci", -1, 162),
        Among(u"ajuci", -1, 161),
        Among(u"irajuci", 927, 155),
        Among(u"urajuci", 927, 156),
        Among(u"astajuci", 927, 138),
        Among(u"istajuci", 927, 139),
        Among(u"ostajuci", 927, 140),
        Among(u"avajuci", 927, 144),
        Among(u"evajuci", 927, 145),
        Among(u"ivajuci", 927, 146),
        Among(u"uvajuci", 927, 147),
        Among(u"ujuci", -1, 157),
        Among(u"lucujuci", 937, 121),
        Among(u"irujuci", 937, 155),
        Among(u"luci", -1, 121),
        Among(u"nuci", -1, 164),
        Among(u"etuci", -1, 153),
        Among(u"astuci", -1, 136),
        Among(u"gi", -1, 20),
        Among(u"ugi", 944, 18),
        Among(u"aji", -1, 109),
        Among(u"caji", 946, 26),
        Among(u"laji", 946, 30),
        Among(u"raji", 946, 31),
        Among(u"\u0107aji", 946, 28),
        Among(u"\u010Daji", 946, 27),
        Among(u"\u0111aji", 946, 29),
        Among(u"biji", -1, 32),
        Among(u"ciji", -1, 33),
        Among(u"diji", -1, 34),
        Among(u"fiji", -1, 40),
        Among(u"giji", -1, 39),
        Among(u"anjiji", -1, 84),
        Among(u"enjiji", -1, 85),
        Among(u"snjiji", -1, 122),
        Among(u"\u0161njiji", -1, 86),
        Among(u"kiji", -1, 95),
        Among(u"skiji", 962, 1),
        Among(u"\u0161kiji", 962, 2),
        Among(u"liji", -1, 35),
        Among(u"eliji", 965, 83),
        Among(u"miji", -1, 37),
        Among(u"niji", -1, 13),
        Among(u"ganiji", 968, 9),
        Among(u"maniji", 968, 6),
        Among(u"paniji", 968, 7),
        Among(u"raniji", 968, 8),
        Among(u"taniji", 968, 5),
        Among(u"piji", -1, 41),
        Among(u"riji", -1, 42),
        Among(u"siji", -1, 43),
        Among(u"osiji", 976, 123),
        Among(u"tiji", -1, 44),
        Among(u"atiji", 978, 120),
        Among(u"evitiji", 978, 92),
        Among(u"ovitiji", 978, 93),
        Among(u"astiji", 978, 94),
        Among(u"aviji", -1, 77),
        Among(u"eviji", -1, 78),
        Among(u"iviji", -1, 79),
        Among(u"oviji", -1, 80),
        Among(u"ziji", -1, 45),
        Among(u"o\u0161iji", -1, 91),
        Among(u"\u017Eiji", -1, 38),
        Among(u"anji", -1, 84),
        Among(u"enji", -1, 85),
        Among(u"snji", -1, 122),
        Among(u"\u0161nji", -1, 86),
        Among(u"ki", -1, 95),
        Among(u"ski", 994, 1),
        Among(u"\u0161ki", 994, 2),
        Among(u"ali", -1, 104),
        Among(u"acali", 997, 128),
        Among(u"astajali", 997, 106),
        Among(u"istajali", 997, 107),
        Among(u"ostajali", 997, 108),
        Among(u"ijali", 997, 47),
        Among(u"injali", 997, 114),
        Among(u"nali", 997, 46),
        Among(u"irali", 997, 100),
        Among(u"urali", 997, 105),
        Among(u"tali", 997, 113),
        Among(u"astali", 1007, 110),
        Among(u"istali", 1007, 111),
        Among(u"ostali", 1007, 112),
        Among(u"avali", 997, 97),
        Among(u"evali", 997, 96),
        Among(u"ivali", 997, 98),
        Among(u"ovali", 997, 76),
        Among(u"uvali", 997, 99),
        Among(u"a\u010Dali", 997, 102),
        Among(u"eli", -1, 83),
        Among(u"ili", -1, 116),
        Among(u"acili", 1018, 124),
        Among(u"lucili", 1018, 121),
        Among(u"nili", 1018, 103),
        Among(u"rosili", 1018, 127),
        Among(u"jetili", 1018, 118),
        Among(u"ozili", 1018, 48),
        Among(u"a\u010Dili", 1018, 101),
        Among(u"lu\u010Dili", 1018, 117),
        Among(u"ro\u0161ili", 1018, 90),
        Among(u"oli", -1, 50),
        Among(u"asli", -1, 115),
        Among(u"nuli", -1, 13),
        Among(u"rami", -1, 52),
        Among(u"lemi", -1, 51),
        Among(u"ni", -1, 13),
        Among(u"ani", 1033, 10),
        Among(u"acani", 1034, 128),
        Among(u"urani", 1034, 105),
        Among(u"tani", 1034, 113),
        Among(u"avani", 1034, 97),
        Among(u"evani", 1034, 96),
        Among(u"ivani", 1034, 98),
        Among(u"uvani", 1034, 99),
        Among(u"a\u010Dani", 1034, 102),
        Among(u"aceni", 1033, 124),
        Among(u"luceni", 1033, 121),
        Among(u"a\u010Deni", 1033, 101),
        Among(u"lu\u010Deni", 1033, 117),
        Among(u"ini", 1033, 11),
        Among(u"cini", 1047, 137),
        Among(u"\u010Dini", 1047, 89),
        Among(u"oni", 1033, 12),
        Among(u"ari", -1, 53),
        Among(u"dri", -1, 54),
        Among(u"eri", -1, 55),
        Among(u"ori", -1, 56),
        Among(u"basi", -1, 135),
        Among(u"gasi", -1, 131),
        Among(u"jasi", -1, 129),
        Among(u"kasi", -1, 133),
        Among(u"nasi", -1, 132),
        Among(u"tasi", -1, 130),
        Among(u"vasi", -1, 134),
        Among(u"esi", -1, 152),
        Among(u"isi", -1, 154),
        Among(u"osi", -1, 123),
        Among(u"avsi", -1, 161),
        Among(u"acavsi", 1065, 128),
        Among(u"iravsi", 1065, 155),
        Among(u"tavsi", 1065, 160),
        Among(u"etavsi", 1068, 153),
        Among(u"astavsi", 1068, 141),
        Among(u"istavsi", 1068, 142),
        Among(u"ostavsi", 1068, 143),
        Among(u"ivsi", -1, 162),
        Among(u"nivsi", 1073, 158),
        Among(u"rosivsi", 1073, 127),
        Among(u"nuvsi", -1, 164),
        Among(u"ati", -1, 104),
        Among(u"acati", 1077, 128),
        Among(u"astajati", 1077, 106),
        Among(u"istajati", 1077, 107),
        Among(u"ostajati", 1077, 108),
        Among(u"injati", 1077, 114),
        Among(u"ikati", 1077, 68),
        Among(u"lati", 1077, 69),
        Among(u"irati", 1077, 100),
        Among(u"urati", 1077, 105),
        Among(u"tati", 1077, 113),
        Among(u"astati", 1087, 110),
        Among(u"istati", 1087, 111),
        Among(u"ostati", 1087, 112),
        Among(u"avati", 1077, 97),
        Among(u"evati", 1077, 96),
        Among(u"ivati", 1077, 98),
        Among(u"ovati", 1077, 76),
        Among(u"uvati", 1077, 99),
        Among(u"a\u010Dati", 1077, 102),
        Among(u"eti", -1, 70),
        Among(u"iti", -1, 116),
        Among(u"aciti", 1098, 124),
        Among(u"luciti", 1098, 121),
        Among(u"niti", 1098, 103),
        Among(u"rositi", 1098, 127),
        Among(u"jetiti", 1098, 118),
        Among(u"eviti", 1098, 92),
        Among(u"oviti", 1098, 93),
        Among(u"a\u010Diti", 1098, 101),
        Among(u"lu\u010Diti", 1098, 117),
        Among(u"ro\u0161iti", 1098, 90),
        Among(u"asti", -1, 94),
        Among(u"esti", -1, 71),
        Among(u"isti", -1, 72),
        Among(u"ksti", -1, 73),
        Among(u"osti", -1, 74),
        Among(u"nuti", -1, 13),
        Among(u"avi", -1, 77),
        Among(u"evi", -1, 78),
        Among(u"ajevi", 1116, 109),
        Among(u"cajevi", 1117, 26),
        Among(u"lajevi", 1117, 30),
        Among(u"rajevi", 1117, 31),
        Among(u"\u0107ajevi", 1117, 28),
        Among(u"\u010Dajevi", 1117, 27),
        Among(u"\u0111ajevi", 1117, 29),
        Among(u"ivi", -1, 79),
        Among(u"ovi", -1, 80),
        Among(u"govi", 1125, 20),
        Among(u"ugovi", 1126, 17),
        Among(u"lovi", 1125, 82),
        Among(u"olovi", 1128, 49),
        Among(u"movi", 1125, 81),
        Among(u"onovi", 1125, 12),
        Among(u"ie\u0107i", -1, 116),
        Among(u"a\u010De\u0107i", -1, 101),
        Among(u"aju\u0107i", -1, 104),
        Among(u"iraju\u0107i", 1134, 100),
        Among(u"uraju\u0107i", 1134, 105),
        Among(u"astaju\u0107i", 1134, 106),
        Among(u"istaju\u0107i", 1134, 107),
        Among(u"ostaju\u0107i", 1134, 108),
        Among(u"avaju\u0107i", 1134, 97),
        Among(u"evaju\u0107i", 1134, 96),
        Among(u"ivaju\u0107i", 1134, 98),
        Among(u"uvaju\u0107i", 1134, 99),
        Among(u"uju\u0107i", -1, 25),
        Among(u"iruju\u0107i", 1144, 100),
        Among(u"lu\u010Duju\u0107i", 1144, 117),
        Among(u"nu\u0107i", -1, 13),
        Among(u"etu\u0107i", -1, 70),
        Among(u"astu\u0107i", -1, 115),
        Among(u"a\u010Di", -1, 101),
        Among(u"lu\u010Di", -1, 117),
        Among(u"ba\u0161i", -1, 63),
        Among(u"ga\u0161i", -1, 64),
        Among(u"ja\u0161i", -1, 61),
        Among(u"ka\u0161i", -1, 62),
        Among(u"na\u0161i", -1, 60),
        Among(u"ta\u0161i", -1, 59),
        Among(u"va\u0161i", -1, 65),
        Among(u"e\u0161i", -1, 66),
        Among(u"i\u0161i", -1, 67),
        Among(u"o\u0161i", -1, 91),
        Among(u"av\u0161i", -1, 104),
        Among(u"irav\u0161i", 1162, 100),
        Among(u"tav\u0161i", 1162, 113),
        Among(u"etav\u0161i", 1164, 70),
        Among(u"astav\u0161i", 1164, 110),
        Among(u"istav\u0161i", 1164, 111),
        Among(u"ostav\u0161i", 1164, 112),
        Among(u"a\u010Dav\u0161i", 1162, 102),
        Among(u"iv\u0161i", -1, 116),
        Among(u"niv\u0161i", 1170, 103),
        Among(u"ro\u0161iv\u0161i", 1170, 90),
        Among(u"nuv\u0161i", -1, 13),
        Among(u"aj", -1, 104),
        Among(u"uraj", 1174, 105),
        Among(u"taj", 1174, 113),
        Among(u"avaj", 1174, 97),
        Among(u"evaj", 1174, 96),
        Among(u"ivaj", 1174, 98),
        Among(u"uvaj", 1174, 99),
        Among(u"ij", -1, 116),
        Among(u"acoj", -1, 124),
        Among(u"ecoj", -1, 125),
        Among(u"ucoj", -1, 126),
        Among(u"anjijoj", -1, 84),
        Among(u"enjijoj", -1, 85),
        Among(u"snjijoj", -1, 122),
        Among(u"\u0161njijoj", -1, 86),
        Among(u"kijoj", -1, 95),
        Among(u"skijoj", 1189, 1),
        Among(u"\u0161kijoj", 1189, 2),
        Among(u"elijoj", -1, 83),
        Among(u"nijoj", -1, 13),
        Among(u"osijoj", -1, 123),
        Among(u"evitijoj", -1, 92),
        Among(u"ovitijoj", -1, 93),
        Among(u"astijoj", -1, 94),
        Among(u"avijoj", -1, 77),
        Among(u"evijoj", -1, 78),
        Among(u"ivijoj", -1, 79),
        Among(u"ovijoj", -1, 80),
        Among(u"o\u0161ijoj", -1, 91),
        Among(u"anjoj", -1, 84),
        Among(u"enjoj", -1, 85),
        Among(u"snjoj", -1, 122),
        Among(u"\u0161njoj", -1, 86),
        Among(u"koj", -1, 95),
        Among(u"skoj", 1207, 1),
        Among(u"\u0161koj", 1207, 2),
        Among(u"aloj", -1, 104),
        Among(u"eloj", -1, 83),
        Among(u"noj", -1, 13),
        Among(u"cinoj", 1212, 137),
        Among(u"\u010Dinoj", 1212, 89),
        Among(u"osoj", -1, 123),
        Among(u"atoj", -1, 120),
        Among(u"evitoj", -1, 92),
        Among(u"ovitoj", -1, 93),
        Among(u"astoj", -1, 94),
        Among(u"avoj", -1, 77),
        Among(u"evoj", -1, 78),
        Among(u"ivoj", -1, 79),
        Among(u"ovoj", -1, 80),
        Among(u"a\u0107oj", -1, 14),
        Among(u"e\u0107oj", -1, 15),
        Among(u"u\u0107oj", -1, 16),
        Among(u"o\u0161oj", -1, 91),
        Among(u"lucuj", -1, 121),
        Among(u"iruj", -1, 100),
        Among(u"lu\u010Duj", -1, 117),
        Among(u"al", -1, 104),
        Among(u"iral", 1231, 100),
        Among(u"ural", 1231, 105),
        Among(u"el", -1, 119),
        Among(u"il", -1, 116),
        Among(u"am", -1, 104),
        Among(u"acam", 1236, 128),
        Among(u"iram", 1236, 100),
        Among(u"uram", 1236, 105),
        Among(u"tam", 1236, 113),
        Among(u"avam", 1236, 97),
        Among(u"evam", 1236, 96),
        Among(u"ivam", 1236, 98),
        Among(u"uvam", 1236, 99),
        Among(u"a\u010Dam", 1236, 102),
        Among(u"em", -1, 119),
        Among(u"acem", 1246, 124),
        Among(u"ecem", 1246, 125),
        Among(u"ucem", 1246, 126),
        Among(u"astadem", 1246, 110),
        Among(u"istadem", 1246, 111),
        Among(u"ostadem", 1246, 112),
        Among(u"ajem", 1246, 104),
        Among(u"cajem", 1253, 26),
        Among(u"lajem", 1253, 30),
        Among(u"rajem", 1253, 31),
        Among(u"astajem", 1253, 106),
        Among(u"istajem", 1253, 107),
        Among(u"ostajem", 1253, 108),
        Among(u"\u0107ajem", 1253, 28),
        Among(u"\u010Dajem", 1253, 27),
        Among(u"\u0111ajem", 1253, 29),
        Among(u"ijem", 1246, 116),
        Among(u"anjijem", 1263, 84),
        Among(u"enjijem", 1263, 85),
        Among(u"snjijem", 1263, 123),
        Among(u"\u0161njijem", 1263, 86),
        Among(u"kijem", 1263, 95),
        Among(u"skijem", 1268, 1),
        Among(u"\u0161kijem", 1268, 2),
        Among(u"lijem", 1263, 24),
        Among(u"elijem", 1271, 83),
        Among(u"nijem", 1263, 13),
        Among(u"rarijem", 1263, 21),
        Among(u"sijem", 1263, 23),
        Among(u"osijem", 1275, 123),
        Among(u"atijem", 1263, 120),
        Among(u"evitijem", 1263, 92),
        Among(u"ovitijem", 1263, 93),
        Among(u"otijem", 1263, 22),
        Among(u"astijem", 1263, 94),
        Among(u"avijem", 1263, 77),
        Among(u"evijem", 1263, 78),
        Among(u"ivijem", 1263, 79),
        Among(u"ovijem", 1263, 80),
        Among(u"o\u0161ijem", 1263, 91),
        Among(u"anjem", 1246, 84),
        Among(u"enjem", 1246, 85),
        Among(u"injem", 1246, 114),
        Among(u"snjem", 1246, 122),
        Among(u"\u0161njem", 1246, 86),
        Among(u"ujem", 1246, 25),
        Among(u"lucujem", 1292, 121),
        Among(u"irujem", 1292, 100),
        Among(u"lu\u010Dujem", 1292, 117),
        Among(u"kem", 1246, 95),
        Among(u"skem", 1296, 1),
        Among(u"\u0161kem", 1296, 2),
        Among(u"elem", 1246, 83),
        Among(u"nem", 1246, 13),
        Among(u"anem", 1300, 10),
        Among(u"astanem", 1301, 110),
        Among(u"istanem", 1301, 111),
        Among(u"ostanem", 1301, 112),
        Among(u"enem", 1300, 87),
        Among(u"snem", 1300, 159),
        Among(u"\u0161nem", 1300, 88),
        Among(u"basem", 1246, 135),
        Among(u"gasem", 1246, 131),
        Among(u"jasem", 1246, 129),
        Among(u"kasem", 1246, 133),
        Among(u"nasem", 1246, 132),
        Among(u"tasem", 1246, 130),
        Among(u"vasem", 1246, 134),
        Among(u"esem", 1246, 152),
        Among(u"isem", 1246, 154),
        Among(u"osem", 1246, 123),
        Among(u"atem", 1246, 120),
        Among(u"etem", 1246, 70),
        Among(u"evitem", 1246, 92),
        Among(u"ovitem", 1246, 93),
        Among(u"astem", 1246, 94),
        Among(u"istem", 1246, 151),
        Among(u"i\u0161tem", 1246, 75),
        Among(u"avem", 1246, 77),
        Among(u"evem", 1246, 78),
        Among(u"ivem", 1246, 79),
        Among(u"a\u0107em", 1246, 14),
        Among(u"e\u0107em", 1246, 15),
        Among(u"u\u0107em", 1246, 16),
        Among(u"ba\u0161em", 1246, 63),
        Among(u"ga\u0161em", 1246, 64),
        Among(u"ja\u0161em", 1246, 61),
        Among(u"ka\u0161em", 1246, 62),
        Among(u"na\u0161em", 1246, 60),
        Among(u"ta\u0161em", 1246, 59),
        Among(u"va\u0161em", 1246, 65),
        Among(u"e\u0161em", 1246, 66),
        Among(u"i\u0161em", 1246, 67),
        Among(u"o\u0161em", 1246, 91),
        Among(u"im", -1, 116),
        Among(u"acim", 1341, 124),
        Among(u"ecim", 1341, 125),
        Among(u"ucim", 1341, 126),
        Among(u"lucim", 1344, 121),
        Among(u"anjijim", 1341, 84),
        Among(u"enjijim", 1341, 85),
        Among(u"snjijim", 1341, 122),
        Among(u"\u0161njijim", 1341, 86),
        Among(u"kijim", 1341, 95),
        Among(u"skijim", 1350, 1),
        Among(u"\u0161kijim", 1350, 2),
        Among(u"elijim", 1341, 83),
        Among(u"nijim", 1341, 13),
        Among(u"osijim", 1341, 123),
        Among(u"atijim", 1341, 120),
        Among(u"evitijim", 1341, 92),
        Among(u"ovitijim", 1341, 93),
        Among(u"astijim", 1341, 94),
        Among(u"avijim", 1341, 77),
        Among(u"evijim", 1341, 78),
        Among(u"ivijim", 1341, 79),
        Among(u"ovijim", 1341, 80),
        Among(u"o\u0161ijim", 1341, 91),
        Among(u"anjim", 1341, 84),
        Among(u"enjim", 1341, 85),
        Among(u"snjim", 1341, 122),
        Among(u"\u0161njim", 1341, 86),
        Among(u"kim", 1341, 95),
        Among(u"skim", 1369, 1),
        Among(u"\u0161kim", 1369, 2),
        Among(u"elim", 1341, 83),
        Among(u"nim", 1341, 13),
        Among(u"cinim", 1373, 137),
        Among(u"\u010Dinim", 1373, 89),
        Among(u"osim", 1341, 123),
        Among(u"rosim", 1376, 127),
        Among(u"atim", 1341, 120),
        Among(u"jetim", 1341, 118),
        Among(u"evitim", 1341, 92),
        Among(u"ovitim", 1341, 93),
        Among(u"astim", 1341, 94),
        Among(u"avim", 1341, 77),
        Among(u"evim", 1341, 78),
        Among(u"ivim", 1341, 79),
        Among(u"ovim", 1341, 80),
        Among(u"a\u0107im", 1341, 14),
        Among(u"e\u0107im", 1341, 15),
        Among(u"u\u0107im", 1341, 16),
        Among(u"a\u010Dim", 1341, 101),
        Among(u"lu\u010Dim", 1341, 117),
        Among(u"o\u0161im", 1341, 91),
        Among(u"ro\u0161im", 1392, 90),
        Among(u"acom", -1, 124),
        Among(u"ecom", -1, 125),
        Among(u"ucom", -1, 126),
        Among(u"gom", -1, 20),
        Among(u"logom", 1397, 19),
        Among(u"ugom", 1397, 18),
        Among(u"bijom", -1, 32),
        Among(u"cijom", -1, 33),
        Among(u"dijom", -1, 34),
        Among(u"fijom", -1, 40),
        Among(u"gijom", -1, 39),
        Among(u"lijom", -1, 35),
        Among(u"mijom", -1, 37),
        Among(u"nijom", -1, 36),
        Among(u"ganijom", 1407, 9),
        Among(u"manijom", 1407, 6),
        Among(u"panijom", 1407, 7),
        Among(u"ranijom", 1407, 8),
        Among(u"tanijom", 1407, 5),
        Among(u"pijom", -1, 41),
        Among(u"rijom", -1, 42),
        Among(u"sijom", -1, 43),
        Among(u"tijom", -1, 44),
        Among(u"zijom", -1, 45),
        Among(u"\u017Eijom", -1, 38),
        Among(u"anjom", -1, 84),
        Among(u"enjom", -1, 85),
        Among(u"snjom", -1, 122),
        Among(u"\u0161njom", -1, 86),
        Among(u"kom", -1, 95),
        Among(u"skom", 1423, 1),
        Among(u"\u0161kom", 1423, 2),
        Among(u"alom", -1, 104),
        Among(u"ijalom", 1426, 47),
        Among(u"nalom", 1426, 46),
        Among(u"elom", -1, 83),
        Among(u"ilom", -1, 116),
        Among(u"ozilom", 1430, 48),
        Among(u"olom", -1, 50),
        Among(u"ramom", -1, 52),
        Among(u"lemom", -1, 51),
        Among(u"nom", -1, 13),
        Among(u"anom", 1435, 10),
        Among(u"inom", 1435, 11),
        Among(u"cinom", 1437, 137),
        Among(u"aninom", 1437, 10),
        Among(u"\u010Dinom", 1437, 89),
        Among(u"onom", 1435, 12),
        Among(u"arom", -1, 53),
        Among(u"drom", -1, 54),
        Among(u"erom", -1, 55),
        Among(u"orom", -1, 56),
        Among(u"basom", -1, 135),
        Among(u"gasom", -1, 131),
        Among(u"jasom", -1, 129),
        Among(u"kasom", -1, 133),
        Among(u"nasom", -1, 132),
        Among(u"tasom", -1, 130),
        Among(u"vasom", -1, 134),
        Among(u"esom", -1, 57),
        Among(u"isom", -1, 58),
        Among(u"osom", -1, 123),
        Among(u"atom", -1, 120),
        Among(u"ikatom", 1456, 68),
        Among(u"latom", 1456, 69),
        Among(u"etom", -1, 70),
        Among(u"evitom", -1, 92),
        Among(u"ovitom", -1, 93),
        Among(u"astom", -1, 94),
        Among(u"estom", -1, 71),
        Among(u"istom", -1, 72),
        Among(u"kstom", -1, 73),
        Among(u"ostom", -1, 74),
        Among(u"avom", -1, 77),
        Among(u"evom", -1, 78),
        Among(u"ivom", -1, 79),
        Among(u"ovom", -1, 80),
        Among(u"lovom", 1470, 82),
        Among(u"movom", 1470, 81),
        Among(u"stvom", -1, 3),
        Among(u"\u0161tvom", -1, 4),
        Among(u"a\u0107om", -1, 14),
        Among(u"e\u0107om", -1, 15),
        Among(u"u\u0107om", -1, 16),
        Among(u"ba\u0161om", -1, 63),
        Among(u"ga\u0161om", -1, 64),
        Among(u"ja\u0161om", -1, 61),
        Among(u"ka\u0161om", -1, 62),
        Among(u"na\u0161om", -1, 60),
        Among(u"ta\u0161om", -1, 59),
        Among(u"va\u0161om", -1, 65),
        Among(u"e\u0161om", -1, 66),
        Among(u"i\u0161om", -1, 67),
        Among(u"o\u0161om", -1, 91),
        Among(u"an", -1, 104),
        Among(u"acan", 1488, 128),
        Among(u"iran", 1488, 100),
        Among(u"uran", 1488, 105),
        Among(u"tan", 1488, 113),
        Among(u"avan", 1488, 97),
        Among(u"evan", 1488, 96),
        Among(u"ivan", 1488, 98),
        Among(u"uvan", 1488, 99),
        Among(u"a\u010Dan", 1488, 102),
        Among(u"acen", -1, 124),
        Among(u"lucen", -1, 121),
        Among(u"a\u010Den", -1, 101),
        Among(u"lu\u010Den", -1, 117),
        Among(u"anin", -1, 10),
        Among(u"ao", -1, 104),
        Among(u"acao", 1503, 128),
        Among(u"astajao", 1503, 106),
        Among(u"istajao", 1503, 107),
        Among(u"ostajao", 1503, 108),
        Among(u"injao", 1503, 114),
        Among(u"irao", 1503, 100),
        Among(u"urao", 1503, 105),
        Among(u"tao", 1503, 113),
        Among(u"astao", 1511, 110),
        Among(u"istao", 1511, 111),
        Among(u"ostao", 1511, 112),
        Among(u"avao", 1503, 97),
        Among(u"evao", 1503, 96),
        Among(u"ivao", 1503, 98),
        Among(u"ovao", 1503, 76),
        Among(u"uvao", 1503, 99),
        Among(u"a\u010Dao", 1503, 102),
        Among(u"go", -1, 20),
        Among(u"ugo", 1521, 18),
        Among(u"io", -1, 116),
        Among(u"acio", 1523, 124),
        Among(u"lucio", 1523, 121),
        Among(u"lio", 1523, 24),
        Among(u"nio", 1523, 103),
        Among(u"rario", 1523, 21),
        Among(u"sio", 1523, 23),
        Among(u"rosio", 1529, 127),
        Among(u"jetio", 1523, 118),
        Among(u"otio", 1523, 22),
        Among(u"a\u010Dio", 1523, 101),
        Among(u"lu\u010Dio", 1523, 117),
        Among(u"ro\u0161io", 1523, 90),
        Among(u"bijo", -1, 32),
        Among(u"cijo", -1, 33),
        Among(u"dijo", -1, 34),
        Among(u"fijo", -1, 40),
        Among(u"gijo", -1, 39),
        Among(u"lijo", -1, 35),
        Among(u"mijo", -1, 37),
        Among(u"nijo", -1, 36),
        Among(u"pijo", -1, 41),
        Among(u"rijo", -1, 42),
        Among(u"sijo", -1, 43),
        Among(u"tijo", -1, 44),
        Among(u"zijo", -1, 45),
        Among(u"\u017Eijo", -1, 38),
        Among(u"anjo", -1, 84),
        Among(u"enjo", -1, 85),
        Among(u"snjo", -1, 122),
        Among(u"\u0161njo", -1, 86),
        Among(u"ko", -1, 95),
        Among(u"sko", 1554, 1),
        Among(u"\u0161ko", 1554, 2),
        Among(u"alo", -1, 104),
        Among(u"acalo", 1557, 128),
        Among(u"astajalo", 1557, 106),
        Among(u"istajalo", 1557, 107),
        Among(u"ostajalo", 1557, 108),
        Among(u"ijalo", 1557, 47),
        Among(u"injalo", 1557, 114),
        Among(u"nalo", 1557, 46),
        Among(u"iralo", 1557, 100),
        Among(u"uralo", 1557, 105),
        Among(u"talo", 1557, 113),
        Among(u"astalo", 1567, 110),
        Among(u"istalo", 1567, 111),
        Among(u"ostalo", 1567, 112),
        Among(u"avalo", 1557, 97),
        Among(u"evalo", 1557, 96),
        Among(u"ivalo", 1557, 98),
        Among(u"ovalo", 1557, 76),
        Among(u"uvalo", 1557, 99),
        Among(u"a\u010Dalo", 1557, 102),
        Among(u"elo", -1, 83),
        Among(u"ilo", -1, 116),
        Among(u"acilo", 1578, 124),
        Among(u"lucilo", 1578, 121),
        Among(u"nilo", 1578, 103),
        Among(u"rosilo", 1578, 127),
        Among(u"jetilo", 1578, 118),
        Among(u"a\u010Dilo", 1578, 101),
        Among(u"lu\u010Dilo", 1578, 117),
        Among(u"ro\u0161ilo", 1578, 90),
        Among(u"aslo", -1, 115),
        Among(u"nulo", -1, 13),
        Among(u"amo", -1, 104),
        Among(u"acamo", 1589, 128),
        Among(u"ramo", 1589, 52),
        Among(u"iramo", 1591, 100),
        Among(u"uramo", 1591, 105),
        Among(u"tamo", 1589, 113),
        Among(u"avamo", 1589, 97),
        Among(u"evamo", 1589, 96),
        Among(u"ivamo", 1589, 98),
        Among(u"uvamo", 1589, 99),
        Among(u"a\u010Damo", 1589, 102),
        Among(u"emo", -1, 119),
        Among(u"astademo", 1600, 110),
        Among(u"istademo", 1600, 111),
        Among(u"ostademo", 1600, 112),
        Among(u"astajemo", 1600, 106),
        Among(u"istajemo", 1600, 107),
        Among(u"ostajemo", 1600, 108),
        Among(u"ijemo", 1600, 116),
        Among(u"injemo", 1600, 114),
        Among(u"ujemo", 1600, 25),
        Among(u"lucujemo", 1609, 121),
        Among(u"irujemo", 1609, 100),
        Among(u"lu\u010Dujemo", 1609, 117),
        Among(u"lemo", 1600, 51),
        Among(u"nemo", 1600, 13),
        Among(u"astanemo", 1614, 110),
        Among(u"istanemo", 1614, 111),
        Among(u"ostanemo", 1614, 112),
        Among(u"etemo", 1600, 70),
        Among(u"astemo", 1600, 115),
        Among(u"imo", -1, 116),
        Among(u"acimo", 1620, 124),
        Among(u"lucimo", 1620, 121),
        Among(u"nimo", 1620, 13),
        Among(u"astanimo", 1623, 110),
        Among(u"istanimo", 1623, 111),
        Among(u"ostanimo", 1623, 112),
        Among(u"rosimo", 1620, 127),
        Among(u"etimo", 1620, 70),
        Among(u"jetimo", 1628, 118),
        Among(u"astimo", 1620, 115),
        Among(u"a\u010Dimo", 1620, 101),
        Among(u"lu\u010Dimo", 1620, 117),
        Among(u"ro\u0161imo", 1620, 90),
        Among(u"ajmo", -1, 104),
        Among(u"urajmo", 1634, 105),
        Among(u"tajmo", 1634, 113),
        Among(u"astajmo", 1636, 106),
        Among(u"istajmo", 1636, 107),
        Among(u"ostajmo", 1636, 108),
        Among(u"avajmo", 1634, 97),
        Among(u"evajmo", 1634, 96),
        Among(u"ivajmo", 1634, 98),
        Among(u"uvajmo", 1634, 99),
        Among(u"ijmo", -1, 116),
        Among(u"ujmo", -1, 25),
        Among(u"lucujmo", 1645, 121),
        Among(u"irujmo", 1645, 100),
        Among(u"lu\u010Dujmo", 1645, 117),
        Among(u"asmo", -1, 104),
        Among(u"acasmo", 1649, 128),
        Among(u"astajasmo", 1649, 106),
        Among(u"istajasmo", 1649, 107),
        Among(u"ostajasmo", 1649, 108),
        Among(u"injasmo", 1649, 114),
        Among(u"irasmo", 1649, 100),
        Among(u"urasmo", 1649, 105),
        Among(u"tasmo", 1649, 113),
        Among(u"avasmo", 1649, 97),
        Among(u"evasmo", 1649, 96),
        Among(u"ivasmo", 1649, 98),
        Among(u"ovasmo", 1649, 76),
        Among(u"uvasmo", 1649, 99),
        Among(u"a\u010Dasmo", 1649, 102),
        Among(u"ismo", -1, 116),
        Among(u"acismo", 1664, 124),
        Among(u"lucismo", 1664, 121),
        Among(u"nismo", 1664, 103),
        Among(u"rosismo", 1664, 127),
        Among(u"jetismo", 1664, 118),
        Among(u"a\u010Dismo", 1664, 101),
        Among(u"lu\u010Dismo", 1664, 117),
        Among(u"ro\u0161ismo", 1664, 90),
        Among(u"astadosmo", -1, 110),
        Among(u"istadosmo", -1, 111),
        Among(u"ostadosmo", -1, 112),
        Among(u"nusmo", -1, 13),
        Among(u"no", -1, 13),
        Among(u"ano", 1677, 104),
        Among(u"acano", 1678, 128),
        Among(u"urano", 1678, 105),
        Among(u"tano", 1678, 113),
        Among(u"avano", 1678, 97),
        Among(u"evano", 1678, 96),
        Among(u"ivano", 1678, 98),
        Among(u"uvano", 1678, 99),
        Among(u"a\u010Dano", 1678, 102),
        Among(u"aceno", 1677, 124),
        Among(u"luceno", 1677, 121),
        Among(u"a\u010Deno", 1677, 101),
        Among(u"lu\u010Deno", 1677, 117),
        Among(u"ino", 1677, 11),
        Among(u"cino", 1691, 137),
        Among(u"\u010Dino", 1691, 89),
        Among(u"ato", -1, 120),
        Among(u"ikato", 1694, 68),
        Among(u"lato", 1694, 69),
        Among(u"eto", -1, 70),
        Among(u"evito", -1, 92),
        Among(u"ovito", -1, 93),
        Among(u"asto", -1, 94),
        Among(u"esto", -1, 71),
        Among(u"isto", -1, 72),
        Among(u"ksto", -1, 73),
        Among(u"osto", -1, 74),
        Among(u"nuto", -1, 13),
        Among(u"nuo", -1, 13),
        Among(u"avo", -1, 77),
        Among(u"evo", -1, 78),
        Among(u"ivo", -1, 79),
        Among(u"ovo", -1, 80),
        Among(u"stvo", -1, 3),
        Among(u"\u0161tvo", -1, 4),
        Among(u"as", -1, 161),
        Among(u"acas", 1713, 128),
        Among(u"iras", 1713, 155),
        Among(u"uras", 1713, 156),
        Among(u"tas", 1713, 160),
        Among(u"avas", 1713, 144),
        Among(u"evas", 1713, 145),
        Among(u"ivas", 1713, 146),
        Among(u"uvas", 1713, 147),
        Among(u"es", -1, 163),
        Among(u"astades", 1722, 141),
        Among(u"istades", 1722, 142),
        Among(u"ostades", 1722, 143),
        Among(u"astajes", 1722, 138),
        Among(u"istajes", 1722, 139),
        Among(u"ostajes", 1722, 140),
        Among(u"ijes", 1722, 162),
        Among(u"injes", 1722, 150),
        Among(u"ujes", 1722, 157),
        Among(u"lucujes", 1731, 121),
        Among(u"irujes", 1731, 155),
        Among(u"nes", 1722, 164),
        Among(u"astanes", 1734, 141),
        Among(u"istanes", 1734, 142),
        Among(u"ostanes", 1734, 143),
        Among(u"etes", 1722, 153),
        Among(u"astes", 1722, 136),
        Among(u"is", -1, 162),
        Among(u"acis", 1740, 124),
        Among(u"lucis", 1740, 121),
        Among(u"nis", 1740, 158),
        Among(u"rosis", 1740, 127),
        Among(u"jetis", 1740, 149),
        Among(u"at", -1, 104),
        Among(u"acat", 1746, 128),
        Among(u"astajat", 1746, 106),
        Among(u"istajat", 1746, 107),
        Among(u"ostajat", 1746, 108),
        Among(u"injat", 1746, 114),
        Among(u"irat", 1746, 100),
        Among(u"urat", 1746, 105),
        Among(u"tat", 1746, 113),
        Among(u"astat", 1754, 110),
        Among(u"istat", 1754, 111),
        Among(u"ostat", 1754, 112),
        Among(u"avat", 1746, 97),
        Among(u"evat", 1746, 96),
        Among(u"ivat", 1746, 98),
        Among(u"irivat", 1760, 100),
        Among(u"ovat", 1746, 76),
        Among(u"uvat", 1746, 99),
        Among(u"a\u010Dat", 1746, 102),
        Among(u"it", -1, 116),
        Among(u"acit", 1765, 124),
        Among(u"lucit", 1765, 121),
        Among(u"rosit", 1765, 127),
        Among(u"jetit", 1765, 118),
        Among(u"a\u010Dit", 1765, 101),
        Among(u"lu\u010Dit", 1765, 117),
        Among(u"ro\u0161it", 1765, 90),
        Among(u"nut", -1, 13),
        Among(u"astadu", -1, 110),
        Among(u"istadu", -1, 111),
        Among(u"ostadu", -1, 112),
        Among(u"gu", -1, 20),
        Among(u"logu", 1777, 19),
        Among(u"ugu", 1777, 18),
        Among(u"ahu", -1, 104),
        Among(u"acahu", 1780, 128),
        Among(u"astajahu", 1780, 106),
        Among(u"istajahu", 1780, 107),
        Among(u"ostajahu", 1780, 108),
        Among(u"injahu", 1780, 114),
        Among(u"irahu", 1780, 100),
        Among(u"urahu", 1780, 105),
        Among(u"avahu", 1780, 97),
        Among(u"evahu", 1780, 96),
        Among(u"ivahu", 1780, 98),
        Among(u"ovahu", 1780, 76),
        Among(u"uvahu", 1780, 99),
        Among(u"a\u010Dahu", 1780, 102),
        Among(u"aju", -1, 104),
        Among(u"caju", 1794, 26),
        Among(u"acaju", 1795, 128),
        Among(u"laju", 1794, 30),
        Among(u"raju", 1794, 31),
        Among(u"iraju", 1798, 100),
        Among(u"uraju", 1798, 105),
        Among(u"taju", 1794, 113),
        Among(u"astaju", 1801, 106),
        Among(u"istaju", 1801, 107),
        Among(u"ostaju", 1801, 108),
        Among(u"avaju", 1794, 97),
        Among(u"evaju", 1794, 96),
        Among(u"ivaju", 1794, 98),
        Among(u"uvaju", 1794, 99),
        Among(u"\u0107aju", 1794, 28),
        Among(u"\u010Daju", 1794, 27),
        Among(u"a\u010Daju", 1810, 102),
        Among(u"\u0111aju", 1794, 29),
        Among(u"iju", -1, 116),
        Among(u"biju", 1813, 32),
        Among(u"ciju", 1813, 33),
        Among(u"diju", 1813, 34),
        Among(u"fiju", 1813, 40),
        Among(u"giju", 1813, 39),
        Among(u"anjiju", 1813, 84),
        Among(u"enjiju", 1813, 85),
        Among(u"snjiju", 1813, 122),
        Among(u"\u0161njiju", 1813, 86),
        Among(u"kiju", 1813, 95),
        Among(u"liju", 1813, 24),
        Among(u"eliju", 1824, 83),
        Among(u"miju", 1813, 37),
        Among(u"niju", 1813, 13),
        Among(u"ganiju", 1827, 9),
        Among(u"maniju", 1827, 6),
        Among(u"paniju", 1827, 7),
        Among(u"raniju", 1827, 8),
        Among(u"taniju", 1827, 5),
        Among(u"piju", 1813, 41),
        Among(u"riju", 1813, 42),
        Among(u"rariju", 1834, 21),
        Among(u"siju", 1813, 23),
        Among(u"osiju", 1836, 123),
        Among(u"tiju", 1813, 44),
        Among(u"atiju", 1838, 120),
        Among(u"otiju", 1838, 22),
        Among(u"aviju", 1813, 77),
        Among(u"eviju", 1813, 78),
        Among(u"iviju", 1813, 79),
        Among(u"oviju", 1813, 80),
        Among(u"ziju", 1813, 45),
        Among(u"o\u0161iju", 1813, 91),
        Among(u"\u017Eiju", 1813, 38),
        Among(u"anju", -1, 84),
        Among(u"enju", -1, 85),
        Among(u"snju", -1, 122),
        Among(u"\u0161nju", -1, 86),
        Among(u"uju", -1, 25),
        Among(u"lucuju", 1852, 121),
        Among(u"iruju", 1852, 100),
        Among(u"lu\u010Duju", 1852, 117),
        Among(u"ku", -1, 95),
        Among(u"sku", 1856, 1),
        Among(u"\u0161ku", 1856, 2),
        Among(u"alu", -1, 104),
        Among(u"ijalu", 1859, 47),
        Among(u"nalu", 1859, 46),
        Among(u"elu", -1, 83),
        Among(u"ilu", -1, 116),
        Among(u"ozilu", 1863, 48),
        Among(u"olu", -1, 50),
        Among(u"ramu", -1, 52),
        Among(u"acemu", -1, 124),
        Among(u"ecemu", -1, 125),
        Among(u"ucemu", -1, 126),
        Among(u"anjijemu", -1, 84),
        Among(u"enjijemu", -1, 85),
        Among(u"snjijemu", -1, 122),
        Among(u"\u0161njijemu", -1, 86),
        Among(u"kijemu", -1, 95),
        Among(u"skijemu", 1874, 1),
        Among(u"\u0161kijemu", 1874, 2),
        Among(u"elijemu", -1, 83),
        Among(u"nijemu", -1, 13),
        Among(u"osijemu", -1, 123),
        Among(u"atijemu", -1, 120),
        Among(u"evitijemu", -1, 92),
        Among(u"ovitijemu", -1, 93),
        Among(u"astijemu", -1, 94),
        Among(u"avijemu", -1, 77),
        Among(u"evijemu", -1, 78),
        Among(u"ivijemu", -1, 79),
        Among(u"ovijemu", -1, 80),
        Among(u"o\u0161ijemu", -1, 91),
        Among(u"anjemu", -1, 84),
        Among(u"enjemu", -1, 85),
        Among(u"snjemu", -1, 122),
        Among(u"\u0161njemu", -1, 86),
        Among(u"kemu", -1, 95),
        Among(u"skemu", 1893, 1),
        Among(u"\u0161kemu", 1893, 2),
        Among(u"lemu", -1, 51),
        Among(u"elemu", 1896, 83),
        Among(u"nemu", -1, 13),
        Among(u"anemu", 1898, 10),
        Among(u"enemu", 1898, 87),
        Among(u"snemu", 1898, 159),
        Among(u"\u0161nemu", 1898, 88),
        Among(u"osemu", -1, 123),
        Among(u"atemu", -1, 120),
        Among(u"evitemu", -1, 92),
        Among(u"ovitemu", -1, 93),
        Among(u"astemu", -1, 94),
        Among(u"avemu", -1, 77),
        Among(u"evemu", -1, 78),
        Among(u"ivemu", -1, 79),
        Among(u"ovemu", -1, 80),
        Among(u"a\u0107emu", -1, 14),
        Among(u"e\u0107emu", -1, 15),
        Among(u"u\u0107emu", -1, 16),
        Among(u"o\u0161emu", -1, 91),
        Among(u"acomu", -1, 124),
        Among(u"ecomu", -1, 125),
        Among(u"ucomu", -1, 126),
        Among(u"anjomu", -1, 84),
        Among(u"enjomu", -1, 85),
        Among(u"snjomu", -1, 122),
        Among(u"\u0161njomu", -1, 86),
        Among(u"komu", -1, 95),
        Among(u"skomu", 1923, 1),
        Among(u"\u0161komu", 1923, 2),
        Among(u"elomu", -1, 83),
        Among(u"nomu", -1, 13),
        Among(u"cinomu", 1927, 137),
        Among(u"\u010Dinomu", 1927, 89),
        Among(u"osomu", -1, 123),
        Among(u"atomu", -1, 120),
        Among(u"evitomu", -1, 92),
        Among(u"ovitomu", -1, 93),
        Among(u"astomu", -1, 94),
        Among(u"avomu", -1, 77),
        Among(u"evomu", -1, 78),
        Among(u"ivomu", -1, 79),
        Among(u"ovomu", -1, 80),
        Among(u"a\u0107omu", -1, 14),
        Among(u"e\u0107omu", -1, 15),
        Among(u"u\u0107omu", -1, 16),
        Among(u"o\u0161omu", -1, 91),
        Among(u"nu", -1, 13),
        Among(u"anu", 1943, 10),
        Among(u"astanu", 1944, 110),
        Among(u"istanu", 1944, 111),
        Among(u"ostanu", 1944, 112),
        Among(u"inu", 1943, 11),
        Among(u"cinu", 1948, 137),
        Among(u"aninu", 1948, 10),
        Among(u"\u010Dinu", 1948, 89),
        Among(u"onu", 1943, 12),
        Among(u"aru", -1, 53),
        Among(u"dru", -1, 54),
        Among(u"eru", -1, 55),
        Among(u"oru", -1, 56),
        Among(u"basu", -1, 135),
        Among(u"gasu", -1, 131),
        Among(u"jasu", -1, 129),
        Among(u"kasu", -1, 133),
        Among(u"nasu", -1, 132),
        Among(u"tasu", -1, 130),
        Among(u"vasu", -1, 134),
        Among(u"esu", -1, 57),
        Among(u"isu", -1, 58),
        Among(u"osu", -1, 123),
        Among(u"atu", -1, 120),
        Among(u"ikatu", 1967, 68),
        Among(u"latu", 1967, 69),
        Among(u"etu", -1, 70),
        Among(u"evitu", -1, 92),
        Among(u"ovitu", -1, 93),
        Among(u"astu", -1, 94),
        Among(u"estu", -1, 71),
        Among(u"istu", -1, 72),
        Among(u"kstu", -1, 73),
        Among(u"ostu", -1, 74),
        Among(u"i\u0161tu", -1, 75),
        Among(u"avu", -1, 77),
        Among(u"evu", -1, 78),
        Among(u"ivu", -1, 79),
        Among(u"ovu", -1, 80),
        Among(u"lovu", 1982, 82),
        Among(u"movu", 1982, 81),
        Among(u"stvu", -1, 3),
        Among(u"\u0161tvu", -1, 4),
        Among(u"ba\u0161u", -1, 63),
        Among(u"ga\u0161u", -1, 64),
        Among(u"ja\u0161u", -1, 61),
        Among(u"ka\u0161u", -1, 62),
        Among(u"na\u0161u", -1, 60),
        Among(u"ta\u0161u", -1, 59),
        Among(u"va\u0161u", -1, 65),
        Among(u"e\u0161u", -1, 66),
        Among(u"i\u0161u", -1, 67),
        Among(u"o\u0161u", -1, 91),
        Among(u"avav", -1, 97),
        Among(u"evav", -1, 96),
        Among(u"ivav", -1, 98),
        Among(u"uvav", -1, 99),
        Among(u"kov", -1, 95),
        Among(u"a\u0161", -1, 104),
        Among(u"ira\u0161", 2002, 100),
        Among(u"ura\u0161", 2002, 105),
        Among(u"ta\u0161", 2002, 113),
        Among(u"ava\u0161", 2002, 97),
        Among(u"eva\u0161", 2002, 96),
        Among(u"iva\u0161", 2002, 98),
        Among(u"uva\u0161", 2002, 99),
        Among(u"a\u010Da\u0161", 2002, 102),
        Among(u"e\u0161", -1, 119),
        Among(u"astade\u0161", 2011, 110),
        Among(u"istade\u0161", 2011, 111),
        Among(u"ostade\u0161", 2011, 112),
        Among(u"astaje\u0161", 2011, 106),
        Among(u"istaje\u0161", 2011, 107),
        Among(u"ostaje\u0161", 2011, 108),
        Among(u"ije\u0161", 2011, 116),
        Among(u"inje\u0161", 2011, 114),
        Among(u"uje\u0161", 2011, 25),
        Among(u"iruje\u0161", 2020, 100),
        Among(u"lu\u010Duje\u0161", 2020, 117),
        Among(u"ne\u0161", 2011, 13),
        Among(u"astane\u0161", 2023, 110),
        Among(u"istane\u0161", 2023, 111),
        Among(u"ostane\u0161", 2023, 112),
        Among(u"ete\u0161", 2011, 70),
        Among(u"aste\u0161", 2011, 115),
        Among(u"i\u0161", -1, 116),
        Among(u"ni\u0161", 2029, 103),
        Among(u"jeti\u0161", 2029, 118),
        Among(u"a\u010Di\u0161", 2029, 101),
        Among(u"lu\u010Di\u0161", 2029, 117),
        Among(u"ro\u0161i\u0161", 2029, 90)
    ]

    a_3 = [
        Among(u"a", -1, 1),
        Among(u"oga", 0, 1),
        Among(u"ama", 0, 1),
        Among(u"ima", 0, 1),
        Among(u"ena", 0, 1),
        Among(u"e", -1, 1),
        Among(u"og", -1, 1),
        Among(u"anog", 6, 1),
        Among(u"enog", 6, 1),
        Among(u"anih", -1, 1),
        Among(u"enih", -1, 1),
        Among(u"i", -1, 1),
        Among(u"ani", 11, 1),
        Among(u"eni", 11, 1),
        Among(u"anoj", -1, 1),
        Among(u"enoj", -1, 1),
        Among(u"anim", -1, 1),
        Among(u"enim", -1, 1),
        Among(u"om", -1, 1),
        Among(u"enom", 18, 1),
        Among(u"o", -1, 1),
        Among(u"ano", 20, 1),
        Among(u"eno", 20, 1),
        Among(u"ost", -1, 1),
        Among(u"u", -1, 1),
        Among(u"enu", 24, 1)
    ]

    g_v = [17, 65, 16]

    g_sa = [65, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 128]

    g_ca = [119, 95, 23, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 0, 0, 0, 16]

    g_rg = [1]

    I_p1 = 0
    B_no_diacritics = False

    def __r_cyr_to_lat(self):
        v_1 = self.cursor
        try:
            while True:
                v_2 = self.cursor
                try:
                    try:
                        while True:
                            v_3 = self.cursor
                            try:
                                self.bra = self.cursor
                                among_var = self.find_among(SerbianStemmer.a_0)
                                if among_var == 0:
                                    raise lab3()
                                self.ket = self.cursor
                                if among_var == 1:
                                    if not self.slice_from(u"a"):
                                        return False
                                elif among_var == 2:
                                    if not self.slice_from(u"b"):
                                        return False
                                elif among_var == 3:
                                    if not self.slice_from(u"v"):
                                        return False
                                elif among_var == 4:
                                    if not self.slice_from(u"g"):
                                        return False
                                elif among_var == 5:
                                    if not self.slice_from(u"d"):
                                        return False
                                elif among_var == 6:
                                    if not self.slice_from(u"\u0111"):
                                        return False
                                elif among_var == 7:
                                    if not self.slice_from(u"e"):
                                        return False
                                elif among_var == 8:
                                    if not self.slice_from(u"\u017E"):
                                        return False
                                elif among_var == 9:
                                    if not self.slice_from(u"z"):
                                        return False
                                elif among_var == 10:
                                    if not self.slice_from(u"i"):
                                        return False
                                elif among_var == 11:
                                    if not self.slice_from(u"j"):
                                        return False
                                elif among_var == 12:
                                    if not self.slice_from(u"k"):
                                        return False
                                elif among_var == 13:
                                    if not self.slice_from(u"l"):
                                        return False
                                elif among_var == 14:
                                    if not self.slice_from(u"lj"):
                                        return False
                                elif among_var == 15:
                                    if not self.slice_from(u"m"):
                                        return False
                                elif among_var == 16:
                                    if not self.slice_from(u"n"):
                                        return False
                                elif among_var == 17:
                                    if not self.slice_from(u"nj"):
                                        return False
                                elif among_var == 18:
                                    if not self.slice_from(u"o"):
                                        return False
                                elif among_var == 19:
                                    if not self.slice_from(u"p"):
                                        return False
                                elif among_var == 20:
                                    if not self.slice_from(u"r"):
                                        return False
                                elif among_var == 21:
                                    if not self.slice_from(u"s"):
                                        return False
                                elif among_var == 22:
                                    if not self.slice_from(u"t"):
                                        return False
                                elif among_var == 23:
                                    if not self.slice_from(u"\u0107"):
                                        return False
                                elif among_var == 24:
                                    if not self.slice_from(u"u"):
                                        return False
                                elif among_var == 25:
                                    if not self.slice_from(u"f"):
                                        return False
                                elif among_var == 26:
                                    if not self.slice_from(u"h"):
                                        return False
                                elif among_var == 27:
                                    if not self.slice_from(u"c"):
                                        return False
                                elif among_var == 28:
                                    if not self.slice_from(u"\u010D"):
                                        return False
                                elif among_var == 29:
                                    if not self.slice_from(u"d\u017E"):
                                        return False
                                else:
                                    if not self.slice_from(u"\u0161"):
                                        return False
                                self.cursor = v_3
                                raise lab2()
                            except lab3: pass
                            self.cursor = v_3
                            if self.cursor >= self.limit:
                                raise lab1()
                            self.cursor += 1
                    except lab2: pass
                    continue
                except lab1: pass
                self.cursor = v_2
                break
        except lab0: pass
        self.cursor = v_1
        return True

    def __r_prelude(self):
        v_1 = self.cursor
        try:
            while True:
                v_2 = self.cursor
                try:
                    try:
                        while True:
                            v_3 = self.cursor
                            try:
                                if not self.in_grouping(SerbianStemmer.g_ca, 98, 382):
                                    raise lab3()
                                self.bra = self.cursor
                                if not self.eq_s(u"ije"):
                                    raise lab3()
                                self.ket = self.cursor
                                if not self.in_grouping(SerbianStemmer.g_ca, 98, 382):
                                    raise lab3()
                                if not self.slice_from(u"e"):
                                    return False
                                self.cursor = v_3
                                raise lab2()
                            except lab3: pass
                            self.cursor = v_3
                            if self.cursor >= self.limit:
                                raise lab1()
                            self.cursor += 1
                    except lab2: pass
                    continue
                except lab1: pass
                self.cursor = v_2
                break
        except lab0: pass
        self.cursor = v_1
        v_4 = self.cursor
        try:
            while True:
                v_5 = self.cursor
                try:
                    try:
                        while True:
                            v_6 = self.cursor
                            try:
                                if not self.in_grouping(SerbianStemmer.g_ca, 98, 382):
                                    raise lab7()
                                self.bra = self.cursor
                                if not self.eq_s(u"je"):
                                    raise lab7()
                                self.ket = self.cursor
                                if not self.in_grouping(SerbianStemmer.g_ca, 98, 382):
                                    raise lab7()
                                if not self.slice_from(u"e"):
                                    return False
                                self.cursor = v_6
                                raise lab6()
                            except lab7: pass
                            self.cursor = v_6
                            if self.cursor >= self.limit:
                                raise lab5()
                            self.cursor += 1
                    except lab6: pass
                    continue
                except lab5: pass
                self.cursor = v_5
                break
        except lab4: pass
        self.cursor = v_4
        v_7 = self.cursor
        try:
            while True:
                v_8 = self.cursor
                try:
                    try:
                        while True:
                            v_9 = self.cursor
                            try:
                                self.bra = self.cursor
                                if not self.eq_s(u"dj"):
                                    raise lab11()
                                self.ket = self.cursor
                                if not self.slice_from(u"\u0111"):
                                    return False
                                self.cursor = v_9
                                raise lab10()
                            except lab11: pass
                            self.cursor = v_9
                            if self.cursor >= self.limit:
                                raise lab9()
                            self.cursor += 1
                    except lab10: pass
                    continue
                except lab9: pass
                self.cursor = v_8
                break
        except lab8: pass
        self.cursor = v_7
        return True

    def __r_mark_regions(self):
        self.B_no_diacritics = True
        v_1 = self.cursor
        try:
            if not self.go_out_grouping(SerbianStemmer.g_sa, 263, 382):
                raise lab0()
            self.cursor += 1
            self.B_no_diacritics = False
        except lab0: pass
        self.cursor = v_1
        self.I_p1 = self.limit
        v_2 = self.cursor
        try:
            if not self.go_out_grouping(SerbianStemmer.g_v, 97, 117):
                raise lab1()
            self.cursor += 1
            self.I_p1 = self.cursor
            if not self.I_p1 < 2:
                raise lab1()
            if not self.go_in_grouping(SerbianStemmer.g_v, 97, 117):
                raise lab1()
            self.cursor += 1
            self.I_p1 = self.cursor
        except lab1: pass
        self.cursor = v_2
        v_3 = self.cursor
        try:
            try:
                while True:
                    try:
                        if not self.eq_s(u"r"):
                            raise lab4()
                        raise lab3()
                    except lab4: pass
                    if self.cursor >= self.limit:
                        raise lab2()
                    self.cursor += 1
            except lab3: pass
            try:
                v_5 = self.cursor
                try:
                    if not self.cursor >= 2:
                        raise lab6()
                    raise lab5()
                except lab6: pass
                self.cursor = v_5
                if not self.go_in_grouping(SerbianStemmer.g_rg, 114, 114):
                    raise lab2()
                self.cursor += 1
            except lab5: pass
            if not (self.I_p1 - self.cursor) > 1:
                raise lab2()
            self.I_p1 = self.cursor
        except lab2: pass
        self.cursor = v_3
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_Step_1(self):
        self.ket = self.cursor
        among_var = self.find_among_b(SerbianStemmer.a_1)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.slice_from(u"loga"):
                return False
        elif among_var == 2:
            if not self.slice_from(u"peh"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"vojka"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"bojka"):
                return False
        elif among_var == 5:
            if not self.slice_from(u"jak"):
                return False
        elif among_var == 6:
            if not self.slice_from(u"\u010Dajni"):
                return False
        elif among_var == 7:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"cajni"):
                return False
        elif among_var == 8:
            if not self.slice_from(u"erni"):
                return False
        elif among_var == 9:
            if not self.slice_from(u"larni"):
                return False
        elif among_var == 10:
            if not self.slice_from(u"esni"):
                return False
        elif among_var == 11:
            if not self.slice_from(u"anjca"):
                return False
        elif among_var == 12:
            if not self.slice_from(u"ajca"):
                return False
        elif among_var == 13:
            if not self.slice_from(u"ljca"):
                return False
        elif among_var == 14:
            if not self.slice_from(u"ejca"):
                return False
        elif among_var == 15:
            if not self.slice_from(u"ojca"):
                return False
        elif among_var == 16:
            if not self.slice_from(u"ajka"):
                return False
        elif among_var == 17:
            if not self.slice_from(u"ojka"):
                return False
        elif among_var == 18:
            if not self.slice_from(u"\u0161ca"):
                return False
        elif among_var == 19:
            if not self.slice_from(u"ing"):
                return False
        elif among_var == 20:
            if not self.slice_from(u"tvenik"):
                return False
        elif among_var == 21:
            if not self.slice_from(u"tetika"):
                return False
        elif among_var == 22:
            if not self.slice_from(u"nstva"):
                return False
        elif among_var == 23:
            if not self.slice_from(u"nik"):
                return False
        elif among_var == 24:
            if not self.slice_from(u"tik"):
                return False
        elif among_var == 25:
            if not self.slice_from(u"zik"):
                return False
        elif among_var == 26:
            if not self.slice_from(u"snik"):
                return False
        elif among_var == 27:
            if not self.slice_from(u"kusi"):
                return False
        elif among_var == 28:
            if not self.slice_from(u"kusni"):
                return False
        elif among_var == 29:
            if not self.slice_from(u"kustva"):
                return False
        elif among_var == 30:
            if not self.slice_from(u"du\u0161ni"):
                return False
        elif among_var == 31:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"dusni"):
                return False
        elif among_var == 32:
            if not self.slice_from(u"antni"):
                return False
        elif among_var == 33:
            if not self.slice_from(u"bilni"):
                return False
        elif among_var == 34:
            if not self.slice_from(u"tilni"):
                return False
        elif among_var == 35:
            if not self.slice_from(u"avilni"):
                return False
        elif among_var == 36:
            if not self.slice_from(u"silni"):
                return False
        elif among_var == 37:
            if not self.slice_from(u"gilni"):
                return False
        elif among_var == 38:
            if not self.slice_from(u"rilni"):
                return False
        elif among_var == 39:
            if not self.slice_from(u"nilni"):
                return False
        elif among_var == 40:
            if not self.slice_from(u"alni"):
                return False
        elif among_var == 41:
            if not self.slice_from(u"ozni"):
                return False
        elif among_var == 42:
            if not self.slice_from(u"ravi"):
                return False
        elif among_var == 43:
            if not self.slice_from(u"stavni"):
                return False
        elif among_var == 44:
            if not self.slice_from(u"pravni"):
                return False
        elif among_var == 45:
            if not self.slice_from(u"tivni"):
                return False
        elif among_var == 46:
            if not self.slice_from(u"sivni"):
                return False
        elif among_var == 47:
            if not self.slice_from(u"atni"):
                return False
        elif among_var == 48:
            if not self.slice_from(u"enta"):
                return False
        elif among_var == 49:
            if not self.slice_from(u"tetni"):
                return False
        elif among_var == 50:
            if not self.slice_from(u"pletni"):
                return False
        elif among_var == 51:
            if not self.slice_from(u"\u0161avi"):
                return False
        elif among_var == 52:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"savi"):
                return False
        elif among_var == 53:
            if not self.slice_from(u"anta"):
                return False
        elif among_var == 54:
            if not self.slice_from(u"a\u010Dka"):
                return False
        elif among_var == 55:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"acka"):
                return False
        elif among_var == 56:
            if not self.slice_from(u"u\u0161ka"):
                return False
        elif among_var == 57:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"uska"):
                return False
        elif among_var == 58:
            if not self.slice_from(u"atka"):
                return False
        elif among_var == 59:
            if not self.slice_from(u"etka"):
                return False
        elif among_var == 60:
            if not self.slice_from(u"itka"):
                return False
        elif among_var == 61:
            if not self.slice_from(u"otka"):
                return False
        elif among_var == 62:
            if not self.slice_from(u"utka"):
                return False
        elif among_var == 63:
            if not self.slice_from(u"eskna"):
                return False
        elif among_var == 64:
            if not self.slice_from(u"ti\u010Dni"):
                return False
        elif among_var == 65:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ticni"):
                return False
        elif among_var == 66:
            if not self.slice_from(u"ojska"):
                return False
        elif among_var == 67:
            if not self.slice_from(u"esma"):
                return False
        elif among_var == 68:
            if not self.slice_from(u"metra"):
                return False
        elif among_var == 69:
            if not self.slice_from(u"centra"):
                return False
        elif among_var == 70:
            if not self.slice_from(u"istra"):
                return False
        elif among_var == 71:
            if not self.slice_from(u"osti"):
                return False
        elif among_var == 72:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"osti"):
                return False
        elif among_var == 73:
            if not self.slice_from(u"dba"):
                return False
        elif among_var == 74:
            if not self.slice_from(u"\u010Dka"):
                return False
        elif among_var == 75:
            if not self.slice_from(u"mca"):
                return False
        elif among_var == 76:
            if not self.slice_from(u"nca"):
                return False
        elif among_var == 77:
            if not self.slice_from(u"voljni"):
                return False
        elif among_var == 78:
            if not self.slice_from(u"anki"):
                return False
        elif among_var == 79:
            if not self.slice_from(u"vca"):
                return False
        elif among_var == 80:
            if not self.slice_from(u"sca"):
                return False
        elif among_var == 81:
            if not self.slice_from(u"rca"):
                return False
        elif among_var == 82:
            if not self.slice_from(u"alca"):
                return False
        elif among_var == 83:
            if not self.slice_from(u"elca"):
                return False
        elif among_var == 84:
            if not self.slice_from(u"olca"):
                return False
        elif among_var == 85:
            if not self.slice_from(u"njca"):
                return False
        elif among_var == 86:
            if not self.slice_from(u"ekta"):
                return False
        elif among_var == 87:
            if not self.slice_from(u"izma"):
                return False
        elif among_var == 88:
            if not self.slice_from(u"jebi"):
                return False
        elif among_var == 89:
            if not self.slice_from(u"baci"):
                return False
        elif among_var == 90:
            if not self.slice_from(u"a\u0161ni"):
                return False
        else:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"asni"):
                return False
        return True

    def __r_Step_2(self):
        self.ket = self.cursor
        among_var = self.find_among_b(SerbianStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_from(u"sk"):
                return False
        elif among_var == 2:
            if not self.slice_from(u"\u0161k"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"stv"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"\u0161tv"):
                return False
        elif among_var == 5:
            if not self.slice_from(u"tanij"):
                return False
        elif among_var == 6:
            if not self.slice_from(u"manij"):
                return False
        elif among_var == 7:
            if not self.slice_from(u"panij"):
                return False
        elif among_var == 8:
            if not self.slice_from(u"ranij"):
                return False
        elif among_var == 9:
            if not self.slice_from(u"ganij"):
                return False
        elif among_var == 10:
            if not self.slice_from(u"an"):
                return False
        elif among_var == 11:
            if not self.slice_from(u"in"):
                return False
        elif among_var == 12:
            if not self.slice_from(u"on"):
                return False
        elif among_var == 13:
            if not self.slice_from(u"n"):
                return False
        elif among_var == 14:
            if not self.slice_from(u"a\u0107"):
                return False
        elif among_var == 15:
            if not self.slice_from(u"e\u0107"):
                return False
        elif among_var == 16:
            if not self.slice_from(u"u\u0107"):
                return False
        elif among_var == 17:
            if not self.slice_from(u"ugov"):
                return False
        elif among_var == 18:
            if not self.slice_from(u"ug"):
                return False
        elif among_var == 19:
            if not self.slice_from(u"log"):
                return False
        elif among_var == 20:
            if not self.slice_from(u"g"):
                return False
        elif among_var == 21:
            if not self.slice_from(u"rari"):
                return False
        elif among_var == 22:
            if not self.slice_from(u"oti"):
                return False
        elif among_var == 23:
            if not self.slice_from(u"si"):
                return False
        elif among_var == 24:
            if not self.slice_from(u"li"):
                return False
        elif among_var == 25:
            if not self.slice_from(u"uj"):
                return False
        elif among_var == 26:
            if not self.slice_from(u"caj"):
                return False
        elif among_var == 27:
            if not self.slice_from(u"\u010Daj"):
                return False
        elif among_var == 28:
            if not self.slice_from(u"\u0107aj"):
                return False
        elif among_var == 29:
            if not self.slice_from(u"\u0111aj"):
                return False
        elif among_var == 30:
            if not self.slice_from(u"laj"):
                return False
        elif among_var == 31:
            if not self.slice_from(u"raj"):
                return False
        elif among_var == 32:
            if not self.slice_from(u"bij"):
                return False
        elif among_var == 33:
            if not self.slice_from(u"cij"):
                return False
        elif among_var == 34:
            if not self.slice_from(u"dij"):
                return False
        elif among_var == 35:
            if not self.slice_from(u"lij"):
                return False
        elif among_var == 36:
            if not self.slice_from(u"nij"):
                return False
        elif among_var == 37:
            if not self.slice_from(u"mij"):
                return False
        elif among_var == 38:
            if not self.slice_from(u"\u017Eij"):
                return False
        elif among_var == 39:
            if not self.slice_from(u"gij"):
                return False
        elif among_var == 40:
            if not self.slice_from(u"fij"):
                return False
        elif among_var == 41:
            if not self.slice_from(u"pij"):
                return False
        elif among_var == 42:
            if not self.slice_from(u"rij"):
                return False
        elif among_var == 43:
            if not self.slice_from(u"sij"):
                return False
        elif among_var == 44:
            if not self.slice_from(u"tij"):
                return False
        elif among_var == 45:
            if not self.slice_from(u"zij"):
                return False
        elif among_var == 46:
            if not self.slice_from(u"nal"):
                return False
        elif among_var == 47:
            if not self.slice_from(u"ijal"):
                return False
        elif among_var == 48:
            if not self.slice_from(u"ozil"):
                return False
        elif among_var == 49:
            if not self.slice_from(u"olov"):
                return False
        elif among_var == 50:
            if not self.slice_from(u"ol"):
                return False
        elif among_var == 51:
            if not self.slice_from(u"lem"):
                return False
        elif among_var == 52:
            if not self.slice_from(u"ram"):
                return False
        elif among_var == 53:
            if not self.slice_from(u"ar"):
                return False
        elif among_var == 54:
            if not self.slice_from(u"dr"):
                return False
        elif among_var == 55:
            if not self.slice_from(u"er"):
                return False
        elif among_var == 56:
            if not self.slice_from(u"or"):
                return False
        elif among_var == 57:
            if not self.slice_from(u"es"):
                return False
        elif among_var == 58:
            if not self.slice_from(u"is"):
                return False
        elif among_var == 59:
            if not self.slice_from(u"ta\u0161"):
                return False
        elif among_var == 60:
            if not self.slice_from(u"na\u0161"):
                return False
        elif among_var == 61:
            if not self.slice_from(u"ja\u0161"):
                return False
        elif among_var == 62:
            if not self.slice_from(u"ka\u0161"):
                return False
        elif among_var == 63:
            if not self.slice_from(u"ba\u0161"):
                return False
        elif among_var == 64:
            if not self.slice_from(u"ga\u0161"):
                return False
        elif among_var == 65:
            if not self.slice_from(u"va\u0161"):
                return False
        elif among_var == 66:
            if not self.slice_from(u"e\u0161"):
                return False
        elif among_var == 67:
            if not self.slice_from(u"i\u0161"):
                return False
        elif among_var == 68:
            if not self.slice_from(u"ikat"):
                return False
        elif among_var == 69:
            if not self.slice_from(u"lat"):
                return False
        elif among_var == 70:
            if not self.slice_from(u"et"):
                return False
        elif among_var == 71:
            if not self.slice_from(u"est"):
                return False
        elif among_var == 72:
            if not self.slice_from(u"ist"):
                return False
        elif among_var == 73:
            if not self.slice_from(u"kst"):
                return False
        elif among_var == 74:
            if not self.slice_from(u"ost"):
                return False
        elif among_var == 75:
            if not self.slice_from(u"i\u0161t"):
                return False
        elif among_var == 76:
            if not self.slice_from(u"ova"):
                return False
        elif among_var == 77:
            if not self.slice_from(u"av"):
                return False
        elif among_var == 78:
            if not self.slice_from(u"ev"):
                return False
        elif among_var == 79:
            if not self.slice_from(u"iv"):
                return False
        elif among_var == 80:
            if not self.slice_from(u"ov"):
                return False
        elif among_var == 81:
            if not self.slice_from(u"mov"):
                return False
        elif among_var == 82:
            if not self.slice_from(u"lov"):
                return False
        elif among_var == 83:
            if not self.slice_from(u"el"):
                return False
        elif among_var == 84:
            if not self.slice_from(u"anj"):
                return False
        elif among_var == 85:
            if not self.slice_from(u"enj"):
                return False
        elif among_var == 86:
            if not self.slice_from(u"\u0161nj"):
                return False
        elif among_var == 87:
            if not self.slice_from(u"en"):
                return False
        elif among_var == 88:
            if not self.slice_from(u"\u0161n"):
                return False
        elif among_var == 89:
            if not self.slice_from(u"\u010Din"):
                return False
        elif among_var == 90:
            if not self.slice_from(u"ro\u0161i"):
                return False
        elif among_var == 91:
            if not self.slice_from(u"o\u0161"):
                return False
        elif among_var == 92:
            if not self.slice_from(u"evit"):
                return False
        elif among_var == 93:
            if not self.slice_from(u"ovit"):
                return False
        elif among_var == 94:
            if not self.slice_from(u"ast"):
                return False
        elif among_var == 95:
            if not self.slice_from(u"k"):
                return False
        elif among_var == 96:
            if not self.slice_from(u"eva"):
                return False
        elif among_var == 97:
            if not self.slice_from(u"ava"):
                return False
        elif among_var == 98:
            if not self.slice_from(u"iva"):
                return False
        elif among_var == 99:
            if not self.slice_from(u"uva"):
                return False
        elif among_var == 100:
            if not self.slice_from(u"ir"):
                return False
        elif among_var == 101:
            if not self.slice_from(u"a\u010D"):
                return False
        elif among_var == 102:
            if not self.slice_from(u"a\u010Da"):
                return False
        elif among_var == 103:
            if not self.slice_from(u"ni"):
                return False
        elif among_var == 104:
            if not self.slice_from(u"a"):
                return False
        elif among_var == 105:
            if not self.slice_from(u"ur"):
                return False
        elif among_var == 106:
            if not self.slice_from(u"astaj"):
                return False
        elif among_var == 107:
            if not self.slice_from(u"istaj"):
                return False
        elif among_var == 108:
            if not self.slice_from(u"ostaj"):
                return False
        elif among_var == 109:
            if not self.slice_from(u"aj"):
                return False
        elif among_var == 110:
            if not self.slice_from(u"asta"):
                return False
        elif among_var == 111:
            if not self.slice_from(u"ista"):
                return False
        elif among_var == 112:
            if not self.slice_from(u"osta"):
                return False
        elif among_var == 113:
            if not self.slice_from(u"ta"):
                return False
        elif among_var == 114:
            if not self.slice_from(u"inj"):
                return False
        elif among_var == 115:
            if not self.slice_from(u"as"):
                return False
        elif among_var == 116:
            if not self.slice_from(u"i"):
                return False
        elif among_var == 117:
            if not self.slice_from(u"lu\u010D"):
                return False
        elif among_var == 118:
            if not self.slice_from(u"jeti"):
                return False
        elif among_var == 119:
            if not self.slice_from(u"e"):
                return False
        elif among_var == 120:
            if not self.slice_from(u"at"):
                return False
        elif among_var == 121:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"luc"):
                return False
        elif among_var == 122:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"snj"):
                return False
        elif among_var == 123:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"os"):
                return False
        elif among_var == 124:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ac"):
                return False
        elif among_var == 125:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ec"):
                return False
        elif among_var == 126:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"uc"):
                return False
        elif among_var == 127:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"rosi"):
                return False
        elif among_var == 128:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"aca"):
                return False
        elif among_var == 129:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"jas"):
                return False
        elif among_var == 130:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"tas"):
                return False
        elif among_var == 131:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"gas"):
                return False
        elif among_var == 132:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"nas"):
                return False
        elif among_var == 133:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"kas"):
                return False
        elif among_var == 134:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"vas"):
                return False
        elif among_var == 135:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"bas"):
                return False
        elif among_var == 136:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"as"):
                return False
        elif among_var == 137:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"cin"):
                return False
        elif among_var == 138:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"astaj"):
                return False
        elif among_var == 139:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"istaj"):
                return False
        elif among_var == 140:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ostaj"):
                return False
        elif among_var == 141:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"asta"):
                return False
        elif among_var == 142:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ista"):
                return False
        elif among_var == 143:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"osta"):
                return False
        elif among_var == 144:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ava"):
                return False
        elif among_var == 145:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"eva"):
                return False
        elif among_var == 146:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"iva"):
                return False
        elif among_var == 147:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"uva"):
                return False
        elif among_var == 148:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ova"):
                return False
        elif among_var == 149:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"jeti"):
                return False
        elif among_var == 150:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"inj"):
                return False
        elif among_var == 151:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ist"):
                return False
        elif among_var == 152:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"es"):
                return False
        elif among_var == 153:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"et"):
                return False
        elif among_var == 154:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"is"):
                return False
        elif among_var == 155:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ir"):
                return False
        elif among_var == 156:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ur"):
                return False
        elif among_var == 157:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"uj"):
                return False
        elif among_var == 158:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ni"):
                return False
        elif among_var == 159:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"sn"):
                return False
        elif among_var == 160:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"ta"):
                return False
        elif among_var == 161:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"a"):
                return False
        elif among_var == 162:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"i"):
                return False
        elif among_var == 163:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"e"):
                return False
        else:
            if not self.B_no_diacritics:
                return False
            if not self.slice_from(u"n"):
                return False
        return True

    def __r_Step_3(self):
        self.ket = self.cursor
        if self.find_among_b(SerbianStemmer.a_3) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if not self.slice_from(u""):
            return False
        return True

    def _stem(self):
        self.__r_cyr_to_lat()
        self.__r_prelude()
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_4 = self.limit - self.cursor
        self.__r_Step_1()
        self.cursor = self.limit - v_4
        v_5 = self.limit - self.cursor
        try:
            try:
                v_6 = self.limit - self.cursor
                try:
                    if not self.__r_Step_2():
                        raise lab2()
                    raise lab1()
                except lab2: pass
                self.cursor = self.limit - v_6
                if not self.__r_Step_3():
                    raise lab0()
            except lab1: pass
        except lab0: pass
        self.cursor = self.limit - v_5
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass


class lab8(BaseException): pass


class lab9(BaseException): pass


class lab10(BaseException): pass


class lab11(BaseException): pass
