# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class YiddishStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"\u05D5\u05D5", -1, 1),
        Among(u"\u05D5\u05D9", -1, 2),
        Among(u"\u05D9\u05D9", -1, 3),
        Among(u"\u05DA", -1, 4),
        Among(u"\u05DD", -1, 5),
        Among(u"\u05DF", -1, 6),
        Among(u"\u05E3", -1, 7),
        Among(u"\u05E5", -1, 8)
    ]

    a_1 = [
        Among(u"\u05D0\u05D3\u05D5\u05E8\u05DB", -1, 1),
        Among(u"\u05D0\u05D4\u05D9\u05E0", -1, 1),
        Among(u"\u05D0\u05D4\u05E2\u05E8", -1, 1),
        Among(u"\u05D0\u05D4\u05F2\u05DE", -1, 1),
        Among(u"\u05D0\u05D5\u05DE", -1, 1),
        Among(u"\u05D0\u05D5\u05E0\u05D8\u05E2\u05E8", -1, 1),
        Among(u"\u05D0\u05D9\u05D1\u05E2\u05E8", -1, 1),
        Among(u"\u05D0\u05E0", -1, 1),
        Among(u"\u05D0\u05E0\u05D8", 7, 1),
        Among(u"\u05D0\u05E0\u05D8\u05E7\u05E2\u05D2\u05E0", 8, 1),
        Among(u"\u05D0\u05E0\u05D9\u05D3\u05E2\u05E8", 7, 1),
        Among(u"\u05D0\u05E4", -1, 1),
        Among(u"\u05D0\u05E4\u05D9\u05E8", 11, 1),
        Among(u"\u05D0\u05E7\u05E2\u05D2\u05E0", -1, 1),
        Among(u"\u05D0\u05E8\u05D0\u05E4", -1, 1),
        Among(u"\u05D0\u05E8\u05D5\u05DE", -1, 1),
        Among(u"\u05D0\u05E8\u05D5\u05E0\u05D8\u05E2\u05E8", -1, 1),
        Among(u"\u05D0\u05E8\u05D9\u05D1\u05E2\u05E8", -1, 1),
        Among(u"\u05D0\u05E8\u05F1\u05E1", -1, 1),
        Among(u"\u05D0\u05E8\u05F1\u05E4", -1, 1),
        Among(u"\u05D0\u05E8\u05F2\u05E0", -1, 1),
        Among(u"\u05D0\u05F0\u05E2\u05E7", -1, 1),
        Among(u"\u05D0\u05F1\u05E1", -1, 1),
        Among(u"\u05D0\u05F1\u05E4", -1, 1),
        Among(u"\u05D0\u05F2\u05E0", -1, 1),
        Among(u"\u05D1\u05D0", -1, 1),
        Among(u"\u05D1\u05F2", -1, 1),
        Among(u"\u05D3\u05D5\u05E8\u05DB", -1, 1),
        Among(u"\u05D3\u05E2\u05E8", -1, 1),
        Among(u"\u05DE\u05D9\u05D8", -1, 1),
        Among(u"\u05E0\u05D0\u05DB", -1, 1),
        Among(u"\u05E4\u05D0\u05E8", -1, 1),
        Among(u"\u05E4\u05D0\u05E8\u05D1\u05F2", 31, 1),
        Among(u"\u05E4\u05D0\u05E8\u05F1\u05E1", 31, 1),
        Among(u"\u05E4\u05D5\u05E0\u05D0\u05E0\u05D3\u05E2\u05E8", -1, 1),
        Among(u"\u05E6\u05D5", -1, 1),
        Among(u"\u05E6\u05D5\u05D6\u05D0\u05DE\u05E2\u05E0", 35, 1),
        Among(u"\u05E6\u05D5\u05E0\u05F1\u05E4", 35, 1),
        Among(u"\u05E6\u05D5\u05E8\u05D9\u05E7", 35, 1),
        Among(u"\u05E6\u05E2", -1, 1)
    ]

    a_2 = [
        Among(u"\u05D3\u05D6\u05E9", -1, -1),
        Among(u"\u05E9\u05D8\u05E8", -1, -1),
        Among(u"\u05E9\u05D8\u05E9", -1, -1),
        Among(u"\u05E9\u05E4\u05E8", -1, -1)
    ]

    a_3 = [
        Among(u"\u05E7\u05DC\u05D9\u05D1", -1, 9),
        Among(u"\u05E8\u05D9\u05D1", -1, 10),
        Among(u"\u05D8\u05E8\u05D9\u05D1", 1, 7),
        Among(u"\u05E9\u05E8\u05D9\u05D1", 1, 15),
        Among(u"\u05D4\u05F1\u05D1", -1, 23),
        Among(u"\u05E9\u05F0\u05D9\u05D2", -1, 12),
        Among(u"\u05D2\u05D0\u05E0\u05D2", -1, 1),
        Among(u"\u05D6\u05D5\u05E0\u05D2", -1, 18),
        Among(u"\u05E9\u05DC\u05D5\u05E0\u05D2", -1, 21),
        Among(u"\u05E6\u05F0\u05D5\u05E0\u05D2", -1, 20),
        Among(u"\u05D1\u05F1\u05D2", -1, 22),
        Among(u"\u05D1\u05D5\u05E0\u05D3", -1, 16),
        Among(u"\u05F0\u05D9\u05D6", -1, 6),
        Among(u"\u05D1\u05D9\u05D8", -1, 4),
        Among(u"\u05DC\u05D9\u05D8", -1, 8),
        Among(u"\u05DE\u05D9\u05D8", -1, 3),
        Among(u"\u05E9\u05E0\u05D9\u05D8", -1, 14),
        Among(u"\u05E0\u05D5\u05DE", -1, 2),
        Among(u"\u05E9\u05D8\u05D0\u05E0", -1, 25),
        Among(u"\u05D1\u05D9\u05E1", -1, 5),
        Among(u"\u05E9\u05DE\u05D9\u05E1", -1, 13),
        Among(u"\u05E8\u05D9\u05E1", -1, 11),
        Among(u"\u05D8\u05E8\u05D5\u05E0\u05E7", -1, 19),
        Among(u"\u05E4\u05D0\u05E8\u05DC\u05F1\u05E8", -1, 24),
        Among(u"\u05E9\u05F0\u05F1\u05E8", -1, 26),
        Among(u"\u05F0\u05D5\u05D8\u05E9", -1, 17)
    ]

    a_4 = [
        Among(u"\u05D5\u05E0\u05D2", -1, 1),
        Among(u"\u05E1\u05D8\u05D5", -1, 1),
        Among(u"\u05D8", -1, 1),
        Among(u"\u05D1\u05E8\u05D0\u05DB\u05D8", 2, 31),
        Among(u"\u05E1\u05D8", 2, 1),
        Among(u"\u05D9\u05E1\u05D8", 4, 33),
        Among(u"\u05E2\u05D8", 2, 1),
        Among(u"\u05E9\u05D0\u05E4\u05D8", 2, 1),
        Among(u"\u05D4\u05F2\u05D8", 2, 1),
        Among(u"\u05E7\u05F2\u05D8", 2, 1),
        Among(u"\u05D9\u05E7\u05F2\u05D8", 9, 1),
        Among(u"\u05DC\u05E2\u05DB", -1, 1),
        Among(u"\u05E2\u05DC\u05E2\u05DB", 11, 1),
        Among(u"\u05D9\u05D6\u05DE", -1, 1),
        Among(u"\u05D9\u05DE", -1, 1),
        Among(u"\u05E2\u05DE", -1, 1),
        Among(u"\u05E2\u05E0\u05E2\u05DE", 15, 3),
        Among(u"\u05D8\u05E2\u05E0\u05E2\u05DE", 16, 4),
        Among(u"\u05E0", -1, 1),
        Among(u"\u05E7\u05DC\u05D9\u05D1\u05E0", 18, 14),
        Among(u"\u05E8\u05D9\u05D1\u05E0", 18, 15),
        Among(u"\u05D8\u05E8\u05D9\u05D1\u05E0", 20, 12),
        Among(u"\u05E9\u05E8\u05D9\u05D1\u05E0", 20, 7),
        Among(u"\u05D4\u05F1\u05D1\u05E0", 18, 27),
        Among(u"\u05E9\u05F0\u05D9\u05D2\u05E0", 18, 17),
        Among(u"\u05D6\u05D5\u05E0\u05D2\u05E0", 18, 22),
        Among(u"\u05E9\u05DC\u05D5\u05E0\u05D2\u05E0", 18, 25),
        Among(u"\u05E6\u05F0\u05D5\u05E0\u05D2\u05E0", 18, 24),
        Among(u"\u05D1\u05F1\u05D2\u05E0", 18, 26),
        Among(u"\u05D1\u05D5\u05E0\u05D3\u05E0", 18, 20),
        Among(u"\u05F0\u05D9\u05D6\u05E0", 18, 11),
        Among(u"\u05D8\u05E0", 18, 4),
        Among(u"GE\u05D1\u05D9\u05D8\u05E0", 31, 9),
        Among(u"GE\u05DC\u05D9\u05D8\u05E0", 31, 13),
        Among(u"GE\u05DE\u05D9\u05D8\u05E0", 31, 8),
        Among(u"\u05E9\u05E0\u05D9\u05D8\u05E0", 31, 19),
        Among(u"\u05E1\u05D8\u05E0", 31, 1),
        Among(u"\u05D9\u05E1\u05D8\u05E0", 36, 1),
        Among(u"\u05E2\u05D8\u05E0", 31, 1),
        Among(u"GE\u05D1\u05D9\u05E1\u05E0", 18, 10),
        Among(u"\u05E9\u05DE\u05D9\u05E1\u05E0", 18, 18),
        Among(u"GE\u05E8\u05D9\u05E1\u05E0", 18, 16),
        Among(u"\u05E2\u05E0", 18, 1),
        Among(u"\u05D2\u05D0\u05E0\u05D2\u05E2\u05E0", 42, 5),
        Among(u"\u05E2\u05DC\u05E2\u05E0", 42, 1),
        Among(u"\u05E0\u05D5\u05DE\u05E2\u05E0", 42, 6),
        Among(u"\u05D9\u05D6\u05DE\u05E2\u05E0", 42, 1),
        Among(u"\u05E9\u05D8\u05D0\u05E0\u05E2\u05E0", 42, 29),
        Among(u"\u05D8\u05E8\u05D5\u05E0\u05E7\u05E0", 18, 23),
        Among(u"\u05E4\u05D0\u05E8\u05DC\u05F1\u05E8\u05E0", 18, 28),
        Among(u"\u05E9\u05F0\u05F1\u05E8\u05E0", 18, 30),
        Among(u"\u05F0\u05D5\u05D8\u05E9\u05E0", 18, 21),
        Among(u"\u05D2\u05F2\u05E0", 18, 5),
        Among(u"\u05E1", -1, 1),
        Among(u"\u05D8\u05E1", 53, 4),
        Among(u"\u05E2\u05D8\u05E1", 54, 1),
        Among(u"\u05E0\u05E1", 53, 1),
        Among(u"\u05D8\u05E0\u05E1", 56, 4),
        Among(u"\u05E2\u05E0\u05E1", 56, 3),
        Among(u"\u05E2\u05E1", 53, 1),
        Among(u"\u05D9\u05E2\u05E1", 59, 2),
        Among(u"\u05E2\u05DC\u05E2\u05E1", 59, 1),
        Among(u"\u05E2\u05E8\u05E1", 53, 1),
        Among(u"\u05E2\u05E0\u05E2\u05E8\u05E1", 62, 1),
        Among(u"\u05E2", -1, 1),
        Among(u"\u05D8\u05E2", 64, 4),
        Among(u"\u05E1\u05D8\u05E2", 65, 1),
        Among(u"\u05E2\u05D8\u05E2", 65, 1),
        Among(u"\u05D9\u05E2", 64, -1),
        Among(u"\u05E2\u05DC\u05E2", 64, 1),
        Among(u"\u05E2\u05E0\u05E2", 64, 3),
        Among(u"\u05D8\u05E2\u05E0\u05E2", 70, 4),
        Among(u"\u05E2\u05E8", -1, 1),
        Among(u"\u05D8\u05E2\u05E8", 72, 4),
        Among(u"\u05E1\u05D8\u05E2\u05E8", 73, 1),
        Among(u"\u05E2\u05D8\u05E2\u05E8", 73, 1),
        Among(u"\u05E2\u05E0\u05E2\u05E8", 72, 3),
        Among(u"\u05D8\u05E2\u05E0\u05E2\u05E8", 76, 4),
        Among(u"\u05D5\u05EA", -1, 32)
    ]

    a_5 = [
        Among(u"\u05D5\u05E0\u05D2", -1, 1),
        Among(u"\u05E9\u05D0\u05E4\u05D8", -1, 1),
        Among(u"\u05D4\u05F2\u05D8", -1, 1),
        Among(u"\u05E7\u05F2\u05D8", -1, 1),
        Among(u"\u05D9\u05E7\u05F2\u05D8", 3, 1),
        Among(u"\u05DC", -1, 2)
    ]

    a_6 = [
        Among(u"\u05D9\u05D2", -1, 1),
        Among(u"\u05D9\u05E7", -1, 1),
        Among(u"\u05D3\u05D9\u05E7", 1, 1),
        Among(u"\u05E0\u05D3\u05D9\u05E7", 2, 1),
        Among(u"\u05E2\u05E0\u05D3\u05D9\u05E7", 3, 1),
        Among(u"\u05D1\u05DC\u05D9\u05E7", 1, -1),
        Among(u"\u05D2\u05DC\u05D9\u05E7", 1, -1),
        Among(u"\u05E0\u05D9\u05E7", 1, 1),
        Among(u"\u05D9\u05E9", -1, 1)
    ]

    g_niked = [255, 155, 6]

    g_vowel = [33, 2, 4, 0, 6]

    g_consonant = [239, 254, 253, 131]

    I_x = 0
    I_p1 = 0

    def __r_prelude(self):
        v_1 = self.cursor
        try:
            while True:
                v_2 = self.cursor
                try:
                    try:
                        while True:
                            v_3 = self.cursor
                            try:
                                self.bra = self.cursor
                                among_var = self.find_among(YiddishStemmer.a_0)
                                if among_var == 0:
                                    raise lab3()
                                self.ket = self.cursor
                                if among_var == 1:
                                    v_4 = self.cursor
                                    try:
                                        if not self.eq_s(u"\u05BC"):
                                            raise lab4()
                                        raise lab3()
                                    except lab4: pass
                                    self.cursor = v_4
                                    if not self.slice_from(u"\u05F0"):
                                        return False
                                elif among_var == 2:
                                    v_5 = self.cursor
                                    try:
                                        if not self.eq_s(u"\u05B4"):
                                            raise lab5()
                                        raise lab3()
                                    except lab5: pass
                                    self.cursor = v_5
                                    if not self.slice_from(u"\u05F1"):
                                        return False
                                elif among_var == 3:
                                    v_6 = self.cursor
                                    try:
                                        if not self.eq_s(u"\u05B4"):
                                            raise lab6()
                                        raise lab3()
                                    except lab6: pass
                                    self.cursor = v_6
                                    if not self.slice_from(u"\u05F2"):
                                        return False
                                elif among_var == 4:
                                    if not self.slice_from(u"\u05DB"):
                                        return False
                                elif among_var == 5:
                                    if not self.slice_from(u"\u05DE"):
                                        return False
                                elif among_var == 6:
                                    if not self.slice_from(u"\u05E0"):
                                        return False
                                elif among_var == 7:
                                    if not self.slice_from(u"\u05E4"):
                                        return False
                                else:
                                    if not self.slice_from(u"\u05E6"):
                                        return False
                                self.cursor = v_3
                                raise lab2()
                            except lab3: pass
                            self.cursor = v_3
                            if self.cursor >= self.limit:
                                raise lab1()
                            self.cursor += 1
                    except lab2: pass
                    continue
                except lab1: pass
                self.cursor = v_2
                break
        except lab0: pass
        self.cursor = v_1
        v_7 = self.cursor
        try:
            while True:
                v_8 = self.cursor
                try:
                    try:
                        while True:
                            v_9 = self.cursor
                            try:
                                self.bra = self.cursor
                                if not self.in_grouping(YiddishStemmer.g_niked, 1456, 1474):
                                    raise lab10()
                                self.ket = self.cursor
                                if not self.slice_del():
                                    return False

                                self.cursor = v_9
                                raise lab9()
                            except lab10: pass
                            self.cursor = v_9
                            if self.cursor >= self.limit:
                                raise lab8()
                            self.cursor += 1
                    except lab9: pass
                    continue
                except lab8: pass
                self.cursor = v_8
                break
        except lab7: pass
        self.cursor = v_7
        return True

    def __r_mark_regions(self):
        self.I_p1 = self.limit
        v_1 = self.cursor
        try:
            self.bra = self.cursor
            if not self.eq_s(u"\u05D2\u05E2"):
                self.cursor = v_1
                raise lab0()
            self.ket = self.cursor
            v_2 = self.cursor
            try:
                try:
                    v_3 = self.cursor
                    try:
                        if not self.eq_s(u"\u05DC\u05D8"):
                            raise lab3()
                        raise lab2()
                    except lab3: pass
                    self.cursor = v_3
                    if not self.eq_s(u"\u05D1\u05E0"):
                        raise lab1()
                except lab2: pass
                self.cursor = v_1
                raise lab0()
            except lab1: pass
            self.cursor = v_2
            if not self.slice_from(u"GE"):
                return False
        except lab0: pass
        v_4 = self.cursor
        try:
            if self.find_among(YiddishStemmer.a_1) == 0:
                self.cursor = v_4
                raise lab4()
            try:
                v_5 = self.cursor
                try:
                    v_6 = self.cursor
                    try:
                        v_7 = self.cursor
                        try:
                            if not self.eq_s(u"\u05E6\u05D5\u05D2\u05E0"):
                                raise lab8()
                            raise lab7()
                        except lab8: pass
                        self.cursor = v_7
                        try:
                            if not self.eq_s(u"\u05E6\u05D5\u05E7\u05D8"):
                                raise lab9()
                            raise lab7()
                        except lab9: pass
                        self.cursor = v_7
                        if not self.eq_s(u"\u05E6\u05D5\u05E7\u05E0"):
                            raise lab6()
                    except lab7: pass
                    if self.cursor < self.limit:
                        raise lab6()
                    self.cursor = v_6
                    raise lab5()
                except lab6: pass
                self.cursor = v_5
                try:
                    v_8 = self.cursor
                    if not self.eq_s(u"\u05D2\u05E2\u05D1\u05E0"):
                        raise lab10()
                    self.cursor = v_8
                    raise lab5()
                except lab10: pass
                self.cursor = v_5
                try:
                    self.bra = self.cursor
                    if not self.eq_s(u"\u05D2\u05E2"):
                        raise lab11()
                    self.ket = self.cursor
                    if not self.slice_from(u"GE"):
                        return False
                    raise lab5()
                except lab11: pass
                self.cursor = v_5
                self.bra = self.cursor
                if not self.eq_s(u"\u05E6\u05D5"):
                    self.cursor = v_4
                    raise lab4()
                self.ket = self.cursor
                if not self.slice_from(u"TSU"):
                    return False
            except lab5: pass
        except lab4: pass
        v_9 = self.cursor
        c = self.cursor + 3
        if c > self.limit:
            return False
        self.cursor = c
        self.I_x = self.cursor
        self.cursor = v_9
        v_10 = self.cursor
        try:
            if self.find_among(YiddishStemmer.a_2) == 0:
                self.cursor = v_10
                raise lab12()
        except lab12: pass
        v_11 = self.cursor
        try:
            if not self.in_grouping(YiddishStemmer.g_consonant, 1489, 1520):
                raise lab13()
            if not self.in_grouping(YiddishStemmer.g_consonant, 1489, 1520):
                raise lab13()
            if not self.in_grouping(YiddishStemmer.g_consonant, 1489, 1520):
                raise lab13()
            self.I_p1 = self.cursor
            return False
        except lab13: pass
        self.cursor = v_11
        if not self.go_out_grouping(YiddishStemmer.g_vowel, 1488, 1522):
            return False
        while True:
            try:
                if not self.in_grouping(YiddishStemmer.g_vowel, 1488, 1522):
                    raise lab14()
                continue
            except lab14: pass
            break
        self.I_p1 = self.cursor
        try:
            if not self.I_p1 < self.I_x:
                raise lab15()
            self.I_p1 = self.I_x
        except lab15: pass
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_R1plus3(self):
        if not self.I_p1 <= (self.cursor + 3):
            return False
        return True

    def __r_standard_suffix(self):
        v_1 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            among_var = self.find_among_b(YiddishStemmer.a_4)
            if among_var == 0:
                raise lab0()
            self.bra = self.cursor
            if among_var == 1:
                if not self.__r_R1():
                    raise lab0()
                if not self.slice_del():
                    return False

            elif among_var == 2:
                if not self.__r_R1():
                    raise lab0()
                if not self.slice_from(u"\u05D9\u05E2"):
                    return False
            elif among_var == 3:
                if not self.__r_R1():
                    raise lab0()
                if not self.slice_del():
                    return False

                self.ket = self.cursor
                among_var = self.find_among_b(YiddishStemmer.a_3)
                if among_var == 0:
                    raise lab0()
                self.bra = self.cursor
                if among_var == 1:
                    if not self.slice_from(u"\u05D2\u05F2"):
                        return False
                elif among_var == 2:
                    if not self.slice_from(u"\u05E0\u05E2\u05DE"):
                        return False
                elif among_var == 3:
                    if not self.slice_from(u"\u05DE\u05F2\u05D3"):
                        return False
                elif among_var == 4:
                    if not self.slice_from(u"\u05D1\u05F2\u05D8"):
                        return False
                elif among_var == 5:
                    if not self.slice_from(u"\u05D1\u05F2\u05E1"):
                        return False
                elif among_var == 6:
                    if not self.slice_from(u"\u05F0\u05F2\u05D6"):
                        return False
                elif among_var == 7:
                    if not self.slice_from(u"\u05D8\u05E8\u05F2\u05D1"):
                        return False
                elif among_var == 8:
                    if not self.slice_from(u"\u05DC\u05F2\u05D8"):
                        return False
                elif among_var == 9:
                    if not self.slice_from(u"\u05E7\u05DC\u05F2\u05D1"):
                        return False
                elif among_var == 10:
                    if not self.slice_from(u"\u05E8\u05F2\u05D1"):
                        return False
                elif among_var == 11:
                    if not self.slice_from(u"\u05E8\u05F2\u05E1"):
                        return False
                elif among_var == 12:
                    if not self.slice_from(u"\u05E9\u05F0\u05F2\u05D2"):
                        return False
                elif among_var == 13:
                    if not self.slice_from(u"\u05E9\u05DE\u05F2\u05E1"):
                        return False
                elif among_var == 14:
                    if not self.slice_from(u"\u05E9\u05E0\u05F2\u05D3"):
                        return False
                elif among_var == 15:
                    if not self.slice_from(u"\u05E9\u05E8\u05F2\u05D1"):
                        return False
                elif among_var == 16:
                    if not self.slice_from(u"\u05D1\u05D9\u05E0\u05D3"):
                        return False
                elif among_var == 17:
                    if not self.slice_from(u"\u05F0\u05D9\u05D8\u05E9"):
                        return False
                elif among_var == 18:
                    if not self.slice_from(u"\u05D6\u05D9\u05E0\u05D2"):
                        return False
                elif among_var == 19:
                    if not self.slice_from(u"\u05D8\u05E8\u05D9\u05E0\u05E7"):
                        return False
                elif among_var == 20:
                    if not self.slice_from(u"\u05E6\u05F0\u05D9\u05E0\u05D2"):
                        return False
                elif among_var == 21:
                    if not self.slice_from(u"\u05E9\u05DC\u05D9\u05E0\u05D2"):
                        return False
                elif among_var == 22:
                    if not self.slice_from(u"\u05D1\u05F2\u05D2"):
                        return False
                elif among_var == 23:
                    if not self.slice_from(u"\u05D4\u05F2\u05D1"):
                        return False
                elif among_var == 24:
                    if not self.slice_from(u"\u05E4\u05D0\u05E8\u05DC\u05D9\u05E8"):
                        return False
                elif among_var == 25:
                    if not self.slice_from(u"\u05E9\u05D8\u05F2"):
                        return False
                else:
                    if not self.slice_from(u"\u05E9\u05F0\u05E2\u05E8"):
                        return False
            elif among_var == 4:
                try:
                    v_2 = self.limit - self.cursor
                    try:
                        if not self.__r_R1():
                            raise lab2()
                        if not self.slice_del():
                            return False

                        raise lab1()
                    except lab2: pass
                    self.cursor = self.limit - v_2
                    if not self.slice_from(u"\u05D8"):
                        return False
                except lab1: pass
                self.ket = self.cursor
                if not self.eq_s_b(u"\u05D1\u05E8\u05D0\u05DB"):
                    raise lab0()
                v_3 = self.limit - self.cursor
                try:
                    if not self.eq_s_b(u"\u05D2\u05E2"):
                        self.cursor = self.limit - v_3
                        raise lab3()
                except lab3: pass
                self.bra = self.cursor
                if not self.slice_from(u"\u05D1\u05E8\u05E2\u05E0\u05D2"):
                    return False
            elif among_var == 5:
                if not self.slice_from(u"\u05D2\u05F2"):
                    return False
            elif among_var == 6:
                if not self.slice_from(u"\u05E0\u05E2\u05DE"):
                    return False
            elif among_var == 7:
                if not self.slice_from(u"\u05E9\u05E8\u05F2\u05D1"):
                    return False
            elif among_var == 8:
                if not self.slice_from(u"\u05DE\u05F2\u05D3"):
                    return False
            elif among_var == 9:
                if not self.slice_from(u"\u05D1\u05F2\u05D8"):
                    return False
            elif among_var == 10:
                if not self.slice_from(u"\u05D1\u05F2\u05E1"):
                    return False
            elif among_var == 11:
                if not self.slice_from(u"\u05F0\u05F2\u05D6"):
                    return False
            elif among_var == 12:
                if not self.slice_from(u"\u05D8\u05E8\u05F2\u05D1"):
                    return False
            elif among_var == 13:
                if not self.slice_from(u"\u05DC\u05F2\u05D8"):
                    return False
            elif among_var == 14:
                if not self.slice_from(u"\u05E7\u05DC\u05F2\u05D1"):
                    return False
            elif among_var == 15:
                if not self.slice_from(u"\u05E8\u05F2\u05D1"):
                    return False
            elif among_var == 16:
                if not self.slice_from(u"\u05E8\u05F2\u05E1"):
                    return False
            elif among_var == 17:
                if not self.slice_from(u"\u05E9\u05F0\u05F2\u05D2"):
                    return False
            elif among_var == 18:
                if not self.slice_from(u"\u05E9\u05DE\u05F2\u05E1"):
                    return False
            elif among_var == 19:
                if not self.slice_from(u"\u05E9\u05E0\u05F2\u05D3"):
                    return False
            elif among_var == 20:
                if not self.slice_from(u"\u05D1\u05D9\u05E0\u05D3"):
                    return False
            elif among_var == 21:
                if not self.slice_from(u"\u05F0\u05D9\u05D8\u05E9"):
                    return False
            elif among_var == 22:
                if not self.slice_from(u"\u05D6\u05D9\u05E0\u05D2"):
                    return False
            elif among_var == 23:
                if not self.slice_from(u"\u05D8\u05E8\u05D9\u05E0\u05E7"):
                    return False
            elif among_var == 24:
                if not self.slice_from(u"\u05E6\u05F0\u05D9\u05E0\u05D2"):
                    return False
            elif among_var == 25:
                if not self.slice_from(u"\u05E9\u05DC\u05D9\u05E0\u05D2"):
                    return False
            elif among_var == 26:
                if not self.slice_from(u"\u05D1\u05F2\u05D2"):
                    return False
            elif among_var == 27:
                if not self.slice_from(u"\u05D4\u05F2\u05D1"):
                    return False
            elif among_var == 28:
                if not self.slice_from(u"\u05E4\u05D0\u05E8\u05DC\u05D9\u05E8"):
                    return False
            elif among_var == 29:
                if not self.slice_from(u"\u05E9\u05D8\u05F2"):
                    return False
            elif among_var == 30:
                if not self.slice_from(u"\u05E9\u05F0\u05E2\u05E8"):
                    return False
            elif among_var == 31:
                if not self.slice_from(u"\u05D1\u05E8\u05E2\u05E0\u05D2"):
                    return False
            elif among_var == 32:
                if not self.__r_R1():
                    raise lab0()
                if not self.slice_from(u"\u05D4"):
                    return False
            elif among_var == 33:
                try:
                    v_4 = self.limit - self.cursor
                    try:
                        try:
                            v_5 = self.limit - self.cursor
                            try:
                                if not self.eq_s_b(u"\u05D2"):
                                    raise lab7()
                                raise lab6()
                            except lab7: pass
                            self.cursor = self.limit - v_5
                            if not self.eq_s_b(u"\u05E9"):
                                raise lab5()
                        except lab6: pass
                        v_6 = self.limit - self.cursor
                        try:
                            if not self.__r_R1plus3():
                                self.cursor = self.limit - v_6
                                raise lab8()
                            if not self.slice_from(u"\u05D9\u05E1"):
                                return False
                        except lab8: pass
                        raise lab4()
                    except lab5: pass
                    self.cursor = self.limit - v_4
                    if not self.__r_R1():
                        raise lab0()
                    if not self.slice_del():
                        return False

                except lab4: pass
        except lab0: pass
        self.cursor = self.limit - v_1
        v_7 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            among_var = self.find_among_b(YiddishStemmer.a_5)
            if among_var == 0:
                raise lab9()
            self.bra = self.cursor
            if among_var == 1:
                if not self.__r_R1():
                    raise lab9()
                if not self.slice_del():
                    return False

            else:
                if not self.__r_R1():
                    raise lab9()
                if not self.in_grouping_b(YiddishStemmer.g_consonant, 1489, 1520):
                    raise lab9()
                if not self.slice_del():
                    return False

        except lab9: pass
        self.cursor = self.limit - v_7
        v_8 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            among_var = self.find_among_b(YiddishStemmer.a_6)
            if among_var == 0:
                raise lab10()
            self.bra = self.cursor
            if among_var == 1:
                if not self.__r_R1():
                    raise lab10()
                if not self.slice_del():
                    return False

        except lab10: pass
        self.cursor = self.limit - v_8
        v_9 = self.limit - self.cursor
        try:
            while True:
                v_10 = self.limit - self.cursor
                try:
                    try:
                        while True:
                            v_11 = self.limit - self.cursor
                            try:
                                self.ket = self.cursor
                                try:
                                    v_12 = self.limit - self.cursor
                                    try:
                                        if not self.eq_s_b(u"GE"):
                                            raise lab16()
                                        raise lab15()
                                    except lab16: pass
                                    self.cursor = self.limit - v_12
                                    if not self.eq_s_b(u"TSU"):
                                        raise lab14()
                                except lab15: pass
                                self.bra = self.cursor
                                if not self.slice_del():
                                    return False

                                self.cursor = self.limit - v_11
                                raise lab13()
                            except lab14: pass
                            self.cursor = self.limit - v_11
                            if self.cursor <= self.limit_backward:
                                raise lab12()
                            self.cursor -= 1
                    except lab13: pass
                    continue
                except lab12: pass
                self.cursor = self.limit - v_10
                break
        except lab11: pass
        self.cursor = self.limit - v_9
        return True

    def _stem(self):
        self.__r_prelude()
        v_2 = self.cursor
        self.__r_mark_regions()
        self.cursor = v_2
        self.limit_backward = self.cursor
        self.cursor = self.limit
        self.__r_standard_suffix()
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass


class lab8(BaseException): pass


class lab9(BaseException): pass


class lab10(BaseException): pass


class lab11(BaseException): pass


class lab12(BaseException): pass


class lab13(BaseException): pass


class lab14(BaseException): pass


class lab15(BaseException): pass


class lab16(BaseException): pass
