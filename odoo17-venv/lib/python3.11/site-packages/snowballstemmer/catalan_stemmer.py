# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class CatalanStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"", -1, 7),
        Among(u"\u00B7", 0, 6),
        Among(u"\u00E0", 0, 1),
        Among(u"\u00E1", 0, 1),
        Among(u"\u00E8", 0, 2),
        Among(u"\u00E9", 0, 2),
        Among(u"\u00EC", 0, 3),
        Among(u"\u00ED", 0, 3),
        Among(u"\u00EF", 0, 3),
        Among(u"\u00F2", 0, 4),
        Among(u"\u00F3", 0, 4),
        Among(u"\u00FA", 0, 5),
        Among(u"\u00FC", 0, 5)
    ]

    a_1 = [
        Among(u"la", -1, 1),
        Among(u"-la", 0, 1),
        Among(u"sela", 0, 1),
        Among(u"le", -1, 1),
        Among(u"me", -1, 1),
        Among(u"-me", 4, 1),
        Among(u"se", -1, 1),
        Among(u"-te", -1, 1),
        Among(u"hi", -1, 1),
        Among(u"'hi", 8, 1),
        Among(u"li", -1, 1),
        Among(u"-li", 10, 1),
        Among(u"'l", -1, 1),
        Among(u"'m", -1, 1),
        Among(u"-m", -1, 1),
        Among(u"'n", -1, 1),
        Among(u"-n", -1, 1),
        Among(u"ho", -1, 1),
        Among(u"'ho", 17, 1),
        Among(u"lo", -1, 1),
        Among(u"selo", 19, 1),
        Among(u"'s", -1, 1),
        Among(u"las", -1, 1),
        Among(u"selas", 22, 1),
        Among(u"les", -1, 1),
        Among(u"-les", 24, 1),
        Among(u"'ls", -1, 1),
        Among(u"-ls", -1, 1),
        Among(u"'ns", -1, 1),
        Among(u"-ns", -1, 1),
        Among(u"ens", -1, 1),
        Among(u"los", -1, 1),
        Among(u"selos", 31, 1),
        Among(u"nos", -1, 1),
        Among(u"-nos", 33, 1),
        Among(u"vos", -1, 1),
        Among(u"us", -1, 1),
        Among(u"-us", 36, 1),
        Among(u"'t", -1, 1)
    ]

    a_2 = [
        Among(u"ica", -1, 4),
        Among(u"l\u00F3gica", 0, 3),
        Among(u"enca", -1, 1),
        Among(u"ada", -1, 2),
        Among(u"ancia", -1, 1),
        Among(u"encia", -1, 1),
        Among(u"\u00E8ncia", -1, 1),
        Among(u"\u00EDcia", -1, 1),
        Among(u"logia", -1, 3),
        Among(u"inia", -1, 1),
        Among(u"\u00EDinia", 9, 1),
        Among(u"eria", -1, 1),
        Among(u"\u00E0ria", -1, 1),
        Among(u"at\u00F2ria", -1, 1),
        Among(u"alla", -1, 1),
        Among(u"ella", -1, 1),
        Among(u"\u00EDvola", -1, 1),
        Among(u"ima", -1, 1),
        Among(u"\u00EDssima", 17, 1),
        Among(u"qu\u00EDssima", 18, 5),
        Among(u"ana", -1, 1),
        Among(u"ina", -1, 1),
        Among(u"era", -1, 1),
        Among(u"sfera", 22, 1),
        Among(u"ora", -1, 1),
        Among(u"dora", 24, 1),
        Among(u"adora", 25, 1),
        Among(u"adura", -1, 1),
        Among(u"esa", -1, 1),
        Among(u"osa", -1, 1),
        Among(u"assa", -1, 1),
        Among(u"essa", -1, 1),
        Among(u"issa", -1, 1),
        Among(u"eta", -1, 1),
        Among(u"ita", -1, 1),
        Among(u"ota", -1, 1),
        Among(u"ista", -1, 1),
        Among(u"ialista", 36, 1),
        Among(u"ionista", 36, 1),
        Among(u"iva", -1, 1),
        Among(u"ativa", 39, 1),
        Among(u"n\u00E7a", -1, 1),
        Among(u"log\u00EDa", -1, 3),
        Among(u"ic", -1, 4),
        Among(u"\u00EDstic", 43, 1),
        Among(u"enc", -1, 1),
        Among(u"esc", -1, 1),
        Among(u"ud", -1, 1),
        Among(u"atge", -1, 1),
        Among(u"ble", -1, 1),
        Among(u"able", 49, 1),
        Among(u"ible", 49, 1),
        Among(u"isme", -1, 1),
        Among(u"ialisme", 52, 1),
        Among(u"ionisme", 52, 1),
        Among(u"ivisme", 52, 1),
        Among(u"aire", -1, 1),
        Among(u"icte", -1, 1),
        Among(u"iste", -1, 1),
        Among(u"ici", -1, 1),
        Among(u"\u00EDci", -1, 1),
        Among(u"logi", -1, 3),
        Among(u"ari", -1, 1),
        Among(u"tori", -1, 1),
        Among(u"al", -1, 1),
        Among(u"il", -1, 1),
        Among(u"all", -1, 1),
        Among(u"ell", -1, 1),
        Among(u"\u00EDvol", -1, 1),
        Among(u"isam", -1, 1),
        Among(u"issem", -1, 1),
        Among(u"\u00ECssem", -1, 1),
        Among(u"\u00EDssem", -1, 1),
        Among(u"\u00EDssim", -1, 1),
        Among(u"qu\u00EDssim", 73, 5),
        Among(u"amen", -1, 1),
        Among(u"\u00ECssin", -1, 1),
        Among(u"ar", -1, 1),
        Among(u"ificar", 77, 1),
        Among(u"egar", 77, 1),
        Among(u"ejar", 77, 1),
        Among(u"itar", 77, 1),
        Among(u"itzar", 77, 1),
        Among(u"fer", -1, 1),
        Among(u"or", -1, 1),
        Among(u"dor", 84, 1),
        Among(u"dur", -1, 1),
        Among(u"doras", -1, 1),
        Among(u"ics", -1, 4),
        Among(u"l\u00F3gics", 88, 3),
        Among(u"uds", -1, 1),
        Among(u"nces", -1, 1),
        Among(u"ades", -1, 2),
        Among(u"ancies", -1, 1),
        Among(u"encies", -1, 1),
        Among(u"\u00E8ncies", -1, 1),
        Among(u"\u00EDcies", -1, 1),
        Among(u"logies", -1, 3),
        Among(u"inies", -1, 1),
        Among(u"\u00EDnies", -1, 1),
        Among(u"eries", -1, 1),
        Among(u"\u00E0ries", -1, 1),
        Among(u"at\u00F2ries", -1, 1),
        Among(u"bles", -1, 1),
        Among(u"ables", 103, 1),
        Among(u"ibles", 103, 1),
        Among(u"imes", -1, 1),
        Among(u"\u00EDssimes", 106, 1),
        Among(u"qu\u00EDssimes", 107, 5),
        Among(u"formes", -1, 1),
        Among(u"ismes", -1, 1),
        Among(u"ialismes", 110, 1),
        Among(u"ines", -1, 1),
        Among(u"eres", -1, 1),
        Among(u"ores", -1, 1),
        Among(u"dores", 114, 1),
        Among(u"idores", 115, 1),
        Among(u"dures", -1, 1),
        Among(u"eses", -1, 1),
        Among(u"oses", -1, 1),
        Among(u"asses", -1, 1),
        Among(u"ictes", -1, 1),
        Among(u"ites", -1, 1),
        Among(u"otes", -1, 1),
        Among(u"istes", -1, 1),
        Among(u"ialistes", 124, 1),
        Among(u"ionistes", 124, 1),
        Among(u"iques", -1, 4),
        Among(u"l\u00F3giques", 127, 3),
        Among(u"ives", -1, 1),
        Among(u"atives", 129, 1),
        Among(u"log\u00EDes", -1, 3),
        Among(u"alleng\u00FCes", -1, 1),
        Among(u"icis", -1, 1),
        Among(u"\u00EDcis", -1, 1),
        Among(u"logis", -1, 3),
        Among(u"aris", -1, 1),
        Among(u"toris", -1, 1),
        Among(u"ls", -1, 1),
        Among(u"als", 138, 1),
        Among(u"ells", 138, 1),
        Among(u"ims", -1, 1),
        Among(u"\u00EDssims", 141, 1),
        Among(u"qu\u00EDssims", 142, 5),
        Among(u"ions", -1, 1),
        Among(u"cions", 144, 1),
        Among(u"acions", 145, 2),
        Among(u"esos", -1, 1),
        Among(u"osos", -1, 1),
        Among(u"assos", -1, 1),
        Among(u"issos", -1, 1),
        Among(u"ers", -1, 1),
        Among(u"ors", -1, 1),
        Among(u"dors", 152, 1),
        Among(u"adors", 153, 1),
        Among(u"idors", 153, 1),
        Among(u"ats", -1, 1),
        Among(u"itats", 156, 1),
        Among(u"bilitats", 157, 1),
        Among(u"ivitats", 157, 1),
        Among(u"ativitats", 159, 1),
        Among(u"\u00EFtats", 156, 1),
        Among(u"ets", -1, 1),
        Among(u"ants", -1, 1),
        Among(u"ents", -1, 1),
        Among(u"ments", 164, 1),
        Among(u"aments", 165, 1),
        Among(u"ots", -1, 1),
        Among(u"uts", -1, 1),
        Among(u"ius", -1, 1),
        Among(u"trius", 169, 1),
        Among(u"atius", 169, 1),
        Among(u"\u00E8s", -1, 1),
        Among(u"\u00E9s", -1, 1),
        Among(u"\u00EDs", -1, 1),
        Among(u"d\u00EDs", 174, 1),
        Among(u"\u00F3s", -1, 1),
        Among(u"itat", -1, 1),
        Among(u"bilitat", 177, 1),
        Among(u"ivitat", 177, 1),
        Among(u"ativitat", 179, 1),
        Among(u"\u00EFtat", -1, 1),
        Among(u"et", -1, 1),
        Among(u"ant", -1, 1),
        Among(u"ent", -1, 1),
        Among(u"ient", 184, 1),
        Among(u"ment", 184, 1),
        Among(u"ament", 186, 1),
        Among(u"isament", 187, 1),
        Among(u"ot", -1, 1),
        Among(u"isseu", -1, 1),
        Among(u"\u00ECsseu", -1, 1),
        Among(u"\u00EDsseu", -1, 1),
        Among(u"triu", -1, 1),
        Among(u"\u00EDssiu", -1, 1),
        Among(u"atiu", -1, 1),
        Among(u"\u00F3", -1, 1),
        Among(u"i\u00F3", 196, 1),
        Among(u"ci\u00F3", 197, 1),
        Among(u"aci\u00F3", 198, 1)
    ]

    a_3 = [
        Among(u"aba", -1, 1),
        Among(u"esca", -1, 1),
        Among(u"isca", -1, 1),
        Among(u"\u00EFsca", -1, 1),
        Among(u"ada", -1, 1),
        Among(u"ida", -1, 1),
        Among(u"uda", -1, 1),
        Among(u"\u00EFda", -1, 1),
        Among(u"ia", -1, 1),
        Among(u"aria", 8, 1),
        Among(u"iria", 8, 1),
        Among(u"ara", -1, 1),
        Among(u"iera", -1, 1),
        Among(u"ira", -1, 1),
        Among(u"adora", -1, 1),
        Among(u"\u00EFra", -1, 1),
        Among(u"ava", -1, 1),
        Among(u"ixa", -1, 1),
        Among(u"itza", -1, 1),
        Among(u"\u00EDa", -1, 1),
        Among(u"ar\u00EDa", 19, 1),
        Among(u"er\u00EDa", 19, 1),
        Among(u"ir\u00EDa", 19, 1),
        Among(u"\u00EFa", -1, 1),
        Among(u"isc", -1, 1),
        Among(u"\u00EFsc", -1, 1),
        Among(u"ad", -1, 1),
        Among(u"ed", -1, 1),
        Among(u"id", -1, 1),
        Among(u"ie", -1, 1),
        Among(u"re", -1, 1),
        Among(u"dre", 30, 1),
        Among(u"ase", -1, 1),
        Among(u"iese", -1, 1),
        Among(u"aste", -1, 1),
        Among(u"iste", -1, 1),
        Among(u"ii", -1, 1),
        Among(u"ini", -1, 1),
        Among(u"esqui", -1, 1),
        Among(u"eixi", -1, 1),
        Among(u"itzi", -1, 1),
        Among(u"am", -1, 1),
        Among(u"em", -1, 1),
        Among(u"arem", 42, 1),
        Among(u"irem", 42, 1),
        Among(u"\u00E0rem", 42, 1),
        Among(u"\u00EDrem", 42, 1),
        Among(u"\u00E0ssem", 42, 1),
        Among(u"\u00E9ssem", 42, 1),
        Among(u"iguem", 42, 1),
        Among(u"\u00EFguem", 42, 1),
        Among(u"avem", 42, 1),
        Among(u"\u00E0vem", 42, 1),
        Among(u"\u00E1vem", 42, 1),
        Among(u"ir\u00ECem", 42, 1),
        Among(u"\u00EDem", 42, 1),
        Among(u"ar\u00EDem", 55, 1),
        Among(u"ir\u00EDem", 55, 1),
        Among(u"assim", -1, 1),
        Among(u"essim", -1, 1),
        Among(u"issim", -1, 1),
        Among(u"\u00E0ssim", -1, 1),
        Among(u"\u00E8ssim", -1, 1),
        Among(u"\u00E9ssim", -1, 1),
        Among(u"\u00EDssim", -1, 1),
        Among(u"\u00EFm", -1, 1),
        Among(u"an", -1, 1),
        Among(u"aban", 66, 1),
        Among(u"arian", 66, 1),
        Among(u"aran", 66, 1),
        Among(u"ieran", 66, 1),
        Among(u"iran", 66, 1),
        Among(u"\u00EDan", 66, 1),
        Among(u"ar\u00EDan", 72, 1),
        Among(u"er\u00EDan", 72, 1),
        Among(u"ir\u00EDan", 72, 1),
        Among(u"en", -1, 1),
        Among(u"ien", 76, 1),
        Among(u"arien", 77, 1),
        Among(u"irien", 77, 1),
        Among(u"aren", 76, 1),
        Among(u"eren", 76, 1),
        Among(u"iren", 76, 1),
        Among(u"\u00E0ren", 76, 1),
        Among(u"\u00EFren", 76, 1),
        Among(u"asen", 76, 1),
        Among(u"iesen", 76, 1),
        Among(u"assen", 76, 1),
        Among(u"essen", 76, 1),
        Among(u"issen", 76, 1),
        Among(u"\u00E9ssen", 76, 1),
        Among(u"\u00EFssen", 76, 1),
        Among(u"esquen", 76, 1),
        Among(u"isquen", 76, 1),
        Among(u"\u00EFsquen", 76, 1),
        Among(u"aven", 76, 1),
        Among(u"ixen", 76, 1),
        Among(u"eixen", 96, 1),
        Among(u"\u00EFxen", 76, 1),
        Among(u"\u00EFen", 76, 1),
        Among(u"in", -1, 1),
        Among(u"inin", 100, 1),
        Among(u"sin", 100, 1),
        Among(u"isin", 102, 1),
        Among(u"assin", 102, 1),
        Among(u"essin", 102, 1),
        Among(u"issin", 102, 1),
        Among(u"\u00EFssin", 102, 1),
        Among(u"esquin", 100, 1),
        Among(u"eixin", 100, 1),
        Among(u"aron", -1, 1),
        Among(u"ieron", -1, 1),
        Among(u"ar\u00E1n", -1, 1),
        Among(u"er\u00E1n", -1, 1),
        Among(u"ir\u00E1n", -1, 1),
        Among(u"i\u00EFn", -1, 1),
        Among(u"ado", -1, 1),
        Among(u"ido", -1, 1),
        Among(u"ando", -1, 2),
        Among(u"iendo", -1, 1),
        Among(u"io", -1, 1),
        Among(u"ixo", -1, 1),
        Among(u"eixo", 121, 1),
        Among(u"\u00EFxo", -1, 1),
        Among(u"itzo", -1, 1),
        Among(u"ar", -1, 1),
        Among(u"tzar", 125, 1),
        Among(u"er", -1, 1),
        Among(u"eixer", 127, 1),
        Among(u"ir", -1, 1),
        Among(u"ador", -1, 1),
        Among(u"as", -1, 1),
        Among(u"abas", 131, 1),
        Among(u"adas", 131, 1),
        Among(u"idas", 131, 1),
        Among(u"aras", 131, 1),
        Among(u"ieras", 131, 1),
        Among(u"\u00EDas", 131, 1),
        Among(u"ar\u00EDas", 137, 1),
        Among(u"er\u00EDas", 137, 1),
        Among(u"ir\u00EDas", 137, 1),
        Among(u"ids", -1, 1),
        Among(u"es", -1, 1),
        Among(u"ades", 142, 1),
        Among(u"ides", 142, 1),
        Among(u"udes", 142, 1),
        Among(u"\u00EFdes", 142, 1),
        Among(u"atges", 142, 1),
        Among(u"ies", 142, 1),
        Among(u"aries", 148, 1),
        Among(u"iries", 148, 1),
        Among(u"ares", 142, 1),
        Among(u"ires", 142, 1),
        Among(u"adores", 142, 1),
        Among(u"\u00EFres", 142, 1),
        Among(u"ases", 142, 1),
        Among(u"ieses", 142, 1),
        Among(u"asses", 142, 1),
        Among(u"esses", 142, 1),
        Among(u"isses", 142, 1),
        Among(u"\u00EFsses", 142, 1),
        Among(u"ques", 142, 1),
        Among(u"esques", 161, 1),
        Among(u"\u00EFsques", 161, 1),
        Among(u"aves", 142, 1),
        Among(u"ixes", 142, 1),
        Among(u"eixes", 165, 1),
        Among(u"\u00EFxes", 142, 1),
        Among(u"\u00EFes", 142, 1),
        Among(u"abais", -1, 1),
        Among(u"arais", -1, 1),
        Among(u"ierais", -1, 1),
        Among(u"\u00EDais", -1, 1),
        Among(u"ar\u00EDais", 172, 1),
        Among(u"er\u00EDais", 172, 1),
        Among(u"ir\u00EDais", 172, 1),
        Among(u"aseis", -1, 1),
        Among(u"ieseis", -1, 1),
        Among(u"asteis", -1, 1),
        Among(u"isteis", -1, 1),
        Among(u"inis", -1, 1),
        Among(u"sis", -1, 1),
        Among(u"isis", 181, 1),
        Among(u"assis", 181, 1),
        Among(u"essis", 181, 1),
        Among(u"issis", 181, 1),
        Among(u"\u00EFssis", 181, 1),
        Among(u"esquis", -1, 1),
        Among(u"eixis", -1, 1),
        Among(u"itzis", -1, 1),
        Among(u"\u00E1is", -1, 1),
        Among(u"ar\u00E9is", -1, 1),
        Among(u"er\u00E9is", -1, 1),
        Among(u"ir\u00E9is", -1, 1),
        Among(u"ams", -1, 1),
        Among(u"ados", -1, 1),
        Among(u"idos", -1, 1),
        Among(u"amos", -1, 1),
        Among(u"\u00E1bamos", 197, 1),
        Among(u"\u00E1ramos", 197, 1),
        Among(u"i\u00E9ramos", 197, 1),
        Among(u"\u00EDamos", 197, 1),
        Among(u"ar\u00EDamos", 201, 1),
        Among(u"er\u00EDamos", 201, 1),
        Among(u"ir\u00EDamos", 201, 1),
        Among(u"aremos", -1, 1),
        Among(u"eremos", -1, 1),
        Among(u"iremos", -1, 1),
        Among(u"\u00E1semos", -1, 1),
        Among(u"i\u00E9semos", -1, 1),
        Among(u"imos", -1, 1),
        Among(u"adors", -1, 1),
        Among(u"ass", -1, 1),
        Among(u"erass", 212, 1),
        Among(u"ess", -1, 1),
        Among(u"ats", -1, 1),
        Among(u"its", -1, 1),
        Among(u"ents", -1, 1),
        Among(u"\u00E0s", -1, 1),
        Among(u"ar\u00E0s", 218, 1),
        Among(u"ir\u00E0s", 218, 1),
        Among(u"ar\u00E1s", -1, 1),
        Among(u"er\u00E1s", -1, 1),
        Among(u"ir\u00E1s", -1, 1),
        Among(u"\u00E9s", -1, 1),
        Among(u"ar\u00E9s", 224, 1),
        Among(u"\u00EDs", -1, 1),
        Among(u"i\u00EFs", -1, 1),
        Among(u"at", -1, 1),
        Among(u"it", -1, 1),
        Among(u"ant", -1, 1),
        Among(u"ent", -1, 1),
        Among(u"int", -1, 1),
        Among(u"ut", -1, 1),
        Among(u"\u00EFt", -1, 1),
        Among(u"au", -1, 1),
        Among(u"erau", 235, 1),
        Among(u"ieu", -1, 1),
        Among(u"ineu", -1, 1),
        Among(u"areu", -1, 1),
        Among(u"ireu", -1, 1),
        Among(u"\u00E0reu", -1, 1),
        Among(u"\u00EDreu", -1, 1),
        Among(u"asseu", -1, 1),
        Among(u"esseu", -1, 1),
        Among(u"eresseu", 244, 1),
        Among(u"\u00E0sseu", -1, 1),
        Among(u"\u00E9sseu", -1, 1),
        Among(u"igueu", -1, 1),
        Among(u"\u00EFgueu", -1, 1),
        Among(u"\u00E0veu", -1, 1),
        Among(u"\u00E1veu", -1, 1),
        Among(u"itzeu", -1, 1),
        Among(u"\u00ECeu", -1, 1),
        Among(u"ir\u00ECeu", 253, 1),
        Among(u"\u00EDeu", -1, 1),
        Among(u"ar\u00EDeu", 255, 1),
        Among(u"ir\u00EDeu", 255, 1),
        Among(u"assiu", -1, 1),
        Among(u"issiu", -1, 1),
        Among(u"\u00E0ssiu", -1, 1),
        Among(u"\u00E8ssiu", -1, 1),
        Among(u"\u00E9ssiu", -1, 1),
        Among(u"\u00EDssiu", -1, 1),
        Among(u"\u00EFu", -1, 1),
        Among(u"ix", -1, 1),
        Among(u"eix", 265, 1),
        Among(u"\u00EFx", -1, 1),
        Among(u"itz", -1, 1),
        Among(u"i\u00E0", -1, 1),
        Among(u"ar\u00E0", -1, 1),
        Among(u"ir\u00E0", -1, 1),
        Among(u"itz\u00E0", -1, 1),
        Among(u"ar\u00E1", -1, 1),
        Among(u"er\u00E1", -1, 1),
        Among(u"ir\u00E1", -1, 1),
        Among(u"ir\u00E8", -1, 1),
        Among(u"ar\u00E9", -1, 1),
        Among(u"er\u00E9", -1, 1),
        Among(u"ir\u00E9", -1, 1),
        Among(u"\u00ED", -1, 1),
        Among(u"i\u00EF", -1, 1),
        Among(u"i\u00F3", -1, 1)
    ]

    a_4 = [
        Among(u"a", -1, 1),
        Among(u"e", -1, 1),
        Among(u"i", -1, 1),
        Among(u"\u00EFn", -1, 1),
        Among(u"o", -1, 1),
        Among(u"ir", -1, 1),
        Among(u"s", -1, 1),
        Among(u"is", 6, 1),
        Among(u"os", 6, 1),
        Among(u"\u00EFs", 6, 1),
        Among(u"it", -1, 1),
        Among(u"eu", -1, 1),
        Among(u"iu", -1, 1),
        Among(u"iqu", -1, 2),
        Among(u"itz", -1, 1),
        Among(u"\u00E0", -1, 1),
        Among(u"\u00E1", -1, 1),
        Among(u"\u00E9", -1, 1),
        Among(u"\u00EC", -1, 1),
        Among(u"\u00ED", -1, 1),
        Among(u"\u00EF", -1, 1),
        Among(u"\u00F3", -1, 1)
    ]

    g_v = [17, 65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128, 129, 81, 6, 10]

    I_p2 = 0
    I_p1 = 0

    def __r_mark_regions(self):
        self.I_p1 = self.limit
        self.I_p2 = self.limit
        v_1 = self.cursor
        try:
            if not self.go_out_grouping(CatalanStemmer.g_v, 97, 252):
                raise lab0()
            self.cursor += 1
            if not self.go_in_grouping(CatalanStemmer.g_v, 97, 252):
                raise lab0()
            self.cursor += 1
            self.I_p1 = self.cursor
            if not self.go_out_grouping(CatalanStemmer.g_v, 97, 252):
                raise lab0()
            self.cursor += 1
            if not self.go_in_grouping(CatalanStemmer.g_v, 97, 252):
                raise lab0()
            self.cursor += 1
            self.I_p2 = self.cursor
        except lab0: pass
        self.cursor = v_1
        return True

    def __r_cleaning(self):
        while True:
            v_1 = self.cursor
            try:
                self.bra = self.cursor
                among_var = self.find_among(CatalanStemmer.a_0)
                if among_var == 0:
                    raise lab0()
                self.ket = self.cursor
                if among_var == 1:
                    if not self.slice_from(u"a"):
                        return False
                elif among_var == 2:
                    if not self.slice_from(u"e"):
                        return False
                elif among_var == 3:
                    if not self.slice_from(u"i"):
                        return False
                elif among_var == 4:
                    if not self.slice_from(u"o"):
                        return False
                elif among_var == 5:
                    if not self.slice_from(u"u"):
                        return False
                elif among_var == 6:
                    if not self.slice_from(u"."):
                        return False
                else:
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                continue
            except lab0: pass
            self.cursor = v_1
            break
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_attached_pronoun(self):
        self.ket = self.cursor
        if self.find_among_b(CatalanStemmer.a_1) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if not self.slice_del():
            return False

        return True

    def __r_standard_suffix(self):
        self.ket = self.cursor
        among_var = self.find_among_b(CatalanStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        elif among_var == 3:
            if not self.__r_R2():
                return False
            if not self.slice_from(u"log"):
                return False
        elif among_var == 4:
            if not self.__r_R2():
                return False
            if not self.slice_from(u"ic"):
                return False
        else:
            if not self.__r_R1():
                return False
            if not self.slice_from(u"c"):
                return False
        return True

    def __r_verb_suffix(self):
        self.ket = self.cursor
        among_var = self.find_among_b(CatalanStemmer.a_3)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        else:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        return True

    def __r_residual_suffix(self):
        self.ket = self.cursor
        among_var = self.find_among_b(CatalanStemmer.a_4)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        else:
            if not self.__r_R1():
                return False
            if not self.slice_from(u"ic"):
                return False
        return True

    def _stem(self):
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_2 = self.limit - self.cursor
        self.__r_attached_pronoun()
        self.cursor = self.limit - v_2
        v_3 = self.limit - self.cursor
        try:
            try:
                v_4 = self.limit - self.cursor
                try:
                    if not self.__r_standard_suffix():
                        raise lab2()
                    raise lab1()
                except lab2: pass
                self.cursor = self.limit - v_4
                if not self.__r_verb_suffix():
                    raise lab0()
            except lab1: pass
        except lab0: pass
        self.cursor = self.limit - v_3
        v_5 = self.limit - self.cursor
        self.__r_residual_suffix()
        self.cursor = self.limit - v_5
        self.cursor = self.limit_backward
        v_6 = self.cursor
        self.__r_cleaning()
        self.cursor = v_6
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass
