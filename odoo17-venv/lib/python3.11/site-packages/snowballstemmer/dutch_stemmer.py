# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class DutchStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"", -1, 6),
        Among(u"\u00E1", 0, 1),
        Among(u"\u00E4", 0, 1),
        Among(u"\u00E9", 0, 2),
        Among(u"\u00EB", 0, 2),
        Among(u"\u00ED", 0, 3),
        Among(u"\u00EF", 0, 3),
        Among(u"\u00F3", 0, 4),
        Among(u"\u00F6", 0, 4),
        Among(u"\u00FA", 0, 5),
        Among(u"\u00FC", 0, 5)
    ]

    a_1 = [
        Among(u"", -1, 3),
        Among(u"I", 0, 2),
        Among(u"Y", 0, 1)
    ]

    a_2 = [
        Among(u"dd", -1, -1),
        Among(u"kk", -1, -1),
        Among(u"tt", -1, -1)
    ]

    a_3 = [
        Among(u"ene", -1, 2),
        Among(u"se", -1, 3),
        Among(u"en", -1, 2),
        Among(u"heden", 2, 1),
        Among(u"s", -1, 3)
    ]

    a_4 = [
        Among(u"end", -1, 1),
        Among(u"ig", -1, 2),
        Among(u"ing", -1, 1),
        Among(u"lijk", -1, 3),
        Among(u"baar", -1, 4),
        Among(u"bar", -1, 5)
    ]

    a_5 = [
        Among(u"aa", -1, -1),
        Among(u"ee", -1, -1),
        Among(u"oo", -1, -1),
        Among(u"uu", -1, -1)
    ]

    g_v = [17, 65, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128]

    g_v_I = [1, 0, 0, 17, 65, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128]

    g_v_j = [17, 67, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128]

    I_p2 = 0
    I_p1 = 0
    B_e_found = False

    def __r_prelude(self):
        v_1 = self.cursor
        while True:
            v_2 = self.cursor
            try:
                self.bra = self.cursor
                among_var = self.find_among(DutchStemmer.a_0)
                if among_var == 0:
                    raise lab0()
                self.ket = self.cursor
                if among_var == 1:
                    if not self.slice_from(u"a"):
                        return False
                elif among_var == 2:
                    if not self.slice_from(u"e"):
                        return False
                elif among_var == 3:
                    if not self.slice_from(u"i"):
                        return False
                elif among_var == 4:
                    if not self.slice_from(u"o"):
                        return False
                elif among_var == 5:
                    if not self.slice_from(u"u"):
                        return False
                else:
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                continue
            except lab0: pass
            self.cursor = v_2
            break
        self.cursor = v_1
        v_3 = self.cursor
        try:
            self.bra = self.cursor
            if not self.eq_s(u"y"):
                self.cursor = v_3
                raise lab1()
            self.ket = self.cursor
            if not self.slice_from(u"Y"):
                return False
        except lab1: pass
        while True:
            v_4 = self.cursor
            try:
                try:
                    while True:
                        v_5 = self.cursor
                        try:
                            if not self.in_grouping(DutchStemmer.g_v, 97, 232):
                                raise lab4()
                            self.bra = self.cursor
                            try:
                                v_6 = self.cursor
                                try:
                                    if not self.eq_s(u"i"):
                                        raise lab6()
                                    self.ket = self.cursor
                                    if not self.in_grouping(DutchStemmer.g_v, 97, 232):
                                        raise lab6()
                                    if not self.slice_from(u"I"):
                                        return False
                                    raise lab5()
                                except lab6: pass
                                self.cursor = v_6
                                if not self.eq_s(u"y"):
                                    raise lab4()
                                self.ket = self.cursor
                                if not self.slice_from(u"Y"):
                                    return False
                            except lab5: pass
                            self.cursor = v_5
                            raise lab3()
                        except lab4: pass
                        self.cursor = v_5
                        if self.cursor >= self.limit:
                            raise lab2()
                        self.cursor += 1
                except lab3: pass
                continue
            except lab2: pass
            self.cursor = v_4
            break
        return True

    def __r_mark_regions(self):
        self.I_p1 = self.limit
        self.I_p2 = self.limit
        if not self.go_out_grouping(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor += 1
        if not self.go_in_grouping(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor += 1
        self.I_p1 = self.cursor
        try:
            if not self.I_p1 < 3:
                raise lab0()
            self.I_p1 = 3
        except lab0: pass
        if not self.go_out_grouping(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor += 1
        if not self.go_in_grouping(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor += 1
        self.I_p2 = self.cursor
        return True

    def __r_postlude(self):
        while True:
            v_1 = self.cursor
            try:
                self.bra = self.cursor
                among_var = self.find_among(DutchStemmer.a_1)
                if among_var == 0:
                    raise lab0()
                self.ket = self.cursor
                if among_var == 1:
                    if not self.slice_from(u"y"):
                        return False
                elif among_var == 2:
                    if not self.slice_from(u"i"):
                        return False
                else:
                    if self.cursor >= self.limit:
                        raise lab0()
                    self.cursor += 1
                continue
            except lab0: pass
            self.cursor = v_1
            break
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_undouble(self):
        v_1 = self.limit - self.cursor
        if self.find_among_b(DutchStemmer.a_2) == 0:
            return False
        self.cursor = self.limit - v_1
        self.ket = self.cursor
        if self.cursor <= self.limit_backward:
            return False
        self.cursor -= 1
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_e_ending(self):
        self.B_e_found = False
        self.ket = self.cursor
        if not self.eq_s_b(u"e"):
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        v_1 = self.limit - self.cursor
        if not self.out_grouping_b(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor = self.limit - v_1
        if not self.slice_del():
            return False

        self.B_e_found = True
        if not self.__r_undouble():
            return False
        return True

    def __r_en_ending(self):
        if not self.__r_R1():
            return False
        v_1 = self.limit - self.cursor
        if not self.out_grouping_b(DutchStemmer.g_v, 97, 232):
            return False
        self.cursor = self.limit - v_1
        v_2 = self.limit - self.cursor
        try:
            if not self.eq_s_b(u"gem"):
                raise lab0()
            return False
        except lab0: pass
        self.cursor = self.limit - v_2
        if not self.slice_del():
            return False

        if not self.__r_undouble():
            return False
        return True

    def __r_standard_suffix(self):
        v_1 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            among_var = self.find_among_b(DutchStemmer.a_3)
            if among_var == 0:
                raise lab0()
            self.bra = self.cursor
            if among_var == 1:
                if not self.__r_R1():
                    raise lab0()
                if not self.slice_from(u"heid"):
                    return False
            elif among_var == 2:
                if not self.__r_en_ending():
                    raise lab0()
            else:
                if not self.__r_R1():
                    raise lab0()
                if not self.out_grouping_b(DutchStemmer.g_v_j, 97, 232):
                    raise lab0()
                if not self.slice_del():
                    return False

        except lab0: pass
        self.cursor = self.limit - v_1
        v_2 = self.limit - self.cursor
        self.__r_e_ending()
        self.cursor = self.limit - v_2
        v_3 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            if not self.eq_s_b(u"heid"):
                raise lab1()
            self.bra = self.cursor
            if not self.__r_R2():
                raise lab1()
            v_4 = self.limit - self.cursor
            try:
                if not self.eq_s_b(u"c"):
                    raise lab2()
                raise lab1()
            except lab2: pass
            self.cursor = self.limit - v_4
            if not self.slice_del():
                return False

            self.ket = self.cursor
            if not self.eq_s_b(u"en"):
                raise lab1()
            self.bra = self.cursor
            if not self.__r_en_ending():
                raise lab1()
        except lab1: pass
        self.cursor = self.limit - v_3
        v_5 = self.limit - self.cursor
        try:
            self.ket = self.cursor
            among_var = self.find_among_b(DutchStemmer.a_4)
            if among_var == 0:
                raise lab3()
            self.bra = self.cursor
            if among_var == 1:
                if not self.__r_R2():
                    raise lab3()
                if not self.slice_del():
                    return False

                try:
                    v_6 = self.limit - self.cursor
                    try:
                        self.ket = self.cursor
                        if not self.eq_s_b(u"ig"):
                            raise lab5()
                        self.bra = self.cursor
                        if not self.__r_R2():
                            raise lab5()
                        v_7 = self.limit - self.cursor
                        try:
                            if not self.eq_s_b(u"e"):
                                raise lab6()
                            raise lab5()
                        except lab6: pass
                        self.cursor = self.limit - v_7
                        if not self.slice_del():
                            return False

                        raise lab4()
                    except lab5: pass
                    self.cursor = self.limit - v_6
                    if not self.__r_undouble():
                        raise lab3()
                except lab4: pass
            elif among_var == 2:
                if not self.__r_R2():
                    raise lab3()
                v_8 = self.limit - self.cursor
                try:
                    if not self.eq_s_b(u"e"):
                        raise lab7()
                    raise lab3()
                except lab7: pass
                self.cursor = self.limit - v_8
                if not self.slice_del():
                    return False

            elif among_var == 3:
                if not self.__r_R2():
                    raise lab3()
                if not self.slice_del():
                    return False

                if not self.__r_e_ending():
                    raise lab3()
            elif among_var == 4:
                if not self.__r_R2():
                    raise lab3()
                if not self.slice_del():
                    return False

            else:
                if not self.__r_R2():
                    raise lab3()
                if not self.B_e_found:
                    raise lab3()
                if not self.slice_del():
                    return False

        except lab3: pass
        self.cursor = self.limit - v_5
        v_9 = self.limit - self.cursor
        try:
            if not self.out_grouping_b(DutchStemmer.g_v_I, 73, 232):
                raise lab8()
            v_10 = self.limit - self.cursor
            if self.find_among_b(DutchStemmer.a_5) == 0:
                raise lab8()
            if not self.out_grouping_b(DutchStemmer.g_v, 97, 232):
                raise lab8()
            self.cursor = self.limit - v_10
            self.ket = self.cursor
            if self.cursor <= self.limit_backward:
                raise lab8()
            self.cursor -= 1
            self.bra = self.cursor
            if not self.slice_del():
                return False

        except lab8: pass
        self.cursor = self.limit - v_9
        return True

    def _stem(self):
        v_1 = self.cursor
        self.__r_prelude()
        self.cursor = v_1
        v_2 = self.cursor
        self.__r_mark_regions()
        self.cursor = v_2
        self.limit_backward = self.cursor
        self.cursor = self.limit
        self.__r_standard_suffix()
        self.cursor = self.limit_backward
        v_4 = self.cursor
        self.__r_postlude()
        self.cursor = v_4
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass


class lab6(BaseException): pass


class lab7(BaseException): pass


class lab8(BaseException): pass
