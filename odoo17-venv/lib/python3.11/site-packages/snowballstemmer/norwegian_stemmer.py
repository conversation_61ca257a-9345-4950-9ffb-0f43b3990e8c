# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class NorwegianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"a", -1, 1),
        Among(u"e", -1, 1),
        Among(u"ede", 1, 1),
        Among(u"ande", 1, 1),
        Among(u"ende", 1, 1),
        Among(u"ane", 1, 1),
        Among(u"ene", 1, 1),
        Among(u"hetene", 6, 1),
        Among(u"erte", 1, 3),
        Among(u"en", -1, 1),
        Among(u"heten", 9, 1),
        Among(u"ar", -1, 1),
        Among(u"er", -1, 1),
        Among(u"heter", 12, 1),
        Among(u"s", -1, 2),
        Among(u"as", 14, 1),
        Among(u"es", 14, 1),
        Among(u"edes", 16, 1),
        Among(u"endes", 16, 1),
        Among(u"enes", 16, 1),
        Among(u"hetenes", 19, 1),
        Among(u"ens", 14, 1),
        Among(u"hetens", 21, 1),
        Among(u"ers", 14, 1),
        Among(u"ets", 14, 1),
        Among(u"et", -1, 1),
        Among(u"het", 25, 1),
        Among(u"ert", -1, 3),
        Among(u"ast", -1, 1)
    ]

    a_1 = [
        Among(u"dt", -1, -1),
        Among(u"vt", -1, -1)
    ]

    a_2 = [
        Among(u"leg", -1, 1),
        Among(u"eleg", 0, 1),
        Among(u"ig", -1, 1),
        Among(u"eig", 2, 1),
        Among(u"lig", 2, 1),
        Among(u"elig", 4, 1),
        Among(u"els", -1, 1),
        Among(u"lov", -1, 1),
        Among(u"elov", 7, 1),
        Among(u"slov", 7, 1),
        Among(u"hetslov", 9, 1)
    ]

    g_v = [17, 65, 16, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 128]

    g_s_ending = [119, 125, 149, 1]

    I_x = 0
    I_p1 = 0

    def __r_mark_regions(self):
        self.I_p1 = self.limit
        v_1 = self.cursor
        c = self.cursor + 3
        if c > self.limit:
            return False
        self.cursor = c
        self.I_x = self.cursor
        self.cursor = v_1
        if not self.go_out_grouping(NorwegianStemmer.g_v, 97, 248):
            return False
        if not self.go_in_grouping(NorwegianStemmer.g_v, 97, 248):
            return False
        self.cursor += 1
        self.I_p1 = self.cursor
        try:
            if not self.I_p1 < self.I_x:
                raise lab0()
            self.I_p1 = self.I_x
        except lab0: pass
        return True

    def __r_main_suffix(self):
        if self.cursor < self.I_p1:
            return False
        v_2 = self.limit_backward
        self.limit_backward = self.I_p1
        self.ket = self.cursor
        among_var = self.find_among_b(NorwegianStemmer.a_0)
        if among_var == 0:
            self.limit_backward = v_2
            return False
        self.bra = self.cursor
        self.limit_backward = v_2
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            try:
                v_3 = self.limit - self.cursor
                try:
                    if not self.in_grouping_b(NorwegianStemmer.g_s_ending, 98, 122):
                        raise lab1()
                    raise lab0()
                except lab1: pass
                self.cursor = self.limit - v_3
                if not self.eq_s_b(u"k"):
                    return False
                if not self.out_grouping_b(NorwegianStemmer.g_v, 97, 248):
                    return False
            except lab0: pass
            if not self.slice_del():
                return False

        else:
            if not self.slice_from(u"er"):
                return False
        return True

    def __r_consonant_pair(self):
        v_1 = self.limit - self.cursor
        if self.cursor < self.I_p1:
            return False
        v_3 = self.limit_backward
        self.limit_backward = self.I_p1
        self.ket = self.cursor
        if self.find_among_b(NorwegianStemmer.a_1) == 0:
            self.limit_backward = v_3
            return False
        self.bra = self.cursor
        self.limit_backward = v_3
        self.cursor = self.limit - v_1
        if self.cursor <= self.limit_backward:
            return False
        self.cursor -= 1
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_other_suffix(self):
        if self.cursor < self.I_p1:
            return False
        v_2 = self.limit_backward
        self.limit_backward = self.I_p1
        self.ket = self.cursor
        if self.find_among_b(NorwegianStemmer.a_2) == 0:
            self.limit_backward = v_2
            return False
        self.bra = self.cursor
        self.limit_backward = v_2
        if not self.slice_del():
            return False

        return True

    def _stem(self):
        v_1 = self.cursor
        self.__r_mark_regions()
        self.cursor = v_1
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_2 = self.limit - self.cursor
        self.__r_main_suffix()
        self.cursor = self.limit - v_2
        v_3 = self.limit - self.cursor
        self.__r_consonant_pair()
        self.cursor = self.limit - v_3
        v_4 = self.limit - self.cursor
        self.__r_other_suffix()
        self.cursor = self.limit - v_4
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass
