# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class HungarianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"cs", -1, -1),
        Among(u"dzs", -1, -1),
        Among(u"gy", -1, -1),
        Among(u"ly", -1, -1),
        Among(u"ny", -1, -1),
        Among(u"sz", -1, -1),
        Among(u"ty", -1, -1),
        Among(u"zs", -1, -1)
    ]

    a_1 = [
        Among(u"\u00E1", -1, 1),
        Among(u"\u00E9", -1, 2)
    ]

    a_2 = [
        Among(u"bb", -1, -1),
        Among(u"cc", -1, -1),
        Among(u"dd", -1, -1),
        Among(u"ff", -1, -1),
        Among(u"gg", -1, -1),
        Among(u"jj", -1, -1),
        Among(u"kk", -1, -1),
        Among(u"ll", -1, -1),
        Among(u"mm", -1, -1),
        Among(u"nn", -1, -1),
        Among(u"pp", -1, -1),
        Among(u"rr", -1, -1),
        Among(u"ccs", -1, -1),
        Among(u"ss", -1, -1),
        Among(u"zzs", -1, -1),
        Among(u"tt", -1, -1),
        Among(u"vv", -1, -1),
        Among(u"ggy", -1, -1),
        Among(u"lly", -1, -1),
        Among(u"nny", -1, -1),
        Among(u"tty", -1, -1),
        Among(u"ssz", -1, -1),
        Among(u"zz", -1, -1)
    ]

    a_3 = [
        Among(u"al", -1, 1),
        Among(u"el", -1, 1)
    ]

    a_4 = [
        Among(u"ba", -1, -1),
        Among(u"ra", -1, -1),
        Among(u"be", -1, -1),
        Among(u"re", -1, -1),
        Among(u"ig", -1, -1),
        Among(u"nak", -1, -1),
        Among(u"nek", -1, -1),
        Among(u"val", -1, -1),
        Among(u"vel", -1, -1),
        Among(u"ul", -1, -1),
        Among(u"n\u00E1l", -1, -1),
        Among(u"n\u00E9l", -1, -1),
        Among(u"b\u00F3l", -1, -1),
        Among(u"r\u00F3l", -1, -1),
        Among(u"t\u00F3l", -1, -1),
        Among(u"\u00FCl", -1, -1),
        Among(u"b\u0151l", -1, -1),
        Among(u"r\u0151l", -1, -1),
        Among(u"t\u0151l", -1, -1),
        Among(u"n", -1, -1),
        Among(u"an", 19, -1),
        Among(u"ban", 20, -1),
        Among(u"en", 19, -1),
        Among(u"ben", 22, -1),
        Among(u"k\u00E9ppen", 22, -1),
        Among(u"on", 19, -1),
        Among(u"\u00F6n", 19, -1),
        Among(u"k\u00E9pp", -1, -1),
        Among(u"kor", -1, -1),
        Among(u"t", -1, -1),
        Among(u"at", 29, -1),
        Among(u"et", 29, -1),
        Among(u"k\u00E9nt", 29, -1),
        Among(u"ank\u00E9nt", 32, -1),
        Among(u"enk\u00E9nt", 32, -1),
        Among(u"onk\u00E9nt", 32, -1),
        Among(u"ot", 29, -1),
        Among(u"\u00E9rt", 29, -1),
        Among(u"\u00F6t", 29, -1),
        Among(u"hez", -1, -1),
        Among(u"hoz", -1, -1),
        Among(u"h\u00F6z", -1, -1),
        Among(u"v\u00E1", -1, -1),
        Among(u"v\u00E9", -1, -1)
    ]

    a_5 = [
        Among(u"\u00E1n", -1, 2),
        Among(u"\u00E9n", -1, 1),
        Among(u"\u00E1nk\u00E9nt", -1, 2)
    ]

    a_6 = [
        Among(u"stul", -1, 1),
        Among(u"astul", 0, 1),
        Among(u"\u00E1stul", 0, 2),
        Among(u"st\u00FCl", -1, 1),
        Among(u"est\u00FCl", 3, 1),
        Among(u"\u00E9st\u00FCl", 3, 3)
    ]

    a_7 = [
        Among(u"\u00E1", -1, 1),
        Among(u"\u00E9", -1, 1)
    ]

    a_8 = [
        Among(u"k", -1, 3),
        Among(u"ak", 0, 3),
        Among(u"ek", 0, 3),
        Among(u"ok", 0, 3),
        Among(u"\u00E1k", 0, 1),
        Among(u"\u00E9k", 0, 2),
        Among(u"\u00F6k", 0, 3)
    ]

    a_9 = [
        Among(u"\u00E9i", -1, 1),
        Among(u"\u00E1\u00E9i", 0, 3),
        Among(u"\u00E9\u00E9i", 0, 2),
        Among(u"\u00E9", -1, 1),
        Among(u"k\u00E9", 3, 1),
        Among(u"ak\u00E9", 4, 1),
        Among(u"ek\u00E9", 4, 1),
        Among(u"ok\u00E9", 4, 1),
        Among(u"\u00E1k\u00E9", 4, 3),
        Among(u"\u00E9k\u00E9", 4, 2),
        Among(u"\u00F6k\u00E9", 4, 1),
        Among(u"\u00E9\u00E9", 3, 2)
    ]

    a_10 = [
        Among(u"a", -1, 1),
        Among(u"ja", 0, 1),
        Among(u"d", -1, 1),
        Among(u"ad", 2, 1),
        Among(u"ed", 2, 1),
        Among(u"od", 2, 1),
        Among(u"\u00E1d", 2, 2),
        Among(u"\u00E9d", 2, 3),
        Among(u"\u00F6d", 2, 1),
        Among(u"e", -1, 1),
        Among(u"je", 9, 1),
        Among(u"nk", -1, 1),
        Among(u"unk", 11, 1),
        Among(u"\u00E1nk", 11, 2),
        Among(u"\u00E9nk", 11, 3),
        Among(u"\u00FCnk", 11, 1),
        Among(u"uk", -1, 1),
        Among(u"juk", 16, 1),
        Among(u"\u00E1juk", 17, 2),
        Among(u"\u00FCk", -1, 1),
        Among(u"j\u00FCk", 19, 1),
        Among(u"\u00E9j\u00FCk", 20, 3),
        Among(u"m", -1, 1),
        Among(u"am", 22, 1),
        Among(u"em", 22, 1),
        Among(u"om", 22, 1),
        Among(u"\u00E1m", 22, 2),
        Among(u"\u00E9m", 22, 3),
        Among(u"o", -1, 1),
        Among(u"\u00E1", -1, 2),
        Among(u"\u00E9", -1, 3)
    ]

    a_11 = [
        Among(u"id", -1, 1),
        Among(u"aid", 0, 1),
        Among(u"jaid", 1, 1),
        Among(u"eid", 0, 1),
        Among(u"jeid", 3, 1),
        Among(u"\u00E1id", 0, 2),
        Among(u"\u00E9id", 0, 3),
        Among(u"i", -1, 1),
        Among(u"ai", 7, 1),
        Among(u"jai", 8, 1),
        Among(u"ei", 7, 1),
        Among(u"jei", 10, 1),
        Among(u"\u00E1i", 7, 2),
        Among(u"\u00E9i", 7, 3),
        Among(u"itek", -1, 1),
        Among(u"eitek", 14, 1),
        Among(u"jeitek", 15, 1),
        Among(u"\u00E9itek", 14, 3),
        Among(u"ik", -1, 1),
        Among(u"aik", 18, 1),
        Among(u"jaik", 19, 1),
        Among(u"eik", 18, 1),
        Among(u"jeik", 21, 1),
        Among(u"\u00E1ik", 18, 2),
        Among(u"\u00E9ik", 18, 3),
        Among(u"ink", -1, 1),
        Among(u"aink", 25, 1),
        Among(u"jaink", 26, 1),
        Among(u"eink", 25, 1),
        Among(u"jeink", 28, 1),
        Among(u"\u00E1ink", 25, 2),
        Among(u"\u00E9ink", 25, 3),
        Among(u"aitok", -1, 1),
        Among(u"jaitok", 32, 1),
        Among(u"\u00E1itok", -1, 2),
        Among(u"im", -1, 1),
        Among(u"aim", 35, 1),
        Among(u"jaim", 36, 1),
        Among(u"eim", 35, 1),
        Among(u"jeim", 38, 1),
        Among(u"\u00E1im", 35, 2),
        Among(u"\u00E9im", 35, 3)
    ]

    g_v = [17, 65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17, 36, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1]

    I_p1 = 0

    def __r_mark_regions(self):
        self.I_p1 = self.limit
        try:
            v_1 = self.cursor
            try:
                if not self.in_grouping(HungarianStemmer.g_v, 97, 369):
                    raise lab1()
                if not self.go_in_grouping(HungarianStemmer.g_v, 97, 369):
                    raise lab1()
                try:
                    v_2 = self.cursor
                    try:
                        if self.find_among(HungarianStemmer.a_0) == 0:
                            raise lab3()
                        raise lab2()
                    except lab3: pass
                    self.cursor = v_2
                    if self.cursor >= self.limit:
                        raise lab1()
                    self.cursor += 1
                except lab2: pass
                self.I_p1 = self.cursor
                raise lab0()
            except lab1: pass
            self.cursor = v_1
            if not self.out_grouping(HungarianStemmer.g_v, 97, 369):
                return False
            if not self.go_out_grouping(HungarianStemmer.g_v, 97, 369):
                return False
            self.cursor += 1
            self.I_p1 = self.cursor
        except lab0: pass
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_v_ending(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_1)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_from(u"a"):
                return False
        else:
            if not self.slice_from(u"e"):
                return False
        return True

    def __r_double(self):
        v_1 = self.limit - self.cursor
        if self.find_among_b(HungarianStemmer.a_2) == 0:
            return False
        self.cursor = self.limit - v_1
        return True

    def __r_undouble(self):
        if self.cursor <= self.limit_backward:
            return False
        self.cursor -= 1
        self.ket = self.cursor
        c = self.cursor - 1
        if c < self.limit_backward:
            return False
        self.cursor = c
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def __r_instrum(self):
        self.ket = self.cursor
        if self.find_among_b(HungarianStemmer.a_3) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if not self.__r_double():
            return False
        if not self.slice_del():
            return False

        if not self.__r_undouble():
            return False
        return True

    def __r_case(self):
        self.ket = self.cursor
        if self.find_among_b(HungarianStemmer.a_4) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if not self.slice_del():
            return False

        if not self.__r_v_ending():
            return False
        return True

    def __r_case_special(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_5)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_from(u"e"):
                return False
        else:
            if not self.slice_from(u"a"):
                return False
        return True

    def __r_case_other(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_6)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"a"):
                return False
        else:
            if not self.slice_from(u"e"):
                return False
        return True

    def __r_factive(self):
        self.ket = self.cursor
        if self.find_among_b(HungarianStemmer.a_7) == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if not self.__r_double():
            return False
        if not self.slice_del():
            return False

        if not self.__r_undouble():
            return False
        return True

    def __r_plural(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_8)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_from(u"a"):
                return False
        elif among_var == 2:
            if not self.slice_from(u"e"):
                return False
        else:
            if not self.slice_del():
                return False

        return True

    def __r_owned(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_9)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"e"):
                return False
        else:
            if not self.slice_from(u"a"):
                return False
        return True

    def __r_sing_owner(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_10)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"a"):
                return False
        else:
            if not self.slice_from(u"e"):
                return False
        return True

    def __r_plur_owner(self):
        self.ket = self.cursor
        among_var = self.find_among_b(HungarianStemmer.a_11)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if not self.__r_R1():
            return False
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"a"):
                return False
        else:
            if not self.slice_from(u"e"):
                return False
        return True

    def _stem(self):
        v_1 = self.cursor
        self.__r_mark_regions()
        self.cursor = v_1
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_2 = self.limit - self.cursor
        self.__r_instrum()
        self.cursor = self.limit - v_2
        v_3 = self.limit - self.cursor
        self.__r_case()
        self.cursor = self.limit - v_3
        v_4 = self.limit - self.cursor
        self.__r_case_special()
        self.cursor = self.limit - v_4
        v_5 = self.limit - self.cursor
        self.__r_case_other()
        self.cursor = self.limit - v_5
        v_6 = self.limit - self.cursor
        self.__r_factive()
        self.cursor = self.limit - v_6
        v_7 = self.limit - self.cursor
        self.__r_owned()
        self.cursor = self.limit - v_7
        v_8 = self.limit - self.cursor
        self.__r_sing_owner()
        self.cursor = self.limit - v_8
        v_9 = self.limit - self.cursor
        self.__r_plur_owner()
        self.cursor = self.limit - v_9
        v_10 = self.limit - self.cursor
        self.__r_plural()
        self.cursor = self.limit - v_10
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass
