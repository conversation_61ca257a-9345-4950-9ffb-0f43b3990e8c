# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class IrishStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"b'", -1, 1),
        Among(u"bh", -1, 4),
        Among(u"bhf", 1, 2),
        Among(u"bp", -1, 8),
        Among(u"ch", -1, 5),
        Among(u"d'", -1, 1),
        Among(u"d'fh", 5, 2),
        Among(u"dh", -1, 6),
        Among(u"dt", -1, 9),
        Among(u"fh", -1, 2),
        Among(u"gc", -1, 5),
        Among(u"gh", -1, 7),
        Among(u"h-", -1, 1),
        Among(u"m'", -1, 1),
        Among(u"mb", -1, 4),
        Among(u"mh", -1, 10),
        Among(u"n-", -1, 1),
        Among(u"nd", -1, 6),
        Among(u"ng", -1, 7),
        Among(u"ph", -1, 8),
        Among(u"sh", -1, 3),
        Among(u"t-", -1, 1),
        Among(u"th", -1, 9),
        Among(u"ts", -1, 3)
    ]

    a_1 = [
        Among(u"\u00EDochta", -1, 1),
        Among(u"a\u00EDochta", 0, 1),
        Among(u"ire", -1, 2),
        Among(u"aire", 2, 2),
        Among(u"abh", -1, 1),
        Among(u"eabh", 4, 1),
        Among(u"ibh", -1, 1),
        Among(u"aibh", 6, 1),
        Among(u"amh", -1, 1),
        Among(u"eamh", 8, 1),
        Among(u"imh", -1, 1),
        Among(u"aimh", 10, 1),
        Among(u"\u00EDocht", -1, 1),
        Among(u"a\u00EDocht", 12, 1),
        Among(u"ir\u00ED", -1, 2),
        Among(u"air\u00ED", 14, 2)
    ]

    a_2 = [
        Among(u"\u00F3ideacha", -1, 6),
        Among(u"patacha", -1, 5),
        Among(u"achta", -1, 1),
        Among(u"arcachta", 2, 2),
        Among(u"eachta", 2, 1),
        Among(u"grafa\u00EDochta", -1, 4),
        Among(u"paite", -1, 5),
        Among(u"ach", -1, 1),
        Among(u"each", 7, 1),
        Among(u"\u00F3ideach", 8, 6),
        Among(u"gineach", 8, 3),
        Among(u"patach", 7, 5),
        Among(u"grafa\u00EDoch", -1, 4),
        Among(u"pataigh", -1, 5),
        Among(u"\u00F3idigh", -1, 6),
        Among(u"acht\u00FAil", -1, 1),
        Among(u"eacht\u00FAil", 15, 1),
        Among(u"gineas", -1, 3),
        Among(u"ginis", -1, 3),
        Among(u"acht", -1, 1),
        Among(u"arcacht", 19, 2),
        Among(u"eacht", 19, 1),
        Among(u"grafa\u00EDocht", -1, 4),
        Among(u"arcachta\u00ED", -1, 2),
        Among(u"grafa\u00EDochta\u00ED", -1, 4)
    ]

    a_3 = [
        Among(u"imid", -1, 1),
        Among(u"aimid", 0, 1),
        Among(u"\u00EDmid", -1, 1),
        Among(u"a\u00EDmid", 2, 1),
        Among(u"adh", -1, 2),
        Among(u"eadh", 4, 2),
        Among(u"faidh", -1, 1),
        Among(u"fidh", -1, 1),
        Among(u"\u00E1il", -1, 2),
        Among(u"ain", -1, 2),
        Among(u"tear", -1, 2),
        Among(u"tar", -1, 2)
    ]

    g_v = [17, 65, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17, 4, 2]

    I_p2 = 0
    I_p1 = 0
    I_pV = 0

    def __r_mark_regions(self):
        self.I_pV = self.limit
        self.I_p1 = self.limit
        self.I_p2 = self.limit
        v_1 = self.cursor
        try:
            if not self.go_out_grouping(IrishStemmer.g_v, 97, 250):
                raise lab0()
            self.cursor += 1
            self.I_pV = self.cursor
            if not self.go_in_grouping(IrishStemmer.g_v, 97, 250):
                raise lab0()
            self.cursor += 1
            self.I_p1 = self.cursor
            if not self.go_out_grouping(IrishStemmer.g_v, 97, 250):
                raise lab0()
            self.cursor += 1
            if not self.go_in_grouping(IrishStemmer.g_v, 97, 250):
                raise lab0()
            self.cursor += 1
            self.I_p2 = self.cursor
        except lab0: pass
        self.cursor = v_1
        return True

    def __r_initial_morph(self):
        self.bra = self.cursor
        among_var = self.find_among(IrishStemmer.a_0)
        if among_var == 0:
            return False
        self.ket = self.cursor
        if among_var == 1:
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"f"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"s"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"b"):
                return False
        elif among_var == 5:
            if not self.slice_from(u"c"):
                return False
        elif among_var == 6:
            if not self.slice_from(u"d"):
                return False
        elif among_var == 7:
            if not self.slice_from(u"g"):
                return False
        elif among_var == 8:
            if not self.slice_from(u"p"):
                return False
        elif among_var == 9:
            if not self.slice_from(u"t"):
                return False
        else:
            if not self.slice_from(u"m"):
                return False
        return True

    def __r_RV(self):
        if not self.I_pV <= self.cursor:
            return False
        return True

    def __r_R1(self):
        if not self.I_p1 <= self.cursor:
            return False
        return True

    def __r_R2(self):
        if not self.I_p2 <= self.cursor:
            return False
        return True

    def __r_noun_sfx(self):
        self.ket = self.cursor
        among_var = self.find_among_b(IrishStemmer.a_1)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        else:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        return True

    def __r_deriv(self):
        self.ket = self.cursor
        among_var = self.find_among_b(IrishStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_R2():
                return False
            if not self.slice_del():
                return False

        elif among_var == 2:
            if not self.slice_from(u"arc"):
                return False
        elif among_var == 3:
            if not self.slice_from(u"gin"):
                return False
        elif among_var == 4:
            if not self.slice_from(u"graf"):
                return False
        elif among_var == 5:
            if not self.slice_from(u"paite"):
                return False
        else:
            if not self.slice_from(u"\u00F3id"):
                return False
        return True

    def __r_verb_sfx(self):
        self.ket = self.cursor
        among_var = self.find_among_b(IrishStemmer.a_3)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.__r_RV():
                return False
            if not self.slice_del():
                return False

        else:
            if not self.__r_R1():
                return False
            if not self.slice_del():
                return False

        return True

    def _stem(self):
        v_1 = self.cursor
        self.__r_initial_morph()
        self.cursor = v_1
        self.__r_mark_regions()
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_3 = self.limit - self.cursor
        self.__r_noun_sfx()
        self.cursor = self.limit - v_3
        v_4 = self.limit - self.cursor
        self.__r_deriv()
        self.cursor = self.limit - v_4
        v_5 = self.limit - self.cursor
        self.__r_verb_sfx()
        self.cursor = self.limit - v_5
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass
