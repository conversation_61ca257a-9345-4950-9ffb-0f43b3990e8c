# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class IndonesianStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"kah", -1, 1),
        Among(u"lah", -1, 1),
        Among(u"pun", -1, 1)
    ]

    a_1 = [
        Among(u"nya", -1, 1),
        Among(u"ku", -1, 1),
        Among(u"mu", -1, 1)
    ]

    a_2 = [
        Among(u"i", -1, 1, "_IndonesianStemmer__r_SUFFIX_I_OK"),
        Among(u"an", -1, 1, "_IndonesianStemmer__r_SUFFIX_AN_OK"),
        Among(u"kan", 1, 1, "_IndonesianStemmer__r_SUFFIX_KAN_OK")
    ]

    a_3 = [
        Among(u"di", -1, 1),
        Among(u"ke", -1, 2),
        Among(u"me", -1, 1),
        Among(u"mem", 2, 5),
        Among(u"men", 2, 1),
        Among(u"meng", 4, 1),
        Among(u"meny", 4, 3, "_IndonesianStemmer__r_VOWEL"),
        Among(u"pem", -1, 6),
        Among(u"pen", -1, 2),
        Among(u"peng", 8, 2),
        Among(u"peny", 8, 4, "_IndonesianStemmer__r_VOWEL"),
        Among(u"ter", -1, 1)
    ]

    a_4 = [
        Among(u"be", -1, 3, "_IndonesianStemmer__r_KER"),
        Among(u"belajar", 0, 4),
        Among(u"ber", 0, 3),
        Among(u"pe", -1, 1),
        Among(u"pelajar", 3, 2),
        Among(u"per", 3, 1)
    ]

    g_vowel = [17, 65, 16]

    I_prefix = 0
    I_measure = 0

    def __r_remove_particle(self):
        self.ket = self.cursor
        if self.find_among_b(IndonesianStemmer.a_0) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        self.I_measure -= 1
        return True

    def __r_remove_possessive_pronoun(self):
        self.ket = self.cursor
        if self.find_among_b(IndonesianStemmer.a_1) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        self.I_measure -= 1
        return True

    def __r_SUFFIX_KAN_OK(self):
        if not self.I_prefix != 3:
            return False
        if not self.I_prefix != 2:
            return False
        return True

    def __r_SUFFIX_AN_OK(self):
        if not self.I_prefix != 1:
            return False
        return True

    def __r_SUFFIX_I_OK(self):
        if not self.I_prefix <= 2:
            return False
        v_1 = self.limit - self.cursor
        try:
            if not self.eq_s_b(u"s"):
                raise lab0()
            return False
        except lab0: pass
        self.cursor = self.limit - v_1
        return True

    def __r_remove_suffix(self):
        self.ket = self.cursor
        if self.find_among_b(IndonesianStemmer.a_2) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        self.I_measure -= 1
        return True

    def __r_VOWEL(self):
        if not self.in_grouping(IndonesianStemmer.g_vowel, 97, 117):
            return False
        return True

    def __r_KER(self):
        if not self.out_grouping(IndonesianStemmer.g_vowel, 97, 117):
            return False
        if not self.eq_s(u"er"):
            return False
        return True

    def __r_remove_first_order_prefix(self):
        self.bra = self.cursor
        among_var = self.find_among(IndonesianStemmer.a_3)
        if among_var == 0:
            return False
        self.ket = self.cursor
        if among_var == 1:
            if not self.slice_del():
                return False

            self.I_prefix = 1
            self.I_measure -= 1
        elif among_var == 2:
            if not self.slice_del():
                return False

            self.I_prefix = 3
            self.I_measure -= 1
        elif among_var == 3:
            self.I_prefix = 1
            if not self.slice_from(u"s"):
                return False
            self.I_measure -= 1
        elif among_var == 4:
            self.I_prefix = 3
            if not self.slice_from(u"s"):
                return False
            self.I_measure -= 1
        elif among_var == 5:
            self.I_prefix = 1
            self.I_measure -= 1
            try:
                v_1 = self.cursor
                try:
                    v_2 = self.cursor
                    if not self.in_grouping(IndonesianStemmer.g_vowel, 97, 117):
                        raise lab1()
                    self.cursor = v_2
                    if not self.slice_from(u"p"):
                        return False
                    raise lab0()
                except lab1: pass
                self.cursor = v_1
                if not self.slice_del():
                    return False

            except lab0: pass
        else:
            self.I_prefix = 3
            self.I_measure -= 1
            try:
                v_3 = self.cursor
                try:
                    v_4 = self.cursor
                    if not self.in_grouping(IndonesianStemmer.g_vowel, 97, 117):
                        raise lab3()
                    self.cursor = v_4
                    if not self.slice_from(u"p"):
                        return False
                    raise lab2()
                except lab3: pass
                self.cursor = v_3
                if not self.slice_del():
                    return False

            except lab2: pass
        return True

    def __r_remove_second_order_prefix(self):
        self.bra = self.cursor
        among_var = self.find_among(IndonesianStemmer.a_4)
        if among_var == 0:
            return False
        self.ket = self.cursor
        if among_var == 1:
            if not self.slice_del():
                return False

            self.I_prefix = 2
            self.I_measure -= 1
        elif among_var == 2:
            if not self.slice_from(u"ajar"):
                return False
            self.I_measure -= 1
        elif among_var == 3:
            if not self.slice_del():
                return False

            self.I_prefix = 4
            self.I_measure -= 1
        else:
            if not self.slice_from(u"ajar"):
                return False
            self.I_prefix = 4
            self.I_measure -= 1
        return True

    def _stem(self):
        self.I_measure = 0
        v_1 = self.cursor
        try:
            while True:
                v_2 = self.cursor
                try:
                    if not self.go_out_grouping(IndonesianStemmer.g_vowel, 97, 117):
                        raise lab1()
                    self.cursor += 1
                    self.I_measure += 1
                    continue
                except lab1: pass
                self.cursor = v_2
                break
        except lab0: pass
        self.cursor = v_1
        if not self.I_measure > 2:
            return False
        self.I_prefix = 0
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_3 = self.limit - self.cursor
        self.__r_remove_particle()
        self.cursor = self.limit - v_3
        if not self.I_measure > 2:
            return False
        v_4 = self.limit - self.cursor
        self.__r_remove_possessive_pronoun()
        self.cursor = self.limit - v_4
        self.cursor = self.limit_backward
        if not self.I_measure > 2:
            return False
        try:
            v_5 = self.cursor
            try:
                v_6 = self.cursor
                if not self.__r_remove_first_order_prefix():
                    raise lab3()
                v_7 = self.cursor
                try:
                    v_8 = self.cursor
                    if not self.I_measure > 2:
                        raise lab4()
                    self.limit_backward = self.cursor
                    self.cursor = self.limit
                    if not self.__r_remove_suffix():
                        raise lab4()
                    self.cursor = self.limit_backward
                    self.cursor = v_8
                    if not self.I_measure > 2:
                        raise lab4()
                    if not self.__r_remove_second_order_prefix():
                        raise lab4()
                except lab4: pass
                self.cursor = v_7
                self.cursor = v_6
                raise lab2()
            except lab3: pass
            self.cursor = v_5
            v_9 = self.cursor
            self.__r_remove_second_order_prefix()
            self.cursor = v_9
            v_10 = self.cursor
            try:
                if not self.I_measure > 2:
                    raise lab5()
                self.limit_backward = self.cursor
                self.cursor = self.limit
                if not self.__r_remove_suffix():
                    raise lab5()
                self.cursor = self.limit_backward
            except lab5: pass
            self.cursor = v_10
        except lab2: pass
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass


class lab4(BaseException): pass


class lab5(BaseException): pass
