# Generated by Snowball 2.2.0 - https://snowballstem.org/

from .basestemmer import BaseStemmer
from .among import Among


class NepaliStemmer(BaseStemmer):
    '''
    This class implements the stemming algorithm defined by a snowball script.
    Generated by Snowball 2.2.0 - https://snowballstem.org/
    '''

    a_0 = [
        Among(u"\u0932\u093E\u0907", -1, 1),
        Among(u"\u0932\u093E\u0908", -1, 1),
        Among(u"\u0938\u0901\u0917", -1, 1),
        Among(u"\u0938\u0902\u0917", -1, 1),
        Among(u"\u092E\u093E\u0930\u094D\u092B\u0924", -1, 1),
        Among(u"\u0930\u0924", -1, 1),
        Among(u"\u0915\u093E", -1, 2),
        Among(u"\u092E\u093E", -1, 1),
        Among(u"\u0926\u094D\u0935\u093E\u0930\u093E", -1, 1),
        Among(u"\u0915\u093F", -1, 2),
        Among(u"\u092A\u091B\u093F", -1, 1),
        Among(u"\u0915\u0940", -1, 2),
        Among(u"\u0932\u0947", -1, 1),
        Among(u"\u0915\u0948", -1, 2),
        Among(u"\u0938\u0901\u0917\u0948", -1, 1),
        Among(u"\u092E\u0948", -1, 1),
        Among(u"\u0915\u094B", -1, 2)
    ]

    a_1 = [
        Among(u"\u0901", -1, -1),
        Among(u"\u0902", -1, -1),
        Among(u"\u0948", -1, -1)
    ]

    a_2 = [
        Among(u"\u0901", -1, 1),
        Among(u"\u0902", -1, 1),
        Among(u"\u0948", -1, 2)
    ]

    a_3 = [
        Among(u"\u0925\u093F\u090F", -1, 1),
        Among(u"\u091B", -1, 1),
        Among(u"\u0907\u091B", 1, 1),
        Among(u"\u090F\u091B", 1, 1),
        Among(u"\u093F\u091B", 1, 1),
        Among(u"\u0947\u091B", 1, 1),
        Among(u"\u0928\u0947\u091B", 5, 1),
        Among(u"\u0939\u0941\u0928\u0947\u091B", 6, 1),
        Among(u"\u0907\u0928\u094D\u091B", 1, 1),
        Among(u"\u093F\u0928\u094D\u091B", 1, 1),
        Among(u"\u0939\u0941\u0928\u094D\u091B", 1, 1),
        Among(u"\u090F\u0915\u093E", -1, 1),
        Among(u"\u0907\u090F\u0915\u093E", 11, 1),
        Among(u"\u093F\u090F\u0915\u093E", 11, 1),
        Among(u"\u0947\u0915\u093E", -1, 1),
        Among(u"\u0928\u0947\u0915\u093E", 14, 1),
        Among(u"\u0926\u093E", -1, 1),
        Among(u"\u0907\u0926\u093E", 16, 1),
        Among(u"\u093F\u0926\u093E", 16, 1),
        Among(u"\u0926\u0947\u0916\u093F", -1, 1),
        Among(u"\u092E\u093E\u0925\u093F", -1, 1),
        Among(u"\u090F\u0915\u0940", -1, 1),
        Among(u"\u0907\u090F\u0915\u0940", 21, 1),
        Among(u"\u093F\u090F\u0915\u0940", 21, 1),
        Among(u"\u0947\u0915\u0940", -1, 1),
        Among(u"\u0926\u0947\u0916\u0940", -1, 1),
        Among(u"\u0925\u0940", -1, 1),
        Among(u"\u0926\u0940", -1, 1),
        Among(u"\u091B\u0941", -1, 1),
        Among(u"\u090F\u091B\u0941", 28, 1),
        Among(u"\u0947\u091B\u0941", 28, 1),
        Among(u"\u0928\u0947\u091B\u0941", 30, 1),
        Among(u"\u0928\u0941", -1, 1),
        Among(u"\u0939\u0930\u0941", -1, 1),
        Among(u"\u0939\u0930\u0942", -1, 1),
        Among(u"\u091B\u0947", -1, 1),
        Among(u"\u0925\u0947", -1, 1),
        Among(u"\u0928\u0947", -1, 1),
        Among(u"\u090F\u0915\u0948", -1, 1),
        Among(u"\u0947\u0915\u0948", -1, 1),
        Among(u"\u0928\u0947\u0915\u0948", 39, 1),
        Among(u"\u0926\u0948", -1, 1),
        Among(u"\u0907\u0926\u0948", 41, 1),
        Among(u"\u093F\u0926\u0948", 41, 1),
        Among(u"\u090F\u0915\u094B", -1, 1),
        Among(u"\u0907\u090F\u0915\u094B", 44, 1),
        Among(u"\u093F\u090F\u0915\u094B", 44, 1),
        Among(u"\u0947\u0915\u094B", -1, 1),
        Among(u"\u0928\u0947\u0915\u094B", 47, 1),
        Among(u"\u0926\u094B", -1, 1),
        Among(u"\u0907\u0926\u094B", 49, 1),
        Among(u"\u093F\u0926\u094B", 49, 1),
        Among(u"\u092F\u094B", -1, 1),
        Among(u"\u0907\u092F\u094B", 52, 1),
        Among(u"\u092D\u092F\u094B", 52, 1),
        Among(u"\u093F\u092F\u094B", 52, 1),
        Among(u"\u0925\u093F\u092F\u094B", 55, 1),
        Among(u"\u0926\u093F\u092F\u094B", 55, 1),
        Among(u"\u0925\u094D\u092F\u094B", 52, 1),
        Among(u"\u091B\u094C", -1, 1),
        Among(u"\u0907\u091B\u094C", 59, 1),
        Among(u"\u090F\u091B\u094C", 59, 1),
        Among(u"\u093F\u091B\u094C", 59, 1),
        Among(u"\u0947\u091B\u094C", 59, 1),
        Among(u"\u0928\u0947\u091B\u094C", 63, 1),
        Among(u"\u092F\u094C", -1, 1),
        Among(u"\u0925\u093F\u092F\u094C", 65, 1),
        Among(u"\u091B\u094D\u092F\u094C", 65, 1),
        Among(u"\u0925\u094D\u092F\u094C", 65, 1),
        Among(u"\u091B\u0928\u094D", -1, 1),
        Among(u"\u0907\u091B\u0928\u094D", 69, 1),
        Among(u"\u090F\u091B\u0928\u094D", 69, 1),
        Among(u"\u093F\u091B\u0928\u094D", 69, 1),
        Among(u"\u0947\u091B\u0928\u094D", 69, 1),
        Among(u"\u0928\u0947\u091B\u0928\u094D", 73, 1),
        Among(u"\u0932\u093E\u0928\u094D", -1, 1),
        Among(u"\u091B\u093F\u0928\u094D", -1, 1),
        Among(u"\u0925\u093F\u0928\u094D", -1, 1),
        Among(u"\u092A\u0930\u094D", -1, 1),
        Among(u"\u0907\u0938\u094D", -1, 1),
        Among(u"\u0925\u093F\u0907\u0938\u094D", 79, 1),
        Among(u"\u091B\u0938\u094D", -1, 1),
        Among(u"\u0907\u091B\u0938\u094D", 81, 1),
        Among(u"\u090F\u091B\u0938\u094D", 81, 1),
        Among(u"\u093F\u091B\u0938\u094D", 81, 1),
        Among(u"\u0947\u091B\u0938\u094D", 81, 1),
        Among(u"\u0928\u0947\u091B\u0938\u094D", 85, 1),
        Among(u"\u093F\u0938\u094D", -1, 1),
        Among(u"\u0925\u093F\u0938\u094D", 87, 1),
        Among(u"\u091B\u0947\u0938\u094D", -1, 1),
        Among(u"\u0939\u094B\u0938\u094D", -1, 1)
    ]


    def __r_remove_category_1(self):
        self.ket = self.cursor
        among_var = self.find_among_b(NepaliStemmer.a_0)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            if not self.slice_del():
                return False

        else:
            try:
                v_1 = self.limit - self.cursor
                try:
                    try:
                        v_2 = self.limit - self.cursor
                        try:
                            if not self.eq_s_b(u"\u090F"):
                                raise lab3()
                            raise lab2()
                        except lab3: pass
                        self.cursor = self.limit - v_2
                        if not self.eq_s_b(u"\u0947"):
                            raise lab1()
                    except lab2: pass
                    raise lab0()
                except lab1: pass
                self.cursor = self.limit - v_1
                if not self.slice_del():
                    return False

            except lab0: pass
        return True

    def __r_check_category_2(self):
        self.ket = self.cursor
        if self.find_among_b(NepaliStemmer.a_1) == 0:
            return False
        self.bra = self.cursor
        return True

    def __r_remove_category_2(self):
        self.ket = self.cursor
        among_var = self.find_among_b(NepaliStemmer.a_2)
        if among_var == 0:
            return False
        self.bra = self.cursor
        if among_var == 1:
            try:
                v_1 = self.limit - self.cursor
                try:
                    if not self.eq_s_b(u"\u092F\u094C"):
                        raise lab1()
                    raise lab0()
                except lab1: pass
                self.cursor = self.limit - v_1
                try:
                    if not self.eq_s_b(u"\u091B\u094C"):
                        raise lab2()
                    raise lab0()
                except lab2: pass
                self.cursor = self.limit - v_1
                try:
                    if not self.eq_s_b(u"\u0928\u094C"):
                        raise lab3()
                    raise lab0()
                except lab3: pass
                self.cursor = self.limit - v_1
                if not self.eq_s_b(u"\u0925\u0947"):
                    return False
            except lab0: pass
            if not self.slice_del():
                return False

        else:
            if not self.eq_s_b(u"\u0924\u094D\u0930"):
                return False
            if not self.slice_del():
                return False

        return True

    def __r_remove_category_3(self):
        self.ket = self.cursor
        if self.find_among_b(NepaliStemmer.a_3) == 0:
            return False
        self.bra = self.cursor
        if not self.slice_del():
            return False

        return True

    def _stem(self):
        self.limit_backward = self.cursor
        self.cursor = self.limit
        v_1 = self.limit - self.cursor
        self.__r_remove_category_1()
        self.cursor = self.limit - v_1
        v_2 = self.limit - self.cursor
        try:
            while True:
                v_3 = self.limit - self.cursor
                try:
                    v_4 = self.limit - self.cursor
                    try:
                        v_5 = self.limit - self.cursor
                        if not self.__r_check_category_2():
                            raise lab2()
                        self.cursor = self.limit - v_5
                        if not self.__r_remove_category_2():
                            raise lab2()
                    except lab2: pass
                    self.cursor = self.limit - v_4
                    if not self.__r_remove_category_3():
                        raise lab1()
                    continue
                except lab1: pass
                self.cursor = self.limit - v_3
                break
        except lab0: pass
        self.cursor = self.limit - v_2
        self.cursor = self.limit_backward
        return True


class lab0(BaseException): pass


class lab1(BaseException): pass


class lab2(BaseException): pass


class lab3(BaseException): pass
