Metadata-Version: 2.3
Name: opentelemetry-instrumentation-redis
Version: 0.50b0
Summary: OpenTelemetry Redis instrumentation
Project-URL: Homepage, https://github.com/open-telemetry/opentelemetry-python-contrib/tree/main/instrumentation/opentelemetry-instrumentation-redis
Author-email: OpenTelemetry Authors <<EMAIL>>
License: Apache-2.0
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.8
Requires-Dist: opentelemetry-api~=1.12
Requires-Dist: opentelemetry-instrumentation==0.50b0
Requires-Dist: opentelemetry-semantic-conventions==0.50b0
Requires-Dist: wrapt>=1.12.1
Provides-Extra: instruments
Requires-Dist: redis>=2.6; extra == 'instruments'
Description-Content-Type: text/x-rst

OpenTelemetry Redis Instrumentation
===================================

|pypi|

.. |pypi| image:: https://badge.fury.io/py/opentelemetry-instrumentation-redis.svg
   :target: https://pypi.org/project/opentelemetry-instrumentation-redis/

This library allows tracing requests made by the Redis library.

Installation
------------

::

    pip install opentelemetry-instrumentation-redis

References
----------

* `OpenTelemetry Redis Instrumentation <https://opentelemetry-python-contrib.readthedocs.io/en/latest/instrumentation/redis/redis.html>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
* `OpenTelemetry Python Examples <https://github.com/open-telemetry/opentelemetry-python/tree/main/docs/examples>`_
