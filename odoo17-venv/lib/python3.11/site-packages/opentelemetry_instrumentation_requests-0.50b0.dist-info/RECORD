opentelemetry/instrumentation/requests/__init__.py,sha256=GGN5ykITzJitPTZJX76PjBUvLxNWXjUx9Ap7UNaV7mM,16623
opentelemetry/instrumentation/requests/__pycache__/__init__.cpython-311.pyc,,
opentelemetry/instrumentation/requests/__pycache__/package.cpython-311.pyc,,
opentelemetry/instrumentation/requests/__pycache__/version.cpython-311.pyc,,
opentelemetry/instrumentation/requests/package.py,sha256=84lK70NyCoRefASXKjU5f4byJhf5qWDL6IdYjch-UTM,679
opentelemetry/instrumentation/requests/version.py,sha256=sBsbV8VQNTte60quPySrr3hMzVnRCO8AnGlwnGPP60o,608
opentelemetry_instrumentation_requests-0.50b0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
opentelemetry_instrumentation_requests-0.50b0.dist-info/METADATA,sha256=R8x_4qInbkbmcBIY0q3RamYqJXs7YHvacnIMqUvfVYI,2498
opentelemetry_instrumentation_requests-0.50b0.dist-info/RECORD,,
opentelemetry_instrumentation_requests-0.50b0.dist-info/WHEEL,sha256=C2FUgwZgiLbznR-k0b_5k3Ai_1aASOXDss3lzCUsUug,87
opentelemetry_instrumentation_requests-0.50b0.dist-info/entry_points.txt,sha256=w_cFVp9h9IXzWm2YeBaOEUANSn9iuUlNAl0QC3PDf94,100
opentelemetry_instrumentation_requests-0.50b0.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
