Metadata-Version: 2.0
Name: xlwt
Version: 1.3.0
Summary: Library to create spreadsheet files compatible with MS Excel 97/2000/XP/2003 XLS files, on any platform, with Python 2.6, 2.7, 3.3+
Home-page: http://www.python-excel.org/
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Download-URL: https://pypi.python.org/pypi/xlwt
Keywords: xls excel spreadsheet workbook worksheet pyExcelerator
Platform: Platform Independent
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: License :: OSI Approved :: BSD License
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: Office/Business :: Financial :: Spreadsheet
Classifier: Topic :: Database
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content :: CGI Tools/Libraries
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6

|Travis|_ |Coveralls|_ |Docs|_ |PyPI|_

.. |Travis| image:: https://api.travis-ci.org/python-excel/xlwt.svg?branch=master
.. _Travis: https://travis-ci.org/python-excel/xlwt

.. |Coveralls| image:: https://coveralls.io/repos/python-excel/xlwt/badge.svg?branch=master
.. _Coveralls: https://coveralls.io/r/python-excel/xlwt?branch=master

.. |Docs| image:: https://readthedocs.org/projects/xlwt/badge/?version=latest
.. _Docs: https://xlwt.readthedocs.org/en/latest/

.. |PyPI| image:: https://badge.fury.io/py/xlwt.svg
.. _PyPI: https://badge.fury.io/py/xlwt

xlwt
====

This is a library for developers to use to generate
spreadsheet files compatible with Microsoft Excel versions 95 to 2003.

The package itself is pure Python with no dependencies on modules or packages
outside the standard Python distribution.

Please read this before using this package:
https://groups.google.com/d/msg/python-excel/P6TjJgFVjMI/g8d0eWxTBQAJ

Installation
============

Do the following in your virtualenv::

  pip install xlwt

Quick start
===========

.. code-block:: python

    import xlwt
    from datetime import datetime

    style0 = xlwt.easyxf('font: name Times New Roman, color-index red, bold on',
        num_format_str='#,##0.00')
    style1 = xlwt.easyxf(num_format_str='D-MMM-YY')

    wb = xlwt.Workbook()
    ws = wb.add_sheet('A Test Sheet')

    ws.write(0, 0, 1234.56, style0)
    ws.write(1, 0, datetime.now(), style1)
    ws.write(2, 0, 1)
    ws.write(2, 1, 1)
    ws.write(2, 2, xlwt.Formula("A3+B3"))

    wb.save('example.xls')


Documentation
=============

Documentation can be found in the ``docs`` directory of the xlwt package.
If these aren't sufficient, please consult the code in the
examples directory and the source code itself.

The latest documentation can also be found at:
https://xlwt.readthedocs.org/en/latest/

Problems?
=========
Try the following in this order:

- Read the source

- Ask a question on https://groups.google.com/group/python-excel/

Acknowledgements
================

xlwt is a fork of the pyExcelerator package, which was developed by
Roman V. Kiseliov. This product includes software developed by
Roman V. Kiseliov <<EMAIL>>.

xlwt uses ANTLR v 2.7.7 to generate its formula compiler.


