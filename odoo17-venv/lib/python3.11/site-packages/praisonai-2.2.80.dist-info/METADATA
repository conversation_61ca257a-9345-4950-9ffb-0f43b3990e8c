Metadata-Version: 2.3
Name: PraisonAI
Version: 2.2.80
Summary: PraisonAI is an AI Agents Framework with Self Reflection. PraisonAI application combines PraisonAI Agents, AutoGen, and CrewAI into a low-code solution for building and managing multi-agent LLM systems, focusing on simplicity, customisation, and efficient human-agent collaboration.
Author: <PERSON><PERSON>son
Requires-Python: >=3.10
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Provides-Extra: agentops
Provides-Extra: anthropic
Provides-Extra: api
Provides-Extra: autogen
Provides-Extra: autogen-v4
Provides-Extra: call
Provides-Extra: chat
Provides-Extra: code
Provides-Extra: cohere
Provides-Extra: crewai
Provides-Extra: google
Provides-Extra: gradio
Provides-Extra: openai
Provides-Extra: realtime
Provides-Extra: train
Provides-Extra: ui
Requires-Dist: PyYAML (>=6.0)
Requires-Dist: agentops (>=0.3.12) ; extra == "agentops"
Requires-Dist: aiosqlite (>=0.20.0) ; extra == "chat"
Requires-Dist: aiosqlite (>=0.20.0) ; extra == "code"
Requires-Dist: aiosqlite (>=0.20.0) ; extra == "realtime"
Requires-Dist: aiosqlite (>=0.20.0) ; extra == "ui"
Requires-Dist: autogen-agentchat (>=0.4.0) ; extra == "autogen-v4"
Requires-Dist: autogen-core (>=0.4.0) ; extra == "autogen-v4"
Requires-Dist: autogen-ext[openai] (>=0.4.0) ; extra == "autogen-v4"
Requires-Dist: chainlit (==2.5.5) ; extra == "chat"
Requires-Dist: chainlit (==2.5.5) ; extra == "code"
Requires-Dist: chainlit (==2.5.5) ; extra == "realtime"
Requires-Dist: chainlit (==2.5.5) ; extra == "ui"
Requires-Dist: crawl4ai (>=0.7.0) ; extra == "chat"
Requires-Dist: crawl4ai (>=0.7.0) ; extra == "code"
Requires-Dist: crawl4ai (>=0.7.0) ; extra == "realtime"
Requires-Dist: crewai (>=0.148.0) ; extra == "crewai"
Requires-Dist: crewai ; extra == "autogen"
Requires-Dist: crewai ; extra == "autogen-v4"
Requires-Dist: duckduckgo_search (>=6.3.0) ; extra == "realtime"
Requires-Dist: fastapi (>=0.115.0) ; extra == "api"
Requires-Dist: fastapi (>=0.95.0) ; extra == "call"
Requires-Dist: flaml[automl] (>=2.3.1) ; extra == "call"
Requires-Dist: flask (>=3.0.0) ; extra == "api"
Requires-Dist: gradio (>=4.26.0) ; extra == "gradio"
Requires-Dist: greenlet (>=3.0.3) ; extra == "chat"
Requires-Dist: greenlet (>=3.0.3) ; extra == "code"
Requires-Dist: greenlet (>=3.0.3) ; extra == "realtime"
Requires-Dist: greenlet (>=3.0.3) ; extra == "ui"
Requires-Dist: instructor (>=1.3.3)
Requires-Dist: langchain-anthropic (>=0.3.0) ; extra == "anthropic"
Requires-Dist: langchain-cohere (>=0.3.0,<0.4.0) ; extra == "cohere"
Requires-Dist: langchain-google-genai (>=2.1.0) ; extra == "google"
Requires-Dist: langchain-openai (>=0.2.1,<0.3.0) ; extra == "openai"
Requires-Dist: litellm (>=1.72.6) ; extra == "chat"
Requires-Dist: litellm (>=1.72.6) ; extra == "code"
Requires-Dist: litellm (>=1.72.6) ; extra == "realtime"
Requires-Dist: markdown (>=3.5)
Requires-Dist: mcp (>=1.6.0)
Requires-Dist: openai (>=1.54.0) ; extra == "call"
Requires-Dist: playwright (>=1.47.0) ; extra == "chat"
Requires-Dist: playwright (>=1.47.0) ; extra == "code"
Requires-Dist: plotly (>=5.24.0) ; extra == "realtime"
Requires-Dist: praisonai-tools (>=0.0.22) ; extra == "autogen"
Requires-Dist: praisonai-tools (>=0.0.22) ; extra == "autogen-v4"
Requires-Dist: praisonai-tools (>=0.0.22) ; extra == "crewai"
Requires-Dist: praisonaiagents (>=0.0.154)
Requires-Dist: pyautogen (==0.2.29) ; extra == "autogen"
Requires-Dist: pydantic (<=2.10.1) ; extra == "chat"
Requires-Dist: pydantic (<=2.10.1) ; extra == "code"
Requires-Dist: pydantic (<=2.10.1) ; extra == "ui"
Requires-Dist: pyngrok (>=1.4.0) ; extra == "call"
Requires-Dist: pyparsing (>=3.0.0)
Requires-Dist: python-dotenv (>=0.19.0)
Requires-Dist: rich (>=13.7)
Requires-Dist: rich ; extra == "call"
Requires-Dist: rich ; extra == "chat"
Requires-Dist: sqlalchemy (>=2.0.36) ; extra == "chat"
Requires-Dist: sqlalchemy (>=2.0.36) ; extra == "code"
Requires-Dist: sqlalchemy (>=2.0.36) ; extra == "realtime"
Requires-Dist: sqlalchemy (>=2.0.36) ; extra == "ui"
Requires-Dist: tavily-python (==0.5.0) ; extra == "chat"
Requires-Dist: tavily-python (==0.5.0) ; extra == "code"
Requires-Dist: tavily-python (==0.5.0) ; extra == "realtime"
Requires-Dist: twilio (>=7.0.0) ; extra == "call"
Requires-Dist: uvicorn (>=0.20.0) ; extra == "call"
Requires-Dist: uvicorn (>=0.34.0) ; extra == "api"
Requires-Dist: websockets (>=12.0) ; extra == "call"
Requires-Dist: websockets (>=12.0) ; extra == "realtime"
Requires-Dist: yfinance (>=0.2.44) ; extra == "realtime"
Project-URL: Homepage, https://docs.praison.ai
Project-URL: Repository, https://github.com/mervinpraison/PraisonAI
Description-Content-Type: text/markdown

# PraisonAI Package

This is the PraisonAI package, which serves as a wrapper for PraisonAIAgents.

It provides a simple and intuitive interface for working with AI agents and their capabilities.

## Directory Structure

The main package code is located in the `praisonai` subdirectory.

