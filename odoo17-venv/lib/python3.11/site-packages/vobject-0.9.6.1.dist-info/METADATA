Metadata-Version: 2.1
Name: vobject
Version: 0.9.6.1
Summary: A full-featured Python package for parsing and creating iCalendar and vCard files
Home-page: http://eventable.github.io/vobject/
Download-URL: https://github.com/eventable/vobject/tarball/0.9.6.1
Author: <PERSON>
Author-email: jef<PERSON>@osafoundation.org
Maintainer: <PERSON><PERSON>
Maintainer-email: <EMAIL>
License: Apache
Keywords: vobject,icalendar,vcard,ics,vcs,hcalendar
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Text Processing
License-File: LICENSE-2.0.txt
Requires-Dist: python-dateutil>=2.4.0


Description
-----------

Parses iCalendar and vCard files into Python data structures, decoding the
relevant encodings. Also serializes vobject data structures to iCalendar, vCard,
or (experimentally) hCalendar unicode strings.

Requirements
------------

Requires python 2.7 or later, dateutil 2.4.0 or later.

Recent changes
--------------
    - Revert too-strict serialization of timestamp values - broke too many other
       implementations

For older changes, see
   - http://eventable.github.io/vobject/#release-history or
   - http://vobject.skyhouseconsulting.com/history.html
