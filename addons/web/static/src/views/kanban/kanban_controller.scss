// ------- Kanban View -------
.o_kanban_view {
    @include media-breakpoint-down(md) {
        --ControlPanel-border-bottom: none;
    }
}

// ------- Kanban renderer -------
.o_kanban_renderer {
    // ----------------------------------------------------------------------------
    // Default KanbanView values

    --Kanban-background: #{$o-kanban-background};
    --Kanban-gap: unset;
    --Kanban-padding: 0;

    --KanbanGroup-background: var(--Kanban-background);
    --KanbanGroup-padding-h: #{$o-kanban-group-padding};
    --KanbanGroup-padding-bottom: #{$o-kanban-group-padding};

    --KanbanRecord-width: #{$o-kanban-default-record-width};
    --KanbanRecord-margin-v: #{$o-kanban-record-margin};
    --KanbanRecord-margin-h: #{$o-kanban-record-margin};
    --KanbanRecord-padding-v: #{$o-kanban-inside-vgutter};
    --KanbanRecord-padding-h: #{$o-kanban-inside-hgutter};
    --KanbanRecord-gap-v: #{$o-kanban-inner-hmargin};

    --KanbanRecord--small-width: #{$o-kanban-small-record-width};

    --KanbanRecord__image-width: #{$o-kanban-image-width};
    --KanbanRecord__image--fill-width: #{$o-kanban-image-fill-width};

    --KanbanRecord__dropdown-gap: #{$border-width};

    --KanbanColumn__highlight-background: #{tint-color($o-action, 90%)};
    --KanbanColumn__highlight-border: #{$o-action};

    --o-view-nocontent-zindex: 1;

    // ----------------------------------------------------------------------------
    // Inner components contextual adaptations

    --Ribbon-font-size: #{$font-size-sm};
    --Ribbon-gap-top: calc(var(--KanbanRecord-padding-v) * -1 - #{$border-width});
    --Ribbon-gap-right: 0;
    --Ribbon-wrapper-width: 6.5rem;
    --Ribbon-height:  calc(var(--Ribbon-font-size) * 2);

    // ----------------------------------------------------------------------------

    border-top: $border-width solid var(--Kanban-background);
    background: var(--Kanban-background);
    padding: var(--Kanban-padding);
    gap: var(--Kanban-gap);

    @include media-breakpoint-down(md) {
        --KanbanRecord-padding-h: #{$o-kanban-inside-hgutter * 2};
    }

    .o_kanban_record > div:not(.o_dropdown_kanban),
    .o_kanban_quick_create {
        border: $border-width solid $border-color;
        background-color: $o-view-background-color;
    }

    .o_kanban_quick_create {
        margin-bottom: calc(var(--KanbanRecord-margin-h) * 2);
        margin-top: calc(var(--KanbanRecord-margin-h) * 2);
        padding: 8px;

        .o_form_view .o_inner_group {
            margin: 0;
        }
    }

    .o_kanban_record {
        position: relative;
        min-width: 150px;
        margin: 0 0 (-$border-width);

        > div:not(.o_dropdown_kanban) {
            padding: var(--KanbanRecord-padding-v) var(--KanbanRecord-padding-h);
            width: 100%;
            height: 100%;
        }

        // ------- Kanban Record, v11 Layout -------
        // Records colours
        > div::after {
            content: "";
            @include o-position-absolute(0, auto, 0, 0);
            width: $o-kanban-color-border-width;
        }

        // Inner Sections
        .o_kanban_record_top,
        .o_kanban_record_body {
            margin-bottom: var(--KanbanRecord-gap-v);
        }

        .o_kanban_record_top,
        .o_kanban_record_bottom {
            display: flex;
        }

        .o_kanban_record_top {
            align-items: flex-start;

            .o_dropdown_kanban {
                // For v11 layout, reset positioning to default to properly use
                // flex-box
                position: relative;
                top: auto;
                right: auto;
            }

            .o_kanban_record_headings {
                line-height: 1.2;
                flex: 1 1 auto;
                // Ensure long word doesn't break out of container
                word-wrap: break-word;
                overflow: hidden;
            }
            
            .o_field_priority {
                margin: auto;
            }
        }

        .o_kanban_record_title {
            @include o-kanban-record-title();
            overflow-wrap: break-word;
            word-wrap: break-word;
        }

        .o_kanban_record_subtitle {
            display: block;
            margin-top: calc(var(--KanbanRecord-gap-v) * 0.5);

            i.fa[role="img"] {
                margin-right: 2px;
            }
        }

        .o_kanban_record_bottom {

            .oe_kanban_bottom_left,
            .oe_kanban_bottom_right {
                display: flex;
                align-items: center;
                min-height: 20px;
                gap: map-get($map: $spacers, $key: 1);
            }
            .oe_kanban_bottom_left {
                flex: 1 1 auto;

                > * {
                    margin-right: 6px;
                    line-height: 1;
                }

                .o_priority_star {
                    margin-top: 1px;
                    font-size: 18px;
                }

                span {
                    overflow-wrap: anywhere;
                }
            }
            .oe_kanban_bottom_right {
                flex: 0 1 auto;
            }
            .o_link_muted {
                color: $body-color;
                &:hover {
                    color: map-get($theme-colors, "primary");
                    text-decoration: underline;
                }
            }
        }

        // ---------- Kanban Record, fill image design ----------
        // Records with images that compensate record's padding
        // filling all the available space (eg. hr, partners.. )
        .o_kanban_record_has_image_fill {
            display: flex;

            .o_kanban_image_fill_left {
                position: relative;
                margin-right: var(--KanbanRecord-padding-h);
                @include media-breakpoint-up(sm) {
                    margin: {
                        top: calc(var(--KanbanRecord-padding-v) * -1);
                        bottom: calc(var(--KanbanRecord-padding-v) * -1);
                        left: calc(var(--KanbanRecord-padding-h) * -1);
                    }
                }
                flex: 1 0 var(--KanbanRecord__image--fill-width);
                min-height: 95px;
                background: {
                    size: cover;
                    position: center;
                    repeat: no-repeat;
                }
                > img:not(.o_kanban_image_inner_pic) {
                    object-fit: cover;
                    object-position: center;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }

                &:not(.o_kanban_image_full) {
                    max-height: 140px;
                    height: calc(100% + var(--KanbanRecord-padding-v) * 2);
                    align-self: center;
                }
                &.o_kanban_image_full {
                    background-size: contain;
                    > img {
                        object-fit: contain;
                    }
                }
            }

            // Adapt default 'o_kanban_image' element if present.
            // This adaptation allow to use both images type.
            // Eg. In partners list we use to fill user picture only, keeping the
            // default design for company logos.
            .o_kanban_image {
                position: relative;
                margin-right: var(--KanbanRecord-padding-h);
                flex: 0 0 var(--KanbanRecord__image-width);
                min-height: var(--KanbanRecord__image-width);
                align-self: center;
                background: {
                    size: cover;
                    repeat: no-repeat;
                    position: center;
                }
                > img:not(.o_kanban_image_inner_pic) {
                    object-fit: cover;
                    object-position: center;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }

                @include media-breakpoint-down(md) {
                    flex-basis: var(--KanbanRecord__image--fill-width);
                    min-height: var(--KanbanRecord__image--fill-width);
                }

                // Reset immedialy after div padding
                + div {
                    padding-left: 0;
                }
            }

            // Images (backgrounds) could accomodate another image inside.
            // (eg. Company logo badge inside a contact picture)
            .o_kanban_image_fill_left,
            .o_kanban_image {
                background-color: var(--KanbanRecord__image-bg-color, none);

                .o_kanban_image_inner_pic {
                    @include o-position-absolute($right: 0, $bottom: 0);
                    max: {
                        height: 25px;
                        width: 80%;
                    }
                    background: white;
                    box-shadow: -1px -1px 0 1px white;
                }
            }
        }
    }

    .o_dragged {
        transform: rotate(-3deg);
        transition: transform 0.6s, box-shadow 0.3s;

        .dropdown {
            display: none;
        }
    }

    // -------  Compatibility of old (<= v10) Generic layouts -------

    // Kanban Records - Uniform Design
    // Provide a basic style for different kanban record layouts
    // ---------------------------------------------------------
    .oe_kanban_card,
    .o_kanban_record {
        // -------  v11 Layout + generic layouts (~v10) -------
        // Kanban Record - Dropdown
        .o_dropdown_kanban {
            visibility: hidden;
            @include media-breakpoint-down(md) {
                visibility: visible;
            }

            .dropdown-toggle {
                $padding-base: var(--KanbanRecord-padding-h);
                margin: 1px 1px 0 0;
                padding: if(type-of($padding-base) == 'string', calc(#{$padding-base}/2) $padding-base, $padding-base/2 $padding-base);
                vertical-align: top;
                @include o-hover-text-color($body-color, $headings-color);
            }
            &.show .dropdown-toggle {
                position: relative;
                z-index: $zindex-dropdown + 1;
            }
            .dropdown-menu {
                margin-top: -1px;
                margin-bottom: 0px;
                min-width: 9rem;
            }
        }

        &:hover .o_dropdown_kanban,
        .o_dropdown_kanban.show {
            visibility: visible;
        }

        // Kanban Record - Dropdown colorpicker
        .oe_kanban_colorpicker {
            @include o-kanban-colorpicker;
        }

        // Kanban Record - Inner elements
        .o_field_many2many_tags {
            display: block;
            margin-bottom: var(--KanbanRecord-gap-v);
            word-break: break-all;

            &.avatar {
                margin: 0 0 0 6px;
                .o_m2m_avatar_empty > span {
                    display: block;
                    margin-top: 3px;
                }
            }

            .o_tag {
                --Tag-max-width: MAX(200px, 100%);
                --Tag-font-size: #{$o-font-size-base-smaller};
            }
        }

        .o_field_many2one_avatar {
            img.o_m2o_avatar {
                margin-right: 0;
            }
        }

        // Commonly used to place an image beside the text
        // (e.g. Fleet, Employees, ...)
        .o_kanban_image {
            position: relative;
            text-align: center;

            img {
                max-width: 100%;
            }
        }

        .o_kanban_button {
            margin-top: 15px;

            > button,
            > a {
                @include o-position-absolute(
                    $right: var(--KanbanRecord-padding-h),
                    $bottom: var(--KanbanRecord-padding-v)
                );
            }
        }

        @mixin global-click-selector {
            &.oe_kanban_global_click,
            &.oe_kanban_global_click_edit,
            .oe_kanban_global_click,
            .oe_kanban_global_click_edit {
                @content;
            }
        }

        @include global-click-selector {
            cursor: pointer;
        }

        &:focus-visible,
        &:focus-within {
            z-index: 2;

            @include global-click-selector {
                outline: thin solid mix($o-main-link-color, map-get($grays, "300"));
                outline-offset: -1px;

                &:after {
                    transform: translateX($border-width);
                }
            }
        }

        .o_attachment_image > img {
            width: 100%;
            height: auto;
        }

        .o_progressbar {
            height: $o-kanban-progressbar-height;

            .o_progressbar_title {
                flex: 0 0 auto;
            }
        }

        .o_kanban_image {
            float: left;
            width: var(--KanbanRecord__image-width);

            + div {
                padding-left: calc(
                    var(--KanbanRecord__image-width) + var(--KanbanRecord-padding-h)
                );
            }
        }

        .oe_kanban_details {
            width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
            // Useful for the class 'o_text_overflow'
            min-width: 0;

            ul {
                margin-bottom: calc(var(--KanbanRecord-gap-v) * 0.5);
                padding-left: 0;
                list-style: none;
                font-size: $font-size-sm;

                li {
                    margin-bottom: 2px;
                }
            }
        }

        .o_kanban_footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            > * {
                flex: 0 0 auto;
            }
        }

        .oe_kanban_text_red {
            color: o-text-color("danger");
            font-weight: $font-weight-bold;
        }

        .o_text_bold {
            font-weight: $font-weight-bold;
        }

        .o_text_block {
            display: block;
        }
    }

    // Kanban Grouped Layout
    &.o_kanban_grouped {
        --KanbanGroup-width: calc(var(--KanbanRecord--small-width) + (2 * var(--KanbanGroup-padding-h)));

        min-height: 100%;

        @include media-breakpoint-down(md) {
            --KanbanGroup-width: 90%; // don't take full width to give a hint of next/previous column on smaller screens

            min-height: initial;
            height: 100%;
            overflow: scroll hidden !important;
            scroll-snap-type: x mandatory;
        }

        @include media-breakpoint-up(lg) {
            --KanbanGroup-width: calc(var(--KanbanRecord-width) + (2 * var(--KanbanGroup-padding-h)));
        }

        .o_kanban_record {
            width: 100%;
        }

        .o_kanban_group {
            background: var(--KanbanGroup-background);
        }

        .o_kanban_group,
        .o_column_quick_create {
            flex-basis: var(--KanbanGroup-width);

            @include media-breakpoint-up(md) {
                max-width: var(--KanbanGroup-width);
            }
        }
    }

    // Kanban Grouped Layout - Column default
    .o_kanban_group {
        .o_kanban_header > .o_kanban_header_title {
            &:hover .o_kanban_config,
            .o_kanban_config.show {
                visibility: visible;
            }

            .o_kanban_config {
                visibility: hidden;
                @include media-breakpoint-down(md) {
                    visibility: visible;
                }

                > .dropdown-menu {
                    cursor: default;
                }
            }
        }
        &.o_kanban_hover {
            background-color: var(--KanbanColumn__highlight-background) !important;
            box-shadow: -1px 0px 0px 0px var(--KanbanColumn__highlight-border) inset,
                1px 0px 0px 0px var(--KanbanColumn__highlight-border) inset;

            .o_kanban_header {
                // hack to display correct background color
                margin: 0 calc(-1 * var(--KanbanGroup-padding-h) + 1px);
                padding: 0 calc(var(--KanbanGroup-padding-h) - 1px);
            }
        }
    }

    .o_kanban_group, .o_column_quick_create {
        padding: 0 var(--KanbanGroup-padding-h) var(--KanbanGroup-padding-bottom);
    }

    .o_kanban_group:not(.o_column_folded) .o_kanban_header, .o_quick_create_folded {
        background: inherit;
    }

    .o_kanban_group:not(.o_column_folded) .o_kanban_header {
        .dropdown-item {
            max-width: calc(var(--KanbanRecord-width) * 0.75);
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .o_quick_create_folded {
        top: map-get($spacers, 3);
    }

    .o_column_quick_create,
    .o_kanban_group:not(.o_column_folded) .o_kanban_header_title {
        // Makes them come on top of the "no-content" background gradient.
        z-index: calc(var(--o-view-nocontent-zindex) + 1);
    }

    .o_group_draggable .o_column_title {
        cursor: grab;

        &:active {
            cursor: grabbing;
        }
    }

    // Kanban Grouped Layout - Column Folded
    .o_kanban_group.o_column_folded {

        // don't fill the width of record for a folded column
        .o_kanban_record.o_draggable {
            display: none !important;
        }
        // don't visually fold on smaller screens (aka. mobile)
        @include media-breakpoint-up(md) {
            --KanbanGroup-padding-h: 0;

            &, .o_column_unfold {
                cursor: col-resize;
            }

            & + .o_kanban_group.o_column_folded {
                margin-left: 1px;
            }

            .o_kanban_header {
                padding: 0 floor($o-kanban-group-padding * 0.7);
            }

            button.o_column_unfold {
                padding: 0 .15rem;

                .fa-caret-left {
                    margin-right: 0.2rem;
                }
            }

            &:hover button.o_column_unfold {
                padding: 0;

                .fa-caret-left {
                    margin-right: 0.5rem;
                }
            }

            .o_column_title {
                @include o-position-absolute(map-get($spacers, 4));
                transform-origin: left bottom 0;
                transform: rotate(90deg);
                overflow: visible;
            }
        }
    }

    @include media-breakpoint-down(md) {
        .o_kanban_group,
        .o_column_quick_create {
            scroll-snap-align: center;
            overflow-y: scroll;
        }
    }

    // Kanban UN-grouped Layout
    &.o_kanban_ungrouped {
        min-height: 100%;

        @include media-breakpoint-up(md) {
            --Kanban-padding:  var(--KanbanRecord-margin-v) var(--KanbanRecord-margin-h);
        }

        .o_kanban_record {
            width: var(--KanbanRecord-width);
            margin: calc(var(--KanbanRecord-margin-v) * 0.5) var(--KanbanRecord-margin-h);

            @include media-breakpoint-down(md) {
                margin: 0 0 #{-$border-width};
                flex-basis: 100%;
            }

            &.o_kanban_ghost {
                max-height: 0 !important; // to prevent view writers to override this height
            }
        }
    }

    // Records colours
    @include o-kanban-record-color;

    .oe_kanban_color_help {
        @include o-position-absolute(0, auto, 0, -1px);
        width: $o-kanban-color-border-width;
        z-index: 1; // show the title over kanban color
    }

    .o_field_monetary, .o_kanban_monetary {
        /*rtl:ignore*/
        direction: ltr;
    }

    span.o_kanban_monetary {
        display: inline-block;
    }
}

.o_kanban_mobile .o_kanban_renderer .o_kanban_record {
    div.label {
        @include o-text-overflow;
    }
}

.o_kanban_small_column .o_kanban_renderer.o_kanban_grouped {
    @include media-breakpoint-up(md) {
        --KanbanGroup-width: calc(var(--KanbanRecord--small-width) + (2 * var(--KanbanGroup-padding-h)));
    }
}


// ------- Sample mode -------
.o_kanban_view .o_view_sample_data {
    // all records
    .o_kanban_record,
    // progress bars and counters
    .o_kanban_counter,
    // column actions
    .o_kanban_toggle_fold,
    .o_column_archive_records,
    .o_column_unarchive_records {
        @include o-sample-data-disabled;
    }
}

// ------- Set cover dialog -------
.modal .o_kanban_cover_container .o_kanban_cover_image {
    height: 120px;
    width: 120px;
}
