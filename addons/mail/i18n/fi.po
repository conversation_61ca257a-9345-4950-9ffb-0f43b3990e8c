# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <heikki.kataji<PERSON>@myyntivoima.fi>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON> (RMO) <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>kka <PERSON>in <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# Konsta Aavaranta, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2023
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Martin Trigaux, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
#, python-format
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Lisää osoitteita sallittujen luetteloon\n"
"            </p><p>\n"
"                Suojellaksesi sinua roskapostilta ja vastaussilmukoilta Odoo estää automaattisesti sähköpostit\n"
"                jotka saapuvat palvelimellesi, kun kynnysarvo on ylittynyt <b>%(threshold)i</b> sähköposteja joka <b>%(minutes)i</b>\n"
"                minuutti. Jos on joitakin osoitteita, joista sinun on saatava hyvin usein\n"
"                päivityksiä, voit kuitenkin lisätä ne alle ja Odoo päästää ne läpi.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" ei enää seurannut"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "\"%(activity_name)s: %(summary)s\" määritetty sinulle"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\" tarvitsee pääsyn mikrofoniisi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\" tarvitsee pääsyn mikrofoniisi"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s, osoitettu %(name)s, erääntyy %(deadline)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s kanavilta %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""
"%(email)s sähköpostia ei tunnisteta kelvolliseksi sähköpostiosoitteeksi. "
"Tämä tarvitaan, jotta voidaan luoda uusi asiakas."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command."
msgstr ""
"%(new_line)s%(new_line)sKirjoita %(bold_start)s@käyttäjänimi%(bold_end)s "
"mainitaksesi jonkun ja kiinnittääksesi hänen huomionsa.%(new_line)sKirjoita "
"%(bold_start)s#kanava%(bold_end)s mainitaksesi kanavan.%(new_line)sKirjoita "
"%(bold_start)s/komento%(bold_end)s suorittaaksesi komennon."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""
"%(open_button)s%(icon)s%(open_em)sHylkää "
"muokkaus%(close_em)s%(close_button)s]"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sEsc%(close_samp)s %(open_em)s "
"%(open_cancel)speruuttaa%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)stallentaa%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"%(open_samp)sEsc%(close_samp)s %(open_em)s "
"%(open_cancel)speruuttaa%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)s "
"%(open_save)stallentaa%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
#, python-format
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""
"%(user)s kytketty. Tämä on heidän ensimmäinen yhteytensä. Toivota heille "
"onnea."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr ""
"%(user_name)s kutsui sinut seuraamaan %(document)s dokumenttia: %(title)s"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s kutsui sinut seuraamaan uutta dokumenttia."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s kiinnitti viestin tälle kanavalle."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
#, python-format
msgid "%s (Email Template)"
msgstr "%s (Sähköpostin malli)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kopio)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Message"
msgstr "%s Viesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Messages"
msgstr "%s Viestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s ja %s kirjoittavat..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s ja %s ovat reagoineet %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s luotu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "%s days overdue"
msgstr "%s päivää yli määräajan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a request"
msgstr "%s on pyytänyt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a suggestion"
msgstr "%s ehdottaa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s on reagoinut %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s kirjoittaa..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "%s messages found"
msgstr "%s viestiä löytyi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s raised their hand"
msgstr "%s nosti kätensä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s aloitti tapaamisen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"camera\" access"
msgstr "%s\" vaatii \"kamera\" -yhteyden"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" edellyttää \"näytön tallennus\"-oikeutta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s ja muita kirjoittavat..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s, %s, %s ja %s muuta henkilöä reagoisivat %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s, %s, %s ja 1 henkilö reagoisivat %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s reagoisivat %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translated from: %(language)s)"
msgstr "(Käännetty osoitteesta: %(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translation Failure: %(error)s)"
msgstr "(Käännös epäonnistui : %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "(from"
msgstr "(lähettäjä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(alunperin vastuutettu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". Tarkenna hakuasi nähdäksesi lisää vaihtoehtoja."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""
"<b invisible=\"not no_record\" class=\"text-warning\">Ei tietuetta tästä "
"mallista</b>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "<samp>%(send_keybind)s</samp><i> to send</i>"
msgstr "<samp>%(send_keybind)s</samp><i> lähetä</i>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Napin väri</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">Ylätunnisteen väri</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">Avaa asiakirja</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">Avaa ylemmän tason asiakirja</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Arkistoitu</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                Viesti lähetetään sähköpostiviestinä mallin vastaanottajille,\n"
"                                eikä se näy viestihistoriassa.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                Viesti lähetetään sisäisenä muistiinpanona, joka näkyy sisäisille\n"
"                                käyttäjille viestihistoriassa.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                Viesti lähetetään viestinä tietueeseen,\n"
"                                jossa ilmoitetaan kaikille seuraajille. Se näkyy viestihistoriassa.\n"
"                            </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Followers of the document and</span>"
msgstr ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Asiakirjan seuraajat ja</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>Jos tämä oli sinun tekemäsi:</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>Jos tämä ei ole sinun tekemääsi:</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>Avaa tietue</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>Suosittelemme, että aloitat</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Sisäinen viestintä</strong>: Vastaus tallennetaan sisäisenä "
"kommenttina. Seuraajat eivät saa sähköposti-ilmoitusta."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Alkuperäinen muistiinpano:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Tallenna</strong> sivu ja palaa takaisin, jotta voit määrittää "
"ominaisuuden."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>Et enää seuraa asiakirjaa:</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python-kirjasto, joka evaluoidaan oletusarvoja varten, kun tätä aliasta "
"varten luodaan uusia tietueita."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "Väylän olemassaolo edellyttää käyttäjää tai vierasta."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "Kanavan jäsenen tulee olla partneri tai vieras."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "Chat-tyyppisellä kanavalla ei voi olla enempää kuin kaksi käyttäjää."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr ""
"Chattia ei voi luoda useammalle kuin kahdelle henkilölle. Luo chatin sijaan "
"ryhmä."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "Viestin reaktion tulee olla joko partnerilta tai vieraalta."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use activities."
msgstr ""
"Seuraa akvititeetti voidaan suunnitella vain malleille, jotka käyttävät "
"aktiviteetteja."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"Viestien kääntäminen edellyttää voimassa olevaa Google API -avainta. "
"https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "Äänenvoimakkuuden asetus edellyttää partneria tai vierasta."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Accept"
msgstr "Hyväksy"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Access Denied"
msgstr "Käyttö estetty"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Käyttöoikeusryhmät"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Pääsytunniste"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
#, python-format
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "Pääsy rajattu ryhmälle \"%(groupFullName)s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Account"
msgstr "Tili"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Toiminto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Action Window View"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Actions"
msgstr "Toiminnot"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Toiminnot voivat laukaista tietyn toiminnan, kuten kalenterinäkymän "
"avaamisen tai automaattisesti valmiiksi merkinnän, kun asiakirja on ladattu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Saapuville sähköposteille tehtävät toimenpiteet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Aktivoidaan oletuksena kun tilataan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "Aktiivinen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "Aktiivinen verkkotunnus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Activities"
msgstr "Toimenpiteet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Generate"
msgstr "Toiminta tuottaa"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr ""
"Aktiviteetit tulee linkittää tietueisiin, joilla ei ole null-arvoa "
"res_id:ssä."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Activity"
msgstr "Toimenpide"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Toimenpiteen poikkeuksen tyyli"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Activity Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "Toimintasuunnitelma"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "Toimintasuunnitelmat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "Toimenpiteen asetukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Toimenpiteen tila"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Toimenpidetyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Toimenpiteen ikoni"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Toimenpiteiden tyypit"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Toimintasuunnitelman malli"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Toiminta-aikataulun suunnittelun ohjattu toiminto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Toimenpiteiden tyyppi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Activity: %s"
msgstr "Toimenpide: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "Lisää kontekstitoiminto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Lisää sähköpostin estolista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Lisää seuraajia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Add Users"
msgstr "Lisää käyttäjiä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#, python-format
msgid "Add a Reaction"
msgstr "Lisää reaktio"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "Lisää Tenor GIF API -avain, jotta GIF-tuki otetaan käyttöön."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"Lisää Tenor GIF API -avain, jotta GIF-tuki saadaan käyttöön. "
"https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Add a description"
msgstr "Lisää kuvaus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "Lisää kuvaus aktiviteetillesi..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr ""
"Lisää uusi %(document)s tai lähetä sähköposti vastaanottajalle "
"%(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Add a new plan"
msgstr "Lisää uusi suunnitelma"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "Lisää sähköpostiosoite estolistalle"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Add as recipient and follower (reason: %s)"
msgstr "Lisää vastaanottaja ja seuraaa (syy: %s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Lisää kontaktit, joille ilmoitetaan..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Add or join a channel"
msgstr "Lisää tai liity kanavalle"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "Lisää allekirjoitus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "Lisää Twilio-tunnukset ICE-palvelimille"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""
"Seuraajien lisääminen kanaville ei ole mahdollista. Harkitse jäsenten "
"lisäämistä sen sijaan."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""
"Useampien jäsenten lisääminen tähän keskusteluun ei ole mahdollista; se on "
"suunniteltu vain kahdelle henkilölle."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Lisäkontaktit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Edistyneet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "Lisäasetukset"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
msgid "Alert"
msgstr "Varoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"Alias %(matching_name)s (%(current_id)s) on jo linkitetty "
"%(alias_model_name)s (%(matching_id)s) ja sitä käytetään %(parent_name)s "
"%(parent_model_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"Alias %(matching_name)s (%(current_id)s) on jo linkitetty "
"%(alias_model_name)s (%(matching_id)s) kanssa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Aliaksen kontaktien tietoturva"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "Alias Domain"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "Alias Verkkotunnus"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "Alias-verkkotunnukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "Alias Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "Aliaksen nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "Alias Status"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "Alias-verkkotunnus"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Aliaksen tila arvioituna viimeksi vastaanotetun viestin perusteella."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "Aliasmalli"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Aliakset"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""
"Alias %(alias_names)s on jo käytössä bounce- tai catchall-keräilyosoitteena."
" Valitse toinen alias."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Kaikki"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "All Messages"
msgstr "Kaikki viestit"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid "All Messages if not specified"
msgstr "Kaikki viestit, jos niitä ei ole määritetty"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "All partners must belong to the same message"
msgstr "Kaikkien kumppaneiden on kuuluttava samaan viestiin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "Salli julkinen lataus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"Tapahtui SSL-virhe. Varmista, että SSL/TLS-määrityksissä on asetettu palvelimen portti.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Jokaisella liitteellä on oltava pääsytunniste."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "Sähköposti tarvitaan, jotta find_or_create toimisi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr "Sähköpostia lähetettäessä tapahtui virhe"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "Virhe sähköpostin haussa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "Odottamaton virhe tapahtui chattia luotaessa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "Ja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "Ja 1 muu jäsen."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Koskee"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#, python-format
msgid "Apply"
msgstr "Vahvista"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr "Arkistoitu, koska %(user_name)s (#%(user_id)s) poisti portaali-tilin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "Oletko varma, että haluat poistaa tämän sähköpostimallin?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "Haluatko varmasti poistaa tämän viestin?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""
"Haluatko varmasti palauttaa nämä sähköpostimallit alkuperäisiin "
"määrityksiin? Muutokset ja käännökset menetetään."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "Haluatko varmasti poistaa tämän sähköpostiosoitteen estolistalta?"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "Kysy käynnistyksen yhteydessä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to ..."
msgstr "Vastuuta hlölle ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to me"
msgstr "Ota tiketti vastuullesi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Assigned To"
msgstr "Vastuuhenkilö"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
#, python-format
msgid "Assigned to"
msgstr "Vastuuhenkilö"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"Vastuullisella käyttäjällä %s ei ole pääsyä tähän dokumenttiin, eikä pysty "
"siksi hoitamaan tätä toimintoa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "Tehtävä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "At this point lang should be correctly set"
msgstr "Tässä vaiheessa kielen pitäisi olla oikein asetettu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Liitä tiedosto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attach files"
msgstr "Liitä tiedostot"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Liite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "Tiedostoliitteiden laskuria ladataan..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Liitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Audio player:"
msgstr "Äänisoitin:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Kirjautuneet kumppanit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Tekijä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Viestin luoja. Jos tätä ei ole asetettu, viestin lähettäjä (email_from) voi "
"olla osoite, joka ei täsmännyt mihinkään kumppaniin."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Tekijän avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "Valtuutettu ryhmä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Automaattinen poisto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Automaattinen ryhmien tilaus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "Automaattinen tilaus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Automaattinen seuraaminen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "Automatisoitu kohdennettu ilmoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Automatisoitu toiminto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Automated message"
msgstr "Automaattinen viesti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""
"Automaattinen aikataulutus toiminnolle, kun nykyinen on merkitty valmiiksi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "Avatar, käyttäjä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Poissa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Background blur intensity"
msgstr "Taustan sumennuksen voimakkuus"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Pohja"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "Pohjamalli"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "Pohjamallit"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Base64-koodattu avain"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "Erän koostumus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""
"Eräloki ei voi tukea liitetiedostoja tai seuranta-arvoja useammassa kuin 1 "
"asiakirjassa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Markkinointikielto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Estolistan päiväys"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Markkinointikiellossa oleva osoite"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Mustalla listalla olevat sähköpostiosoitteet"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""
"Estetty, koska portaali-tili on poistettu %(portal_user_name)s tekijänä "
"%(user_name)s (#%(user_id)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Blur video background"
msgstr "Sumenna videon tausta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Viesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Rungon sisältö on sama kuin mallissa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Bot"
msgstr "Botti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "Viestin palautus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "Bounce Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "Bounce Email"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""
"Bounce alias %(bounce)s on jo käytössä toiselle samannimiselle "
"verkkotunnukselle. Käytä toista bouncea tai yksinkertaisesti toista alias-"
"verkkotunnusta."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "Bounce-sähköpostiviestien tulisi olla ainutlaatuisia"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"Bounce/Catchall '%(matching_alias_name)s' on jo %(document_name)s:n "
"käytössä. Valitse toinen alias tai muuta se toisessa asiakirjassa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""
"Bounce/Catchall '%(matching_alias_name)s' on jo käytössä. Valitse toinen "
"alias tai muuta se linkitetyssä mallissa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Hylätty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""
"Brave: ota käyttöön 'Google Services for Push Messaging' push-ilmoitusten "
"ottamiseksi käyttöön"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Browser default"
msgstr "Selaimen oletusarvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__endpoint
msgid "Browser endpoint"
msgstr "Selaimen päätepiste"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__keys
msgid "Browser keys"
msgstr "Selaimen näppäimet"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "Sähköpostin kopio"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "CTRL-Enter"
msgstr "CTRL-Enter"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Soitto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Camera is off"
msgstr "Kamera on pois päältä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "Voi perua"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "Voi muokata runkoa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "Voi lähettää uudelleen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "Voi kirjoittaa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr "Ei voi päivittää viestiä tai vastaanottokuittausta."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Peruuta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Peruuta sähköposti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Peruttu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Peruttu"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Tallennettu vastaus / Pikakoodi"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Canned Responses"
msgstr "Tallennetut vastaukset"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_search
msgid "Canned Responses Search"
msgstr "Valmiiden vastausten haku"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"Valmiiden vastausten avulla voit lisätä valmiiksi kirjoitettuja vastauksia osoitteeseen\n"
"                viesteihisi kirjoittamalla <i>:pikanäppäin</i>. Pikanäppäin on\n"
"                korvataan suoraan viestissäsi, joten voit edelleen muokata vastausvaihtoehtoja\n"
"                sitä ennen lähettämistä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "Ei voi muuttaa kanavan tyyppiä: %(channel_names)s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Piilokopion vastaanottajat"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "Viestin kopion vastaanottajat"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Viestin kopion vastaanottajat (voit käyttä täytettä)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "Catchall"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "Catchall Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Catchall-sähköposti"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""
"Catchall-aliasta %(catchall)s käytetään jo toisen samannimisen "
"verkkotunnuksen yhteydessä. Käytä toista catchall-tunnusta tai "
"yksinkertaisesti toista alias-verkkotunnusta."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "Catchall-sähköpostien tulisi olla yksilöllisiä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Kopio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "Ketjutyyppi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Muuta tähän aktiviteettityyppiin liittyviä taustavärejä."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Kanava"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "Kanavan jäsen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Kanavan tyyppi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Channel full"
msgstr "Kanava täynnä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "Channel members cannot include public users."
msgstr "Kanavan jäseniin ei voi kuulua julkisia käyttäjiä."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Channel settings"
msgstr "Kanavan asetukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
#, python-format
msgid "Channels"
msgstr "Kanavat"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "Kanavat / jäsenet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"Chat on yksityinen ja ainutlaatuinen kahden henkilön välillä. Ryhmä on "
"yksityinen kutsuttujen kesken. Kanavaan voi liittyä vapaasti (sen "
"kokoonpanosta riippuen)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Chats"
msgstr "Chatit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "Tarkista poissulkemisluettelo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Aliviestit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "Valitse malli..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "Valitse käyttäjä..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Choose another value or change it on the other document."
msgstr "Valitse toinen arvo tai muuta sitä toisessa asiakirjassa."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr ""
"Valitse toimeksianto toimintoja varten, joissa on kysynnän mukaan tapahtuva "
"toimeksianto."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Click here to retry"
msgstr "Klikkaa tästä yrittääksesi uudelleen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "Klikkaa nähdäksesi liitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Sulje"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Chat Window"
msgstr "Sulje chat-ikkuna"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Search"
msgstr "Sulje haku"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close button"
msgstr "Sulje-painike"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
#, python-format
msgid "Close panel"
msgstr "Sulje paneeli"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close search"
msgstr "Sulje haku"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "Suljettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "Kerää vastauksia tietyltä sähköpostiosoitteelta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Come here often? Install Odoo on your device!"
msgstr "Käytkö täällä usein? Asenna Odoo laitteeseesi!"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr ""
"Pilkulla eroteltu lista piilokopioiden vastaanottajien sähköpostiosoitteista"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Pilkulla erotettu lista vastaanottajien id:istä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Pilkulla erotettu lista vastaanottaja-kumppanien id -kentistä (voit käyttää "
"paikkamerkkejä)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Pilkulla eroteltu lista vastaanottajien sähköpostiosoitteista"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "Pilkulla erotetut vastaanottajien osoitteet (voit käyttä täytettä)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Kommentti"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "Yritykset"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr ""
"Yritykset, jotka käyttävät tätä verkkotunnusta oletusarvoisesti sähköpostien"
" lähettämiseen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "Yritys"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Luo sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Kirjoitustila"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "Asetukset"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "Määritä ICE-palvelinten lista webRTC:tä varten"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Konfiguroi aktiviteettien tyypit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "Konfiguroi oma sähköpostipalvelin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Confirm"
msgstr "Vahvista"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#, python-format
msgid "Confirmation"
msgstr "Vahvistus"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Vahvistettu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "Olet suorittanut kaikki toimenpiteesi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Hienoa! Saapuneet-laatikkosi on tyhjä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Hienoa! Saapuneet-laatikkosi on tyhjä."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Yhteys epäonnistui (ongelma lähettävässä sähköpostipalvelimessa)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "Yhteystesti epäonnistui: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Connection to SFU server closed by the server"
msgstr "Palvelin on sulkenut yhteyden SFU-palvelimeen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection type:"
msgstr "Yhteystyyppi:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection:"
msgstr "Tunnistus epäonnistui:"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Yhteydet salataan SSL/TLS tiettyä porttia käyttäen (oletus: IMAPS=993, "
"POP3S=995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "Pitää vastauksia uutena lankana"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontakti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "Ota yhteyttä ylläpitäjään"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Yhteystiedot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "Säiliömalli"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""
"Sisältää viimeisen mielenkiintoisen tapahtuman päivämäärän ja kellonajan, "
"joka tapahtui tällä kanavalla tälle kumppanille. Tämä sisältää: luomisen, "
"liittymisen, kiinnittämisen ja uuden viestin lähetyksen."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Sisältö"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""
"Sisältö, joka korvaa automaattisesti valitsemasi pikakuvakkeen. Tätä "
"sisältöä voidaan vielä muokata ennen viestin lähettämistä."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Sisältö"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "Keskustelun laskostuksen tila"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_minimized
msgid "Conversation is minimized"
msgstr "Keskustelu on pienennetty"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Tämän yhteystiedon bounced-tilassa olevien sähköpostien laskuri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Maa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Create"
msgstr "Luo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "Luo aktiviteetti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "Luontipäivä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Create Group Chat"
msgstr "Luo ryhmäkeskustelu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Luojan UID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Luo uusi tietue"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Create a new Mail Template"
msgstr "Luo uusi sähköpostimalli"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Create a new canned response"
msgstr "Luo uusi purkitettu vastaus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "Luo uusi %(document)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr ""
"Luo uusi %(document)s lähettämällä sähköposti osoitteeseen %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
#, python-format
msgid "Create: #"
msgstr "Luo: #"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Created"
msgstr "Luotu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Luonut"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Luotu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_service_patch.js:0
#, python-format
msgid "Creating a new record..."
msgstr "Luo uusi tietue..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Luontipäivä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "Kirjoittaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "Valtuudet"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification_web_push
msgid "Cron data used for web push notification"
msgstr "Cron-tiedot, joita käytetään web-push-ilmoituksissa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Valuutta"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Nykyinen käyttäjä on merkannut tähän viestiin linkitetyn ilmoituksen "
"tärkeäksi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Mukautettu palautettu viesti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "Mukautettu ICE-palvelinluettelo"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "Mukautettu malli"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "Mukautetut mallit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "Räätälöity kanavan nimi"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "Asiakas vaaditaan viestien / sähköpostin ilmoituksiin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "Mukauta automaattisten sähköpostien ulkoasua ja tunnelmaa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "Mukautetut ilmoitukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "DTLS:"
msgstr "DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Data channel:"
msgstr "Datakanava:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Päivämäärä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "Päivä, jolloin huomautukset tulisi lähettää."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "Päivämäärä, jolloin viesti on kiinnitetty"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Päivää"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "Määräaika"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "Määräaika:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Deadline: %s"
msgstr "Määräaika: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Deafen"
msgstr "Mykistä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Hyvä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Dear Sender"
msgstr "Hyvä lähettäjä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "Hyvä lähettäjä,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Koristelun tyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Oletus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "Oletusarvoinen näyttötila"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "Oletus From"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "Oletus From Alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "Oletushuomio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Oletusyhteenveto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Oletuskäyttäjä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "Oletusarvot"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"Oletusarvo alkaen, kun se ei vastaa lähtevän palvelimen suodattimia. Voi "
"olla joko paikallinen osa, esim. 'notifications', tai täydellinen "
"sähköpostiosoite, esim. '<EMAIL>', joka ohittaa kaikki "
"lähtevät sähköpostit."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Oletusvastaanottajat"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Tietueen oletusvastaanottajat:\n"
"- kumppani (käyttäen id:tä kumppanissa tai partner_id-kentässä) TAI\n"
"- sähköposti (käyttäen email_from- tai email-kenttää)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "Oletus käyttäjä"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr ""
"Määrittelee toimintojen jäjrestyksen, pienempi arvo tarkoittaa suurempaa "
"prioriteettia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "Viiveen otsikko"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Viiveen tyyppi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "Viive tangentin vapauttamisen jälkeen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Viiveen yksiköt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Poista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Poista sähköpostiviestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Delete all previews"
msgstr "Poista kaikki esikatselut"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#, python-format
msgid "Delivered"
msgstr "Toimitettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Lähetys epäonnistui"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "Toimitus epäonnistui"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr ""
"Poistettu käytöstä 'default_res_id', pitäisi käyttää 'default_res_ids'."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Kuvaus"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Kuvaus, joka lisätään tähän alatyyppiin lähetettyihin viesteihin. Jos tyhjä,"
"  viesteihin lisätään nimi."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""
"Määrittää, kuinka kanava näytetään oletuksena, kun se avataan kutsulinkistä."
" Tyhjä kenttä tarkoittaa näyttötekstiä (ei ääntä/videota)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Direct messages"
msgstr "Pikaviestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Hylkää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "Katkaise yhteys"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "RTC-yhteys katkennut palvelimelta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Viestintä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "Keskustelun sivupalkki"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "Keskustele: kanavan jäsen mykistyksen poisto"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "Keskustelukanava"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Keskustelut"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Dismiss"
msgstr "Hylkää"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Näytä vaihtoehto tähän liittyvissä asiakirjoissa, jolla voit avata ohjatun "
"kokoonpanotoiminnon tällä mallilla"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
#, python-format
msgid "Display avatar name"
msgstr "Näytä avatarin nimi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "Haluatko varmasti poistaa \"%s\"?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Do you really want to delete this preview?"
msgstr "Haluatko varmasti poistaa tämän esikatselun?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokumentti"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Dokumentin seuraajat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "Asiakirjan tunnukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "Dokumentin malli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Dokumentin nimi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "Dokumentti: \""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "Verkkotunnus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#, python-format
msgid "Done"
msgstr "Valmis"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "Merkitse tehdyksi & avaa seuraava"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Merkitse tehdyksi & aikatauluta seuraava"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
msgid "Done Date"
msgstr "Valmis päivä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Done and Schedule Next"
msgstr "Valmis ja aseta seuraavan aikataulu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Download"
msgstr "Lataa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Download Files"
msgstr "Lataa tiedostoja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Download logs"
msgstr "Lataa lokit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Download:"
msgstr "Lataa:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/dropzone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "Raahaa tiedostoja tähän"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "Eräpäivä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Erääntyy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due in"
msgstr "Erääntyy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Due in %s days"
msgstr "Erääntyy %s päivässä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due on"
msgstr "Eräpäivä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Erääntymisen tyyppi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "Sähköpostin kaksoiskappale"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "Ääniaktiviteetin kesto millisekunneissa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "Dynaamiset raportit"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "Dynaaminen käyttäjä (perustuu tietueeseen)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Edge blur intensity"
msgstr "Sivujen sumennuksen voimakkuus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Edit"
msgstr "Muokkaa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Muokkaa kumppaneita"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "Edit Subscription of %(name)s"
msgstr "Muokkaa tilausta %(name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Muokkaa tilausta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
#, python-format
msgid "Edit: %s"
msgstr "Muokkaa: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "Sähköposti Lisää allekirjoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Sähköpostiosoite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "Sähköpostialias"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Sähköpostialiakset"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "Sähköpostialiaksien Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "Email Aliases Mixin (kevyt)"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Sähköpostien estolista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "Sähköpostin painikkeen väri"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "Sähköpostikopioiden hallinta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Sähköpostin asetukset"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "Sähköpostin verkkotunnus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "Sähköpostin otsikon väri"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "Sähköpostimarkkinointi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "Sähköposti-ilmoitusten asettelu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "Sähköpostin esikatselu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Sähköpostin haku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Sähköpostin mallipohja"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Sähköpostimallin esikatselu"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "Sähköpostimallit"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Sähköpostiviestiketju"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "Sähköpostiosoite on jo olemassa!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Lähettäjän sähköpostiosoite. Tälle kentälle annetaan arvo kun lähettäjälle "
"ei automaattisesti löydy kumppania. Korvaa author_id kentän keskustelussa."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""
"Sähköpostiosoite, johon vastaukset ohjataan lähetettäessä massasähköposteja"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""
"Sähköpostiosoite, johon vastaukset ohjataan lähetettäessä sähköposteja "
"massana; käytetään vain, kun vastausta ei ole kirjattu alkuperäiseen "
"keskusteluketjuun."

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""
"Markkinointikiellon listalla olevat sähköpostiosoitteet eivät enää "
"vastaanota sähköpostiviestejä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""
"Sähköpostin aliaksia %(alias_name)s ei voi käyttää useissa tietueissa "
"samanaikaisesti. Päivitä tietueet yksi kerrallaan."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "Sähköpostin kopio"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Sähköpostin ohjattu koostaminen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""
"Sähköpostin verkkotunnus esim. 'example.com' kohdassa '<EMAIL>'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Sähköpostiviesti"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Ohjattu sähköpostien uudelleenlähetys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "Sähköpostipohjat"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Sähköpostit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "Emoji"
msgstr "Hymiö"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
#, python-format
msgid "Emojis"
msgstr "Emojit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "Vain henkilökunnalle"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Salli järjestetty seuranta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Enable Push-to-talk"
msgstr "Paina tangentia puhuaksesi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Enable desktop notifications to chat"
msgstr "Ota työpöydän ilmoitukset käyttöön chatissa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Enter"
msgstr "Käy sisään"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Enter Full Screen"
msgstr "Siirry koko näyttöön"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Virhe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Virheviesti"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Virhe kommunikoinnin aikana julkaisijan takuupalvelimen kanssa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "Virheilmoitus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""
"Virhe ilman poikkeusta. Todennäköisesti syynä ilmoitustietueiden "
"samanaikaisen käytön päivitys. Katso asiaa järjestelmänvalvojan kanssa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""
"Virhe poikkeuksetta. Todennäköisesti syynä on sähköpostin lähettäminen ilman"
" laskettuja vastaanottajia."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Virhe: kumppani ei voi olla saman tietueen seuraajan kahdesti."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "Näytä kaikille"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Poikkeus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Exit Full Screen"
msgstr "Poistu koko näytöstä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Expand"
msgstr "Laajenna"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__expiration_time
msgid "Expiration Token Date"
msgstr "Pääsytunnisteen voimassaolon päättymispäivä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Edistyneet suotimet..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Epäonnistunut posti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Epäonnistui"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid "Failed to enable push notifications"
msgstr "Push-ilmoitusten ottaminen käyttöön epäonnistui"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Failed to load gifs..."
msgstr "Gifien lataaminen epäonnistui..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr ""
"SFU-palvelimen lataaminen epäonnistui, palataan takaisin "
"vertaisverkkopalveluun"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"QWeb-mallin renderöinti epäonnistui: %(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template: %(template_txt)s)"
msgstr "Inline_template-mallin renderöinti epäonnistui: %(template_txt)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr "Inline_template-mallin renderöinti epäonnistui: %(template_txt)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template: %(view_ref)s"
msgstr "Mallin renderöinti epäonnistui: %(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Epäonnistumisen syy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Epäonnistumisen syy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Epäonnistumisen syy. Tämä on yleensä sähköpostipalvelimen ilmoittama "
"poikkeusvirhe, joka on tallennettu helpottamaan postitusongelmien "
"virheenkorjausta."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Epäonnistumisen tyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Suosikkina"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Favorites"
msgstr "Suosikit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "Toimita nyt"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "Nouda määritettyyn GIF-määrään asti."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "Kenttä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "Kenttää \"Sähköpostin aktiivisuus\" ei voida vaihtaa tyhjäksi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "Kenttää \"Sähköpostin estolista\" ei voida vaihtaa tyhjäksi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Kenttää \"Sähköpostiketju\" ei voida vaihtaa falseksi."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "Kenttien ryhmät"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Kentän tiedot"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Kenttä, jota käytetään linkittämään liittyvä malli alatyyppimalliin, kun "
"käytetään automaattista tilausta liittyvässä asiakirjassa. Kenttää käytetään"
" getattr(related_document.relation_field) laskemiseen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
#, python-format
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"Mallin \"%(model)s\" kentän \"%(field)s\" on oltava tyyppiä Many2one ja sen "
"on oltava tracking=True keston laskemista varten."

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Kentät"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""
"Tiedosto, josta malli on peräisin. Käytetään rikkinäisen mallin "
"nollaamiseen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "File too large"
msgstr "Tiedosto on liian suuri"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is disabled for external users"
msgstr "Tiedoston lataus on poistettu käytöstä ulkoisilta käyttäjiltä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is enabled for external users"
msgstr "Tiedoston lataus on käytössä ulkoisille käyttäjille"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Files"
msgstr "Tiedostot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Fold"
msgstr "Laskosta"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "Laskostettu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Follow"
msgstr "Seuraa"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Seuraajat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Seuraajien lomake"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Vain seuraajat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "Lisättävät seuraajat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "Poistettavat seuraajat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Following"
msgstr "Seurataan"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome -ikoni esim.. fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""
"Kohdassa %(channels)s kanavatyypin tulee olla 'channel', jotta sillä on "
"ryhmäpohjainen valtuutus tai ryhmätilaus."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 1 hour"
msgstr "1 tunnin ajan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 15 minutes"
msgstr "15 minuutin ajan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 24 hours"
msgstr "24 tunnin ajan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 3 hours"
msgstr "3 tunnin ajan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 8 hours"
msgstr "8 tunnin ajan"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "Pakota lähettäminen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "Pakota kieli:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Muotoiltu sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Alkaa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "From peer:"
msgstr "Vertaisilta:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Täysi syöttönäkymä"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "Kokoruudun video"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Future"
msgstr "Tulevaisuudessa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Tulevat toimenpiteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Category"
msgstr "GIF-luokka"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Favorites"
msgstr "GIF-suosikit"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF-suosikki"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "GIF id Tenorilta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "GIFs"
msgstr "GIF:t"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Yhdyskäytävä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Go to conversation"
msgstr "Siirry keskusteluun"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "Google Translate -integraatio"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "Ryhmä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "Ryhmän nimi"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""
"Ryhmävaltuutusta ja automaattista ryhmätilausta tuetaan vain kanavilla."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Ryhmittele..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "Ryhmitetty keskustelu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "Ryhmät"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
#, python-format
msgid "Guest"
msgstr "Vieras"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "Vieraan nimi ei voi olla tyhjä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "Vieraan nimi on liian pitkä."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "Vieraat"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-reititys"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Hallitse sähköpostissa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Hallitse Odoossa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "Sisältää virheen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "On sähköpostin aktiviteetteja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "On sähköpostin markkinointikieltolista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "On sähköpostin ketju"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "On mainintoja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "On kytkenyt äänet pois päältä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "On virhe"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "Sisältää pyynnöstä vastaavan"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Otsakkeet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Hei"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Hei,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Piilotettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "Kätketty malli"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Attachments"
msgstr "Piilota liitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Hide Call Settings"
msgstr "Kätke soittoasetukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Member List"
msgstr "Kätke jäsenlista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#, python-format
msgid "Hide Pinned Messages"
msgstr "Piilota kiinnitetyt viestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Hide sidebar"
msgstr "Kätke sivupalkki"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Piilota alatyyppi seuraajavalinnoista"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""
"Piilota julkisille / portaalin käyttäjille, riippumatta alatyypin "
"asetuksista."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "Korkea"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "History"
msgstr "Historia"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Sähköpostipalvelimen nimi tai IP osoite"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""
"Kuinka kauan äänilähetys pysyy aktiivisena äänenvoimakkuuden kynnyksen "
"ylittämisen jälkeen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE-palvelimet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE gathering:"
msgstr "ICE:n kokoontuminen:"

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE-palvelin"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "ICE-palvelimet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE:"
msgstr "ICE:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__id
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Ylätason aliastietueen id (esim. tehtävien luontiin käytettävän aliaksen "
"sisältävä projekti)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "Pikaviestimen tila"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP-palvelin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Kuvake"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kuvake joka kertoo poikkeustoiminnosta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Seuratun resurssin id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "Identiteetti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Idle"
msgstr "Toimeton"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "Jos SSL vaaditaan."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "If not set, shared with all users."
msgstr "Jos ei asetettu, jaetaan kaikille käyttäjille."

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""
"Jos asetettu, jokainen tähän kenttään tehty muutos seurataan. Arvoa "
"käytetään seuranta-arvojen järjestämiseen."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""
"Jos asetettu, jäsen ei saa kanavan ilmoituksia ennen tätä päivämäärää."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""
"Jos valittu, jononhallinta lähettää sähköpostin päivämäärän jälkeen. Jos ei "
"valittu, sähköposti lähetetään mahdollisimman pian. Jos aikavyöhykettä ole "
"määritetty, sen katsotaan olevan UTC-aikavyöhykkeellä."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""
"Jos valittu, jononhallinta lähettää sähköpostin päivämäärän jälkeen. Jos ei "
"valittu, sähköposti lähetetään mahdollisimman pian. Voit käyttää dynaamista "
"lauseketta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jos asetettu, lähetetään automaattisesti luvattomille käyttäjille "
"oletusviestin sijasta."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"Jos asetettu, malli rajoitetaan koskemaan vain tätä käyttäjää."
"                                                   Jos asetettu, jaetaan "
"kaikille käyttäjille."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Jos sähköpostiosoite on markkinointikiellossa, yhteystieto ei vastaanota "
"postituksia miltään postituslistalta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"Jos True, vastaukset eivät mene alkuperäisen asiakirjan keskusteluketjuun. "
"Sen sijaan se tarkistaa reply_to-parametrin seurantaviestin id:stä ja ohjaa "
"sen vastaavasti. Tämä vaikuttaa luotuun viestitunnukseen."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Jos haluat asettaa catchall-sähköpostidomainin (osoite joka vastaanottaa "
"kaikki domainiin saapuvat postit, joilla ei ole vastaanottajaa), joka "
"ohjataan Odoo-palvelimelle, aseta domainin nimi tässä."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "If you want to use Twilio as TURN/STUN server provider"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "Ohita kaikki"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "Kuva"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Kuva 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Kuva 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Kuva 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Kuva 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "Kuvan MIME-tyyppi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "Kuva on linkki"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""
"Kommentointitilassa: jos asetettu, lykkää ilmoitusten lähettämistä. "
"Massapostitilassa: jos asetettu, lähetä sähköpostit kyseisen päivämäärän "
"jälkeen. Tämän päivämäärän katsotaan olevan UTC-aikavyöhykkeessä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Passiivinen alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Saapuvan sähköpostin palvelin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Saapuneet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "Saapuva puhelu..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "Saapuva sähköposti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Saapuvan sähköpostin palvelimet"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Saapuvan sähköpostin palvelin"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Saapuvan sähköpostin palvelimet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr ""
"Ilmaisee, että tämä aktiviteetti on luotu automaattisesti, eikä käyttäjän "
"luomana."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Info"
msgstr "Info"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Alkuperäinen malli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "Riviin merkitty täydellinen osoite"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Input device"
msgstr "Vastaanottolaite"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Install"
msgstr "Asenna"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "Integraatiot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Vain sisäinen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "Sisäinen viestintä:"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "Virheellinen"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Invalid domain %(domain)r (type %(domain_type)s)"
msgstr "Virheellinen verkkotunnus %(domain)r (type %(domain_type)s)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Virheellinen sähköpostiosoite"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Virheellinen sähköpostiosoite %r"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Virheellinen lauseke. Lausekkeen täytyy olla oikeanmuotoinen python-"
"dictionary, esim. \"{'kentta': 'arvo'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr ""
"Virheellinen kenttä \"%(field_name)s\" luotaessa kanavaa, jossa on jäseniä."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "Virheellinen osoite"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "Virheellinen pääsähköposti mallilla %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
#, python-format
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "Virheelliset res_ids %(res_ids_str)s (type %(res_ids_type)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"Virheellinen palvelimen nimi!\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""
"Virheellinen mallin tai näkymän lähde %(svalue)s (type %(stype)s), pitäisi "
"olla tietue tai XMLID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""
"Virheellinen malli tai näkymän lähde Xml ID %(source_ref)s ei ole enää "
"olemassa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr "Virheellinen mallin tai näkymän lähdetietue %(svalue)s, on %(model)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""
"Virheellinen mallin tai näkymän lähdeviittaus %(svalue)s, on %(model)s sen "
"sijaan"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""
"Virheellinen arvo luotaessa kanavaa, jossa on jäseniä, vain 4 tai 6 "
"sallitaan."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""
"Virheellinen arvo luotaessa jäsenyyksiä sisältävää kanavaa. Vain 0 on "
"sallittu."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "Kutsun URL"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "Kutsu seuraamaan %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite"
msgstr "Kutsu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
#, python-format
msgid "Invite Follower"
msgstr "Kutsu seuraaja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Invite a User"
msgstr "Kutsu käyttäjä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite people"
msgstr "Kutsu ihmisiä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Channel"
msgstr "Kutsu kanavalle"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Group Chat"
msgstr "Kutsu ryhmäkeskusteluun"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Kutsuavustaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "On aktiivinen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "On nykyinen käyttäjä tai tekijävieras"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "On muokattava"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "On muokkaaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "On jäsen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "On luettu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "On itse"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "Onko mallieditori"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_chat
msgid "Is a chat"
msgstr "On keskustelu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "On loki"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "Onko keskustelun sivupalkin kanavan kategoria avoin?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "Onko keskustelun sivupalkin kategorian keskustelu avoin?"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "Onko mikrofoni kytketty pois päältä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "On kiinnitetty käyttöliittymään"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "On lähettämässä videota"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "On jakamassa näyttöä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""
"Näyttää siltä, että yrität luoda kanavan jäsentä, mutta olet ilmeisesti "
"unohtanut määrittää siihen liittyvän kanavan. Jos haluat jatkaa, varmista, "
"että annat tarvittavat kanavatiedot."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_partner_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"Viittaa selaimen näppäimiin, joita ilmoitus käyttää:\n"
"- p256dh: Se on selaimen luoma tilauksen julkinen avain. Selain\n"
"          pitää yksityisen avaimen salassa ja käyttää sitä hyötykuorman purkamiseen\n"
"- auth: Auth-arvoa on käsiteltävä salaisuutena, eikä sitä saa jakaa Odoon ulkopuolelle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""
"JSON, joka kartoittaa many2one-kentän tunnukset käytetyiksi sekunneiksi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "Liity"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Join Call"
msgstr "Liity puheluun"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Join Channel"
msgstr "Liity kanavalle"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "Liity ryhmään"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#, python-format
msgid "Jump"
msgstr "Hyppää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Jump to Present"
msgstr "Hyppää nykyhetkeen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Säilytä liitteet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "Pidä valmiit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "Pidä viestin kopio"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Säilytä alkuperäinen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""
"Säilytä kopio sähköpostin sisällöstä, jos sähköpostit poistetaan (vain "
"massapostitus)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr "Pidä toiminnot, jotka on merkitty tehdyiksi toimintonäkymässä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "Avain"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
#, python-format
msgid "Kind Regards"
msgstr "Ystävällisin terveisin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "LIVE"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Kieli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Edellinen noutopäivä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "Haettu viimeksi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "Viimeinen kiinnostus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "Viimeksi nähty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Viimeksi päivitetty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__last_used
msgid "Last Used"
msgstr "Viimeksi käytetty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "Viimeksi nähty päivänä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__last_used
msgid "Last time this shortcode was used"
msgstr "Viimeksi tätä lyhytkoodia käytettiin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Late"
msgstr "Myöhässä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Myöhässä olevat toimenpiteet"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Launch Plans"
msgstr "Laukaise suunnitelmat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Ulkoasu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "Poissaolo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Leave Conversation"
msgstr "Jätä keskustelu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Leave this channel"
msgstr "Eroa kanavalta"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "Linkin esikatselut"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Link copied!"
msgstr "Linkki kopioitu!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Listaa nykyisen kanavan käyttäjät"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Load More"
msgstr "Lataa lisää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Load more"
msgstr "Näytä lisää"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Load template"
msgstr "Lataa malli"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Loading"
msgstr "Ladataan"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Paikallinen palvelin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Paikallisiin osiin perustuva saapuvien lähetysten havaitseminen"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""
"Sähköpostin paikallinen osa, jota käytetään Reply-To -osoitteessa vastausten"
" saamiseksi, esim. \"catchall\" osoitteessa \"<EMAIL>\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"Sähköpostin paikallinen osa, jota käytetään Return-Pathissa, jota käytetään,"
" kun sähköpostit kimpoavat, esim. 'bounce' sanassa '<EMAIL>'"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Tallenna"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Log RTC events"
msgstr "RTC-tapahtumien kirjaaminen"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "Kirjaa muistiinpano..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "Kirjaa tehty toimenpide"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Log an internal note…"
msgstr "Kirjaa sisäinen huomautus…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "Kirjaa muistiinpano"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Log step:"
msgstr "Kirjaa askel:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#, python-format
msgid "Logged in as %s"
msgstr "Kirjautunut käyttäjänä %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "Kirjautumistiedot"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "Alin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Lower Hand"
msgstr "Laske käsi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "MIME-tyyppi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Sähköposti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Sähköpostin aktiviteetti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Postitoiminnan tyyppi"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Sähköpostin estolista"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Sähköpostin estolistan mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "Sähköpostikanavan lomake"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "Sähköpostin luomisen mixin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mail Failures"
msgstr "Sähköpostin virheet"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "Sallittu sähköpostin kuljetustie"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "Mail Layout"
msgstr "Sähköpostin asettelu"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "Sähköpostin pääliitteen hallinta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "Sähköpostin viestin tunnus (Id int)"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "Sähköpostin RTC-istunto"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "Mail Render Mixin"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "Sähköpostipalvelin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Viestipohja"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "Sähköpostimallin editori"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "Sähköpostimallin nollaus"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
#, python-format
msgid "Mail Templates have been reset"
msgstr "Sähköpostimalli on nollattu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Sähköpostiketju"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Sähköpostin seuranta-arvo"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""
"Kommentointitilassa olevan sähköpostin koostajan pitäisi toimia vähintään "
"yhdellä tietueella. Tietueita ei löytynyt (model %(model_name)s)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr ""
"Olemassaolevasta sähköpostiviestistä (mail.message) on lähetetty ilmoitus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Mail template model of %(action_name)s does not match action model."
msgstr "Mail-mallin malli %(action_name)s ei vastaa toimintamallia."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "Lähetä malli tämän palvelimen kautta"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "Sähköposti: Sähköpostijonon hallinta"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "Postipalvelin"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "Mail: lähetä web push -ilmoitus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Sähköpostilaatikko ei ole saatavilla - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mailboxes"
msgstr "Postilaatikot"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""
"Lähteen kanssa tapahtuvaa postitusta tai lähettämistä ei saa kutsua tyhjällä"
" %(source_type)s -kentällä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Postit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Hallinnoi viestejä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Hallitse vastauksia uusina saapuvina sähköpostiviesteinä sen sijaan, että "
"vastaukset menisivät samaan viestiketjuun."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Mark As Read"
msgstr "Merkitse luetuksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Mark Done"
msgstr "Merkitse tehdyksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Mark all read"
msgstr "Merkitse kaikki luetuksi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree_without_record_access
msgid "Mark as Done"
msgstr "Merkitse tehdyksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Read"
msgstr "Merkitse luetuksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Todo"
msgstr "Merkitse tehtäväksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Unread"
msgstr "Lukematta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Mark as done"
msgstr "Merkitse tehdyksi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""
"Medialaitteet ovat saavuttamattomissa. SSL:ää ei ehkä ole asetettu oikein."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "Media"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Tapaaminen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "Jäsenten määrä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
#, python-format
msgid "Member List"
msgstr "Käyttäjäista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "Jäsenet"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Näiden ryhmien jäsenet lisätään automaattisesti seuraajiksi. Huomaa, että he"
" voivat hallita tilaustaan tarvittaessa manuaalisesti."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Mentions"
msgstr "Viittaukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
#, python-format
msgid "Mentions Only"
msgstr "Vain maininnat"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Kumppanin yhdistämistyökalu"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Yhdistä seuraavat kontaktit:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Viesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message #%(thread name)s…"
msgstr "Viesti #%(thread name)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message %(thread name)s…"
msgstr "Viesti %(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "Viestin ID"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Viestien ilmoitukset"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "Viestin reaktio"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "Viestin reaktiot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Viestin tietuenimi"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "Viestin käännös"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "Viestin kääntämisen API-avain"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Viestityyppi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "Viestin kuvaus: joko aihe tai varsinaisen viestin alku"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "Viesti lähetetty kohteeseen \"%s\""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Viestin vastaanottajat (sähköpostit)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "Viestin viitteet, kuten tunnisteet aikaisemmista viesteistä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "Viestin tulee olla kelvollinen EmailMessage-esiintymä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Viestin alatyyppi antaa tarkemman tyypin viestille, erityisesti "
"järjestelmäilmoituksille. Se voi olla esimerkiksi ilmoitus, joka liittyy "
"uuteen tietueeseen (Uusi) tai prosessin vaiheen muutokseen (Stage change). "
"Viestien alatyypit antavat mahdollisuuden säätää tarkasti ilmoitukset, joita"
" käyttäjä haluaa vastaanottaa seinälleen."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Viestin alityypit"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Viestien alatyypit seurasivat, eli alatyyppejä, jotka työnnetään käyttäjän "
"seinälle."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Viestityyppi: Sähköposti  sähköposteille, Ilmoitus järjestelmän viesteille, "
"ja Kommentti muille viesteille, kuten käyttäjien vastauksille"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Viestin yksilöivä tunniste"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "Viestin ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Viestit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Viestien haku"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "Luetuksi merkatut viestit näkyvät historiassa."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Sisäisiä alatyyppejä sisältävät viestit näkyvät vain työntekijöille eli "
"base_user-ryhmän jäsenille"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "Viestejä, joissa on seurantaarvoja, ei voi muokata"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "Ääniliitteiden metatiedot"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "Puuttuva sähköposti"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Puuttuva sähköpostiosoite"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "Osoitteesta puuttuu"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""
"Mixin, joka laskee ajan, jonka tietue on viettänyt jokaisessa arvossa, jonka"
" many2one-kenttä voi ottaa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "Malli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Malli on muuttunut"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Seuraavan resurssin malli"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Malli, jota alatyyppi koskee. Jos arvoksi asetettu False, tämä alatyyppi "
"koskee kaikkia malleja."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Mallit"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Mallin muuttaminen voi vaikuttaa olemassa oleviin toimintoihin, joissa "
"käytetään tätä toimintatyyppiä, ole varovainen."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Moduulin poisto"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Kuukaudet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "More"
msgstr "Lisää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Mute"
msgstr "Mykkä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Mute Channel"
msgstr "Mykistä kanava"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
msgid "Mute notifications until"
msgstr "Mykistä ilmoitukset, kunnes"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Toimenpiteeni määräaika"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "Omat mallipohjat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "Nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Vaatii toimenpiteitä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Channel"
msgstr "Uusi kanava"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Message"
msgstr "Uusi viesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Uusi Char-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Uusi Datetime-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Uusi Float-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Uusi Integer-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Uusi Text-arvo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "New message"
msgstr "Uusi viesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages"
msgstr "Uudet viestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Uudet viestit tulevat näkyviin tähän."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Uudet arvot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Seuraavat toimenpiteet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "Seuraava toimenpide"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Seuraavan toimenpiteen eräpäivä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Seuraavan toimenpiteen kuvaus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Seuraavan toimenpiteen tyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Seuraavat saatavilla olevat aktiviteetit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "No"
msgstr "Ei"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "Ei virhettä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "No Followers"
msgstr "Ei seuraajia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "No IM status available"
msgstr "Pikaviestistatus ei ole saatavilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "Ei tietuetta"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "Ei toimintaa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No channel found"
msgstr "Kanavaa ei löydy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "Keskustelua ei valittu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Keskustelua ei ole vielä..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No history messages"
msgstr "Ei viestihistoriaa"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "message_id ei löydy kontekstista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
#, python-format
msgid "No messages found"
msgstr "Viestejä ei löytynyt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "No recipient"
msgstr "Ei vastaanottajaa"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "Vastaanottajaa ei löydy."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"Ei vastausta. Tarkista palvelimen tiedot.\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""
"%(activity_type_name)s ei ole määritetty vastuulliseksi: "
"%(activity_summary)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "No results found"
msgstr "Hakutuloksia ei löytynyt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr "Ei tähdellisiä viestejä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Älä tee vastauksista viestiketjuja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No user found"
msgstr "Käyttäjä ei löydy."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "Ei löytynyt käyttäjää, joka ei olisi jo tämän kanavan jäsen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "No users found"
msgstr "Käyttäjiä ei löydy."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
#, python-format
msgid "Non existing record or wrong token."
msgstr "Ei olemassa olevaa tietuetta tai väärä pääsytunniste."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#, python-format
msgid "None"
msgstr "Ei mitään"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Normalisoitu sähköposti"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Ei vahvistettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "Ei testattu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Muistiinpano"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#, python-format
msgid "Nothing"
msgstr "Ei mitään"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Ilmoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "Huomiosähköposti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Notification Item Image"
msgstr "Ilmoituskohteen kuva"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "Huomioparametri"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Notification Settings"
msgstr "Ilmoitusasetukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Ilmoitustyyppi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Ilmoituksen tulisi vastaanottaa liitetiedostot luettelona tai "
"tietokantariveinä (received %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""
"Ilmoituksen tulisi vastaanottaa liitetietueet ID-luettelona (received "
"%(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Ilmoituksen tulisi vastaanottaa kumppanit, jotka annetaan ID-luettelona "
"(received %(pids)s)"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "Ilmoitukset: Poista yli 6kk ikäiset"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Send scheduled message notifications"
msgstr "Huomiot: lähetä ajastetut viestihuomiot"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Ilmoitukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications allowed"
msgstr "Sallitut ilmoitukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications blocked"
msgstr "Ilmoitukset estetty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Päivien, viikkojen tai kuukausien määrä ennen toimenpiteen suorittamista. "
"Tämän avulla voidaan suunnitella toiminnan määräaika."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will not send notifications on this device."
msgstr "Odoo ei lähetä ilmoituksia tällä laitteella."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will send notifications on this device!"
msgstr "Odoo lähettää ilmoituksia tälle laitteelle!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "Pois"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Offline"
msgstr "Poissa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Offline -"
msgstr "Offline -"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Vanha Char-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Vanha Datetime-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Vanha Float-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Vanha Integer-arvo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Vanha Text-arvo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "Vanhat arvot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Online"
msgstr "Verkossa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Online -"
msgstr "Online -"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "Vain järjestelmänvalvojat voivat viedä sähköpostiviestejä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Vain mukautettuja malleja voidaan muokata."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "Vain sisäinen käyttäjä voi vastaanottaa ilmoituksia Odoossa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr "Vain kommenttityyppisten viestien sisältö voidaan päivittää"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""
"Vain kommenttityyppisten viestien sisältöä voidaan päivittää mallissa "
"'discuss.channel'"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Only users belonging to the \"%(group_name)s\" group can modify dynamic "
"templates."
msgstr ""
"Vain ryhmään \"%(group_name)s\" kuuluvat käyttäjät voivat muokata dynaamisia"
" malleja."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
#, python-format
msgid "Open"
msgstr "Avoin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
#, python-format
msgid "Open Actions Menu"
msgstr "Avaa Toiminnot-valikko"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "Avaa dokumentti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Open Form View"
msgstr "Avaa lomakkeen näkymä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Open Link"
msgstr "Avaa linkki"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "Avaa omistaja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Open card"
msgstr "Avaa kortti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#, python-format
msgid "Open in Discuss"
msgstr "Avaa keskustelussa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Operation not supported"
msgstr "Toiminto ei ole tuettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Markkinointikiellon asettaneet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Valinnainen sen viestiketjun (tietueen) ID, johon kaikki saapuvat viestit "
"liitetään, vaikka niihin ei vastattaisikaan. Jos tämä asetetaan, uusien "
"tietueiden luominen estetään kokonaan."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "Valinnainen mail_mail ID. Käytetään pääasiassa hakujen optimointiin."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Lisätiedoksi toivottu lähtevän sähköpostin palvelin, jos ei asetettu "
"korkeimman prioriteetin palvelinta käytetään."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valinnainen käännöskieli (ISO-koodi), joka valitaan sähköpostia "
"lähetettäessä. Jos sitä ei ole asetettu, käytetään englanninkielistä "
"versiota. Tämän pitäisi yleensä olla sijoitusilmaus, joka antaa sopivan "
"kielen, esim. {{ object.partner_id.lang }}."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"Alkuperäinen keskustelu: Vastaukset tulevat alkuperäisen asiakirjan keskusteluketjuun.\n"
" Toinen sähköpostiosoite: Vastaukset lähetetään seurantaviestin tunnisteessa mainittuun sähköpostiosoitteeseen alkuperäisen asiakirjan keskusteluketjun sijasta.\n"
" Tämä vaikuttaa luotuun viestin tunnisteeseen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "Alkuperäinen viesti on poistettu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
#, python-format
msgid "Other activities"
msgstr "Muuta tekemistä"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Lähtevä"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "Lähtevä sähköposti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Lähtevän sähköpostin palvelimet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Lähtevän sähköpostin palvelin"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Lähtevät sähköpostit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Lähtevän postin palvelin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Myöhässä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Yliaja luojan sähköposti"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP palvelin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP-palvelimet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets received:"
msgstr "Vastaanotetut paketit:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets sent:"
msgstr "Lähetetyt paketit:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Ylätaso"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Edeltävä viesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "Ylätason malli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Ylätason keskustelun ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Aliaksen hallussa oleva emomalli. Alias-viittauksen sisältävä malli ei "
"välttämättä ole alias_model_id:llä annettu malli (esimerkki: project "
"(parent_model) ja task (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Vanhempaa alatyyppiä, jota käytetään automaattisessa tilauksessa, ei ole "
"oikea. Esimerkiksi projektissa projektin alatyyppien parent_id viittaa "
"tehtäviin liittyviin alatyyppeihin."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "Kumppani"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Kumppaniprofiili"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "Kumppani Vain luku"

#. module: mail
#: model:ir.model,name:mail.model_mail_partner_device
msgid "Partner Web Push Device"
msgstr "Kumppanin Web Push -laite"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr ""
"Yhteistyökumppani, jolla on lisätietoja postin uudelleenlähetystä varten"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "Kumppanit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Toimenpiteitä vaativat kumppanit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "Salasana"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "Liitä API-avaimesi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Pause"
msgstr "Tauko"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__payload
msgid "Payload"
msgstr "Hyötykuorma"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "Poista tämä malli pysyvästi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Puhelin"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "Puhelinsoitto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Pin"
msgstr "Pin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Pin It"
msgstr "Kiinnitä se"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "Kiinnitetty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
#, python-format
msgid "Pinned Messages"
msgstr "Kiinnitetyt viestit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "Suunnittele"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "Suunnitelma on saatavilla"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date_deadline
msgid "Plan Date"
msgstr "Suunnitelman päivämäärä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "Suunnitelman nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__assignation_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_assignation_summary
msgid "Plan Summary"
msgstr "Suunnitelman tiivistelmä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "Suunnitelman tiivistelmä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Suunniteltu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Planned Activities"
msgstr "Suunnitellut toimet"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Suunniteltu viive"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "Suunnittelu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Play"
msgstr "Toista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "Täytä asiakkaan tiedot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Ota sen sijaan yhteyttä meihin käyttämällä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "Odota, tiedosto ladataan."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Käytäntö siitä, miten Chatter-ilmoituksia käsitellään:\n"
"- Ilmoitukset lähetetään sähköpostiosoitteeseesi\n"
"- Käsittele Odoossa: ilmoitukset näkyvät Odoon Saapuneet-kansiossasi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Käytäntö, jolla lähetetään viesti asiakirjaan mailgatewayn avulla.\n"
"- everyone: kaikki voivat lähettää viestin\n"
"- partners: vain todennetut kumppanit\n"
"- seuraajat: vain asiaan liittyvän asiakirjan seuraajat tai seuraavien kanavien jäsenet\n"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "Portti"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "Portaalin käyttöoikeus myönnetty"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "Portaalin käyttöoikeus peruutettu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "Lähetä asiakirjaan"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""
"Viestin lähettäminen on tehtävä työasiakirjalla. Lähetä ilmoitus käyttäjälle"
" message_notify-ohjelmalla."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""
"Viestin lähettämisen pitäisi vastaanottaa liitteet luettelona tai "
"tietokantariveinä (received %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""
"Viestin lähettämisen pitäisi vastaanottaa liitetiedostot ID-luettelona "
"(received %(aids)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""
"Viestin lähettämisen pitäisi vastaanottaa kumppanit ID-luettelona (received "
"%(pids)s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "Järjestelmää pyörittää"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Edeltävät toimenpiteet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "Suositeltu vastausosoite"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Press Enter to start"
msgstr "Paina Enter aloittaaksesi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr "Paina näppäintä rekisteröidäksesi sen tangentin-pikanäppäimeksi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Esikatselu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Esikatselu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Edellisen toiminnon tyyppi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "Yksityisyys"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Käsittele jokainen saapuva sähköposti osana tämän dokumenttityypin "
"keskustelua. Tämä luo uuden dokumentin jokaiselle uudelle keskustelulle, tai"
" liittää vastausviestit olemassaoleville keskusteluille (dokumenteille)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
#, python-format
msgid "Processing"
msgstr "Käsittelyssä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Public Channel"
msgstr "Julkiset kanavat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Public Channels"
msgstr "Julkiset kanavat"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Kustantajan takuusopimus"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "Julkaisija: Päivitä ilmoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "Tangentin näppäinoikotie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "Tangentin näppäin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Quick search..."
msgstr "Pikahaku..."

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC-istunto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "RTC Session ID:"
msgstr "RTC-istunnon tunnus:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC-istunnot"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC-istunnot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Raise Hand"
msgstr "Nosta käsi ylös"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "Alueet 0,0 ja 1,0 välillä. Asteikko riippuu selaimen toteutuksesta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "Reagoiva vieras"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "Reagoiva partneri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "Reaktiot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "Lukupäivä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#, python-format
msgid "Ready"
msgstr "Valmis siirrettäväksi"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Valmiina lähetettäväksi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Syy"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "Vastaanota ilmoituksia Odoossa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Vastaanotettu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Recent"
msgstr "Viimeisin"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Vastaanottaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "Vastaanottajan nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Vastaanottajat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "Suositellut aktiviteetit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Suositellun toiminnon tyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Tietue"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "Tiedon nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Tietueen keskustelun ID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Tietueet:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Viitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Refuse"
msgstr "Hylkää"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "Terveisin,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Register new key"
msgstr "Rekisteröi uusi avain"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Reject"
msgstr "Hylkää"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Liittyvä yritys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Liittyvä dokumentti ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "Liittyvän asiakirjan tunnukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Liittyvä dokumenttimalli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Liittyvän dokumentin mallin nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "Liittyvä sähköpostin malli"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Liittyvä viesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Liittyvä kumppani"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Yhdistämiskenttä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
#, python-format
msgid "Remove"
msgstr "Poista"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "Poista kontekstitoiminto"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "Poista seuraajat"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "Poista sähköposti markkinointiestolistalta, ohjattu toiminto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Poista kontekstuaalinen toiminto käyttääksesi tätä mallia asiaan liittyvissä"
" dokumenteissa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Poista seuraaja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "Poistetut kentän tiedot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Rename"
msgstr "Uudelleennimeä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "Renderöintimalli"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""
"Renderöinti %(field_name)s ei ole mahdollista, koska mallissa ei ole "
"vastinetta."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr ""
"Renderöinti %(field_name)s ei ole mahdollista, koska sitä ei ole määritelty "
"mallissa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Repeat"
msgstr "Toista"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "Vastaukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Vastaa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Vastausosoite"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Vastausosoite"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "Vastausosoite"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "Vastataan osoitteelle"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Report"
msgstr "Raportti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "Pyynnön esittänyt kumppani"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "Lähetä uudelleen"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "Lähetä uudelleen sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Uudelleenlähetystyökalu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Resetoi vahvistus"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "Nollaa sähköpostimalli"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "Nollaa malli"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "Salasanan palauttaminen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Vastuuhenkilö"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "Rajoita mallin renderöintiä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""
"Rajoita sähköpostimallien muokkauksen ja QWEB-paikanvaraajien käyttöä."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "Rajoitetut liitteet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "Kielen tunnistuksen tulos sen sisällön perusteella."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Yritä uudelleen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Revert"
msgstr "Kumoa muutokset"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "Tarkista kaikki mallit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "Rich-text -muotoiset sisällöt"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Rich-text/HTML -muotoiltu viesti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "Soittava istunto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "RTC-istunto"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "SFU-palvelimen URL-osoite"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "SFU-palvelimen avain"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "SFU-palvelimen URL"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP-palvelin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Myyjä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "Save"
msgstr "Tallenna"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save Template"
msgstr "Tallenna mallipohja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Tallenna uutena mallina"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Save editing"
msgstr "Tallenna muokkaus"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "Tallenna suosikki GIF Tenor API:sta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Aikatauluta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "Aikatauluta ja merkitse valmiiksi"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity"
msgstr "Aikatauluta toimenpide"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity On Selected Records"
msgstr "Toiminnan ajoittaminen valituille tietueille"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "Aikatauluta toimenpide ja aikaansaa asioita."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Aikatauluta toimenpide"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Aikatauluta toimenpide"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Aikatauluta toimenpide"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity on selected records"
msgstr "Ajoittaa toiminto valituille tietueille"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Suunniteltu päivämäärä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Scheduled Message"
msgstr "Aikataulutettu viesti"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "Aikataulutetut viestit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Aikataulutettu lähetyspäivä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "Skripti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search"
msgstr "Hae"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "Hae alias"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "Hakuryhmät"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Etsi saapuvan sähköpostin palvelimia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Search Messages"
msgstr "Hae viestejä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "Etsi RTC-istunto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search button"
msgstr "Hakupainike"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search for a GIF"
msgstr "GIF:n etsiminen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a channel..."
msgstr "Etsi kanavaa..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a user..."
msgstr "Etsi käyttäjää..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search in progress"
msgstr "Haku käynnissä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "Search messages"
msgstr "Etsi viestejä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search..."
msgstr "Hae..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "Etsi: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Email Changed"
msgstr "Tietoturvapäivitys: Sähköposti muutettu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Login Changed"
msgstr "Tietoturvapäivitys: Kirjautuminen muutettu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Password Changed"
msgstr "Tietoturvapäivitys: Salasana muutettu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "Katso virheen yksityiskohdat"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "See all pinned messages."
msgstr "Katso kaikki kiinnitetyt viestit."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "Valitse kieli"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Select a user..."
msgstr "Valitse käyttäjä..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "Valitse GIF-kuvien suodattamiseen käytettävä sisältösuodatin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Selected users:"
msgstr "Valitut käyttäjät:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Lähetä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "Lähetä ja sulje"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "Lähetä sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "Lähetä sähköposti nimellä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Lähetä sähköposti (%s)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Send Notification"
msgstr "Lähetä ilmoitus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Lähetä heti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Send a message to followers…"
msgstr "Lähetä viesti seuraajille…"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "Lähetä ja vastaanota sähköposteja Gmail-tilin kautta."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "Lähetä ja vastaanota viestejä Outlook-tilin kautta."

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Lähetä sähköposti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "Lähetä postituksia tai ilmoituksia suoraan"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Send message"
msgstr "Lähetä viesti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "Lähettäjän osoite"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Lähettäjän osoite (tässä voi käyttää varauksia). Jos ei asetettu, oletusarvo"
" on kirjoittajan sähköpostialias, jos se on määritetty, tai "
"sähköpostiosoite."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "Lähetysvirheet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Lähetetty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "Palvelin ja kirjautuminen"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Palvelintoiminto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "Palvelimen tiedot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Palvelimen nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Palvelimen prioriteetti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Palvelimen tyyppi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "Palvelintyypin info"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "Server error"
msgstr "Palvelinvirhe"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"Palvelin vastasi seuraavalla poikkeusviestillä:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "Palvelimen tyyppi IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "Palvelimen tyyppi POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr ""
"Aseta aktiivisuus arvoon \"False\", jotta kanava voidaan piilottaa ilman sen"
" poistamista."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "Asetukset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "SFU-kanavan UUID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "SFU-palvelimen URL"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Share Screen"
msgstr "Jaa näyttö"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "Lyhyt kuvaus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Pikakoodit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Oikotie"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid ""
"Shortcut that will automatically be substituted with longer content in your "
"messages. Type ':' followed by the name of your shortcut (e.g. :hello) to "
"use in your messages."
msgstr ""
"Pikakuvake, joka korvataan automaattisesti pidemmällä sisällöllä "
"viesteissäsi. Kirjoita ':' ja sen jälkeen viesteissäsi käytettävän "
"pikakuvakkeen nimi (esim. :hello)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr "Lyhennetty kielikoodi, jota käytetään käännöspyynnön kohteena."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Attachments"
msgstr "Näytä liitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Show Call Settings"
msgstr "Näytä soiton asetukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Show Followers"
msgstr "Näytä seuraajat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Member List"
msgstr "Näytä jäsenlista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "Show a helper message"
msgstr "Näytä avustava viesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#, python-format
msgid "Show activities"
msgstr "Näytä aktiviteetit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Show all recipients"
msgstr "Näytä kaikki vastaanottajat"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Näytä kaikki tietueet joissa on toimenpide myöhässä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "Näytä vähemmän"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "Näytä lisää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Show sidebar"
msgstr "Näytä sivupalkki"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Show video participants only"
msgstr "Näytä vain videon osallistujat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Showing"
msgstr "Näytetään"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Sivupalkin toiminto"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Sivupalkin toiminto, jolla tämä malli saadaan käyttöön liittyvän "
"asiakirjamallin tietueissa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "Sivun nimi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "Ehkä voisit merkitä jotain GIF-kuvia suosikeiksi?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Lähde"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "Lähdekieli"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Tietty käyttäjä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""
"Määritä malli, jos toiminnon pitäisi olla mallikohtainen eikä se ole "
"käytettävissä, kun hallitaan muiden mallien toimintoja."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Tähdelliset"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "Tähdellä merkitty viesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Start a Call"
msgstr "Aloita puhelu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Start a Conversation"
msgstr "Aloita keskustelu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "Start a conversation"
msgstr "Aloita keskustelu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/web/discuss_sidebar_start_meeting.xml:0
#, python-format
msgid "Start a meeting"
msgstr "Aloita tapaaminen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "Alue"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Tila"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tila aktiviteetin perusteella\n"
"Myöhässä: Eräpäivä on menneisyydessä\n"
"Tänään: Eräpäivä on tänään\n"
"Suunniteltu: Tulevaisuudessa."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "Tila-aika"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
#, python-format
msgid "Status with time"
msgstr "Tila ajan myötä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "Askelten määrä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Stop Adding Users"
msgstr "Lopeta käyttäjien lisääminen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Stop Recording"
msgstr "Lopeta tallennus"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Stop Sharing Screen"
msgstr "Lopeta näytön jakaminen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Stop camera"
msgstr "Sulje kamera"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "Lopeta toisto"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr "Tallenna sähköpostit ja vastaukset jokaisen tietueen chatteriin"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "Tallenna linkin esikatselutiedot"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""
"Merkkijono, joka on muotoiltu edustamaan avainta tämän mallin mukaisilla "
"muokkauksilla: shift.ctrl.alt.key, esim.: truey.1.true.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "Käännöspyynnöstä saatu merkkijono."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Aihe"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "Aihe (voit käyttä täytettä)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Aihe:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "Tilaa vastaanottajat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Korvaus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Alityyppi"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Alityypit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "Ehdota"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "Ehdota seuraavaa toimintoa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "Ehdota näitä toimintoja kunnes nykyinen on merkitty valmiiksi."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "Yhteenveto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "Yhteenveto:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "Tukee Gmail-tunnistusta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "Tukee Outlook-tunnistusta"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Järjestelmäparametri"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Järjestelmän ilmoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "Kohdekieli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "Kohdemalli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "ALV-numero"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Technical Settings"
msgstr "Tekniset asetukset"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""
"Tekninen kenttä mallin seuraamiseksi muokkauksen alussa UX:hen liittyvän "
"toiminnan tukemiseksi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "Malli"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "Mallin kategoria"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "Mallin tiedostonimi"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Mallin esikatselu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "Mallin kielen esikatselu"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "Mallin nollaus mixin"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Template creation from composer requires a valid model."
msgstr "Mallien luominen editoijalta edellyttää kelvollista mallia."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template description"
msgstr "Mallin kuvaus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering should be called only using on a list of IDs; received "
"%(res_ids)r instead."
msgstr ""
"Mallin renderöintiä pitäisi kutsua vain käyttämällä ID-luetteloa; sen sijaan"
" saatiin %(res_ids)r."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""
"Mallin renderöinti tukee vain inline_template-, qweb- tai qweb_view- (view "
"tai raw) -muotoja; sen sijaan saatiin %(engine)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Mallit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "Tenor API-avain"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "Tenor GIF API-avain"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Tenor GIF-sisällön suodatin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Tenori GIF-rajat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Tenori Gif-raja"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Tenorin sisällön suodatin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "Testaa ja vahvista"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "Testiennätys:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "Tekstin sisältö"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "Eräpäivän arvo ei voi olla negatiivinen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
#, python-format
msgid "The Fullscreen mode was denied by the browser"
msgstr "Selain ei sallinut koko ruudun näyttötilan käyttöä"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""
"Verotunniste. Tässä olevat arvot validoidaan maamuodon perusteella. Voit "
"käyttää '/'-merkkiä osoittaaksesi, että kumppani ei ole verovelvollinen."

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The activity cannot be launched:"
msgstr "Toimintaa ei voida käynnistää:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"Toimintatyyppi \"%(activity_type_name)s\" ei ole yhteensopiva suunnitelman "
"\"%(plan_name)s\" kanssa, koska se rajoittuu malliin "
"\"%(activity_type_model)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "Liitettä %s ei ole olemassa, tai sinulla ei ole oikeuksia nähdä sitä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "Liitetiedostoa %s ei ole olemassa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "The avatar has been updated!"
msgstr "Avatar on päivitetty!"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "Kanavan UUID:n täytyy olla ainutlaatuinen"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "Kanavan tyyppi ei voi olla tyhjä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "Nykyinen käyttäjä voi muokata mallia."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "The duration of voice messages is limited to 1 minute."
msgstr "Ääniviestien kesto on rajoitettu 1 minuuttiin."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Sähköposti lähetetty"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_partner_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "Päätepisteen on oltava yksilöllinen !"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
#, python-format
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"Seuraavia sähköpostimalleja ei voitu nollata, koska niihin liittyviä lähdetiedostoja ei löytynyt:\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "Tästä yhteyshenkilöstä vastaava sisäinen käyttäjä."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr ""
"Viimeisin tällä aliaksella vastaanotettu viesti on aiheuttanut virheen."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "Osoite ei voinut hyväksyä alla olevaa viestiä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"Osoite %(alias_display_name)s ei hyväksynyt alla olevaa viestiä.\n"
"                 Vain %(contact_description)s saa ottaa yhteyttä.<br /><br />\n"
"                 Varmista, että käytät oikeaa osoitetta, tai ota sen sijaan yhteyttä osoitteeseen %(default_email)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"Osoite %(alias_display_name)s ei voinut hyväksyä alla olevaa viestiä.\n"
"Yritä myöhemmin uudelleen tai ota yhteyttä %(company_name)s."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Malli (Odoo Document Kind), jota tämä alias vastaa. Kaikki saapuvat "
"sähköpostiviestit, jotka eivät vastaa olemassa olevaan tietueeseen, "
"aiheuttavat uuden tietueen luomisen tähän malliin (esim. projektitehtävä)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Sähköpostin aliaksen nimi, esim. 'jobs', jos haluat saada sähköposteja "
"osoitteeseen <<EMAIL>>"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "Suunnitelmaa \"%(plan_name)s\" ei voida käynnistää:"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "Suunnitelma \"%(plan_name)s\" on käynnistetty"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "Jonokäsittelijä lähettää sähköpostin päivämäärän jälkeen"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The records must belong to the same company."
msgstr "Tietueiden on kuuluttava samaan yritykseen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Pyydettyä toimintoa ei voida suorittaa tietoturvarajoitusten takia. Ole hyvä ja ota yhteyttä ylläpitoon.\n"
"\n"
"(Dokumentin tyyppi: %s, Operaatio: %s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "Palvelinta \"%s\" ei voi käyttää, koska se on arkistoitu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "Tilausasetusten käyttöönotto onnistui."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "Malli kuuluu tälle käyttäjälle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr "Sähköpostin esikatseluna käytetyn tekstiosan alku."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "Tässä keskustelussa ei ole viestejä."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "Kanavan jäsentä kohden voi olla vain yksi RTC-istunto"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Tämä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action can only be done on a mail thread models"
msgstr "Tämä toiminto voidaan tehdä vain sähköpostisäikeen malleissa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action cannot be done on transient models."
msgstr "Tätä toimintoa ei voi tehdä transienttimalleissa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Tämä toiminto lähettää sähköpostin."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This channel doesn't have any attachments."
msgstr "Tällä kanavalla ei ole liitetiedostoja."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This channel doesn't have any pinned messages."
msgstr "Tällä kanavalla ei ole kiinnitettyjä viestejä."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This conversation doesn't have any attachments."
msgstr "Tässä keskustelussa ei ole liitetiedostoja."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This conversation doesn't have any pinned messages."
msgstr "Tässä keskustelussa ei ole kiinnitettyjä viestejä."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Tämä sähköposti on massapostitusten mustalla listalla. Klikkaa poistaaksesi "
"mustalta listalta."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Tämä kenttä on kirjainkoosta riippumaton."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr "Tätä kenttää käytetään mallin käytön sisäiseen kuvaukseen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Tätä kenttää käytetään sähköpostiosoitteiden hakuun, koska ensisijainen "
"sähköpostiosoite voi sisältää muutakin kuin vain sähköpostiosoitteen."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "This layout seems to no longer exist."
msgstr "Näkymää ei näytä olevan enää olemassa."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Tämä vaihtoehto poistaa pysyvästi kaikki sähköpostiviestijäljet lähettämisen"
" jälkeen, mukaan lukien Asetutusten Tekniset-valikosta Odoo-tietokannan "
"tallennustilan säilyttämiseksi."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
#, python-format
msgid "This record has an exception activity."
msgstr "Tässä tietueessa on poikkeuksellista toimintaa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr "Näitä arvoja ei tueta vaihtoehtoina renderöinnissä: %(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr ""
"Näitä arvoja ei tueta lähetettäessä tai ilmoitettaessa: %(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Viestit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Thread Image"
msgstr "Viestiketjun kuva"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "Säie-aktivoitu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Aikavyöhyke"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "Otsikko"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "Päättyy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Vastaanottaja (sähköpostit)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Vastaanottaja (kumppanit)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
#, python-format
msgid "To :"
msgstr "Vastaanottaja :"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "To peer:"
msgstr "Vertaisarviointiin:"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "Tehtävälista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "To:"
msgstr "Vastaanottajat:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#, python-format
msgid "Today"
msgstr "Tänään"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Tämän päivän toimenpiteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Today:"
msgstr "Tänään:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Tomorrow"
msgstr "Huomenna"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Tomorrow:"
msgstr "Huomenna:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Aiheet tämän ryhmän keskusteltavaksi..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "Seuraa vastaanottajia"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""
"Seuratut arvot tallennetaan erilliseen malliin. Tämän kentän avulla voidaan "
"rekonstruoida seuranta ja luoda tilastoja mallista."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Seuranta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Seuranta-arvo"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Seuranta-arvot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Seuranta-arvot"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Translate"
msgstr "Käännä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "Käännösrunko"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Translation Failure"
msgstr "Käännös epäonnistui"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Liipaisin"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "Laukaise seuraava toimenpide"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "Yritä uudelleen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Turn camera on"
msgstr "Kytke kamera päälle"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Twilio-tilin tunnuskoodi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio-tilin SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Tyyppi"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Viiveen tyyppi"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"Palvelimen toimintatyyppi. Käytettävissä ovat seuraavat arvot:\n"
"- päivitä tietue: päivitä tietueen arvot\n"
"- 'Create Activity': luo toiminnon (Keskustele)\n"
"- 'Lähetä sähköposti': lähettää viestin, huomautuksen tai sähköpostin (Keskustele)\n"
"- 'Lähetä tekstiviesti': lähetä tekstiviestejä, kirjaa ne asiakirjoihin (tekstiviesti)- 'Lisää/poista seuraajia': lisää tai poista seuraajia tietueeseen (Keskustele)\n"
"- 'Luo tietue': luo uusi tietue uusilla arvoilla\n"
"- 'Execute Code': Python-koodin lohko, joka suoritetaan\n"
"- 'Send Webhook Notification': lähetä POST-pyyntö ulkoiseen järjestelmään, joka tunnetaan myös nimellä Webhook\n"
"- 'Execute Existing Actions': Määritä toiminto, joka käynnistää useita muita palvelintoimintoja\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Poikkeusaktiviteetin tyyppi tietueella."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "Kirjoita henkilön nimi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "SMTP-palvelimeen ei saada yhteyttä"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Viestin lähettäminen ei onnistu, määritä lähettäjän sähköpostiosoite."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to send message, please configure the sender's email address."
msgstr "Viestiä ei voi lähettää, määritä lähettäjän sähköpostiosoite."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign"
msgstr "Ei vastuutettu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign from me"
msgstr "Siirrä pois minulta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "Poista sähköpostin markkinointikiellosta"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr "Poissulkemisen poistamisen syy: %(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Undeafen"
msgstr "Poista äänentoiston poiskytkentä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#, python-format
msgid "Unfollow"
msgstr "Älä seuraa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Viiveen yksikkö"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
#, python-format
msgid "Unknown"
msgstr "Tuntematon"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Tuntematon virhe"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Unmute"
msgstr "Poista mykistys"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Unmute Channel"
msgstr "Kanavan mykistyksen poistaminen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Unpin"
msgstr "Poista kiinnitys"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "Poista keskustelun kiinnitys"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Unpin Message"
msgstr "Poista viestin kiinnitys"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Lukemattomien viestien laskuri"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Lukemattomat viestit"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "Rajoittamattomat liitteet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Unstar all"
msgstr "Poista kaikki tähtimerkinnät"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Tukematon raporttityyppi %s löytynyt."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.js:0
#, python-format
msgid "Until "
msgstr "Kunnes "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "Until I turn it back on"
msgstr "Kunnes kytken sen takaisin päälle"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "Päivitä sähköpostin asettelua"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Upload Avatar"
msgstr "Lataa avatar"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "Lataa dokumentti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload File"
msgstr "Lataa tiedosto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload file"
msgstr "Lataa tiedosto"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Upload:"
msgstr "Lähetä:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploaded"
msgstr "Ladattu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploading"
msgstr "Tiedosto latautuu"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""
"Käyttämällä 'Tietty käyttäjä' voit määrittää aina saman käyttäjän seuraavaan"
" toimintoon. Käytä 'Dynaaminen käyttäjä' -vaihtoehtoa määrittääksesi "
"käyttäjän kentän nimen, joka valitaan tietueeseen."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "Käytä mukautettuja sähköpostipalvelimia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "Käytä Twilio ICE -palvelimia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "Käytä Gmail-palvelinta"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Use a local script to fetch your emails and create new records."
msgstr ""
"Käytä paikallista skriptiä sähköpostien hakemiseen ja uusien tietueiden "
"luomiseen."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "Käytä Outlook-palvelinta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "Käytä eri verkkotunnuksia sähköpostialiaksille"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "Käyttö erässä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Use message_notify to send a notification to an user."
msgstr "Käytä message_notify-toimintoa lähettääksesi ilmoituksen käyttäjälle."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Käytä mallia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "Puhu tangentia painamalla"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "Käytössä"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr ""
"Käytetään kontekstina, jota käytetään muokkaajan toimialueen arvioinnissa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"Käytetään viestigeneraattorin luokitteluun\n"
"'email': syntyy saapuvan sähköpostin, esim. mailgatewayn, tuloksena\n"
"'kommentti': syntyy käyttäjän syötteestä esim. discuss- tai composer-ohjelmassa\n"
"'email_outgoing': syntyy postituksesta\n"
"'notification': järjestelmän tuottama esim. seurantaviestit\n"
"'auto_comment': automaattisen ilmoitusmekanismin tuottama, esim. kuittaus\n"
"'user_notification': luodaan tietylle vastaanottajalle"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "Käytetään valuutan näyttämiseen, kun seurataan raha-arvoja"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Käytetään alityyppien järjestämiseen."

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Käyttäjä"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "Käyttäjäkenttä"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Käyttäjän läsnäolo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "Käyttäjän asetus"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "Käyttäjäasetukset"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "Käyttäjän asetukset: äänenvoimakkuudet"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "Käyttäjäkohtainen ilmoitus"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "Käyttäjäprofiili"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is a bot"
msgstr "Käyttäjä on robotti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is idle"
msgstr "Käyttäjä on toimettomana"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is offline"
msgstr "Käyttäjä ei ole aktiivinen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is online"
msgstr "Käyttäjä on aktiivinen"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "Käyttäjällä ei pitäisi olla päällekkäisiä suosikki-GIF:ejä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Käyttäjä:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Käyttäjänimi"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "Tämän kanavan käyttäjät: %(members)s %(dots)s ja sinä."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"Käyttäjät voivat edelleen renderöidä malleja.\n"
"Kuitenkin vain sähköpostimallien muokkaajat voivat luoda uusia dynaamisia malleja tai muokata olemassa olevia malleja."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Sähköpostien lähettäminen/vastaanottaminen Community- ja Enterprise-"
"versioissa edellyttää oman sähköpostipalvelimen käyttöä. Online-käyttäjät "
"hyötyvät jo valmiista sähköpostipalvelimesta (@mycompany.odoo.com)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "Vahvistettu"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"Arvoa %(allowed_domains)s arvolle `mail.catchall.domain.allowed` ei voida vahvistaa.\n"
"Sen tulisi olla pilkulla erotettu luettelo verkkotunnuksista, esim. example.com,example.org."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Video Settings"
msgstr "Videoasetukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Video player:"
msgstr "Videosoitin:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#, python-format
msgid "View"
msgstr "Näytä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Katso %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "View Reactions"
msgstr "Näytä reaktiot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Näkymän tyyppi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "View or join channels"
msgstr "Kanavien katselu tai niihin liittyminen"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Ääni"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Voice Message"
msgstr "Ääniviesti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Voice Settings"
msgstr "Ääniasetukset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Voice detection threshold"
msgstr "Puheentunnistuskynnys"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "Voice recording stopped"
msgstr "Äänen tallennus pysähtyi"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Tilavuus"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "Volyymi kumppania kohti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "Muiden kumppaneiden volyymit"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""
"Haluatko piristää keskustelujasi GIF-kuvilla? Aktivoi ominaisuus "
"asetuksista!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
#, python-format
msgid "Warning"
msgstr "Varoitus"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"Emme voineet luoda aliasta %(alias_name)s, koska verkkotunnus "
"%(alias_domain_name)s kuuluu yritykselle %(alias_company_names)s, kun taas "
"omistaja-asiakirja kuuluu yritykselle %(company_name)s."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"Emme voineet luoda aliasta %(alias_name)s, koska verkkotunnus "
"%(alias_domain_name)s kuuluu yritykselle %(alias_company_names)s, kun taas "
"kohdeasiakirja kuuluu yritykselle %(company_name)s."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Viikot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "Tervetuloa MyCompanyyn!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr "Haluatko varmasti poistaa tämän viestin kiinnityksen?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "What's your name?"
msgstr "Mikä sinun nimesi on?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""
"Kun valitset \"Oletuskäyttäjä\"-määrityksen, sinun on määritettävä "
"vastuuhenkilö."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Säilytetäänkö koko alkuperäisen sähköpostin kopio viitteeksi ja liitetään "
"jokaiseen käsiteltyyn säköpostiin. Tämä useimmiten tuplaa viestitietokannan "
"koon."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Ladataanko sähköpostien liitteet. Jos tämä ei ole valittuna, saapuvien "
"sähköpostien liitteet jätetään tuomatta järjestelmään"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "Näytetäänkö kaikki vastaanottajat vai vain tärkeät."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Kirjoita muistiinpanot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Write your message here..."
msgstr "Kirjoita viestisi tähän..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "Väärä operaation nimi (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yeah, pin it!"
msgstr "Kiinnitä se."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Yes"
msgstr "Kyllä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yes, remove it please"
msgstr "Kyllä, poista se"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Yesterday"
msgstr "Eilen"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Yesterday:"
msgstr "Eilen:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""
"Olet poistumassa tästä ryhmäkeskustelusta etkä voi enää käyttää sitä, ellei "
"sinua kutsuta uudelleen. Oletko varma, että haluat jatkaa?"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in a private conversation."
msgstr "Olet yksin yksityisessä keskustelussa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "Olet yksin kanavalla."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in a private conversation with %(member_names)s."
msgstr "Olet yksityisessä keskustelussa %(member_names)s kanssa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "Olet kanavalla %(bold_start)s#%(channel_name)s%(bold_end)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_service.js:0
#, python-format
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "Et enää seuraa \"%(thread_name)s\"."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "Sinulla ei ole oikeuksia ladata liitettä tähän."

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "Tällä kanavalla ei saa ladata liitetiedostoja."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "Olet tämän kanavan ylläpitäjä. Oletko varma, että haluat poistua?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Voit merkitä minkä tahansa viestin tähdellä, jolloin se näkyy tässä "
"postilaatikossa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "Et voi kirjoittaa kohtaan %(field_name)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "Voit keskustella vain olemassa olevien käyttäjien kanssa."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr ""
"Voit keskustella vain sellaisten kumppanien kanssa, joilla on oma käyttäjä."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "Voit huoletta jättää tämän viestin huomiotta"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Näitä ryhmiä ei voi poistaa, koska muut moduulit tarvitsevat Koko yritys "
"-ryhmää."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""
"Alias-osoitteessa %(alias_name)s ei voi käyttää muita kuin korostamattomia "
"latinalaisia merkkejä."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""
"Verkkotunnuksessa ei voi käyttää muita kuin korostamattomia latinalaisia "
"merkkejä %(domain_name)s."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "Sinulla ei ole pääsyä malliin \"%s\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""
"Sinulla ei ole oikeutta poistaa sähköpostien mustaa listaa. Ota yhteyttä "
"järjestelmänvalvojaan."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Olet merkattu vastuulliseksi: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Vastuullesi on annettu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "Sinut on kutsuttu #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Voit liittää tähän mallipohjaan liitetiedostoja, jotka lähetetään tämän "
"mallin perusteella tehtyjen viestien liitteinä"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "You may not define a template on an abstract model: %s"
msgstr "Et voi määritellä mallia abstraktille mallille: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr "Haluatko kiinnittää tämän viestin %(conversation)s pysyvästi?"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unpinned your conversation with %s"
msgstr "Olet poistanut keskustelun kiinnityksesi %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "Poistuit tilauksesta %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "You're viewing older messages"
msgstr "Katselet vanhempia viestejä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "Sinut on kutsuttu keskusteluun!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "Sinut on kutsuttu tapaamiseen!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "You:"
msgstr "Sinä:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "Sinulle kuuluva"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr ""
"Tilisi sähköpostiosoite on muutettu %(old_email)s:sta %(new_email)s:ksi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account login has been updated"
msgstr "Tilisi kirjautumistiedot on päivitetty"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account password has been updated"
msgstr "Tilisi salasana on päivitetty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "Selaimesi ei tue videoneuvottelua"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "Selaimesi ei tue ääniaktivointia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "Selaimesi ei tue webRTC:tä."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your name"
msgstr "Nimesi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
#, python-format
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (ei sähköpostiosoitetta)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "rekisteröityihin kumppaneihin liittyvät osoitteet"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "valmistumispäivän jälkeen"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "edellisen toimenpiteen määräajan jälkeen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "alias %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "tämän sähköpostin liitetiedosto(t)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "available bitrate:"
msgstr "käytettävissä oleva yhteysnopeus:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "back"
msgstr "takaisin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"koska olette\n"
"                ottanut siihen yhteyttä liian monta kertaa viime minuuttien aikana.\n"
"               <br/>\n"
"                Yritä myöhemmin uudelleen."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "kirjoittaja"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "camera"
msgstr "kamera"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"ei voida käsitellä. Tätä osoitetta\n"
"    käytetään vastausten keräämiseen, eikä sitä voi käyttää suoraan yhteydenottoihin"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "channels"
msgstr "kanavat"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "clock rate:"
msgstr "kellotaajuus:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "close"
msgstr "sulkemista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "codec:"
msgstr "koodekki:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "created this channel."
msgstr "loi tämän kanavan."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "päivää"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days overdue:"
msgstr "päivää myöhässä:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days:"
msgstr "päivää:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "kuurot"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__user_device
msgid "devices"
msgstr "laitteet"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "dokumentti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
#, python-format
msgid "done"
msgstr "valmis"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down DTLS:"
msgstr "alas DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down ICE:"
msgstr "alas ICE:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "esim. keskustele tarjouksesta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "esim. \"Käykää läpi tarjous ja keskustelkaa yksityiskohdista.\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "esim. \"Tervetulosähköposti\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr "esim. \"Tervetuloa MyCompanyyn\" tai \"Hauska tavata, {{ object.name }}\".\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "esim. \"bounce\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "esim. \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "esim. \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "esim. \"notifications\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "e.g. 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "esim. yhteystiedot"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "esim. keskustele tarjouksesta"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "esim. alkuunpääsy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "esim. Aikatauluta kokous"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. domain.com"
msgstr "esim. domain.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "esim. tuki"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "esim. true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "esim. user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "esim: \"<EMAIL>\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "for"
msgstr "tekijänä"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "on luotu lähteestä:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "on muutettu:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "on juuri antanut sinulle seuraavan tehtävän:"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "virheellisesti konfiguroitu alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "virheellisesti konfiguroitu alias (tuntematon tietueviittaus)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "invited %s to the channel"
msgstr "kutsuttu %s kanavalle"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "joined the channel"
msgstr "liittyi kanavaan"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "left the channel"
msgstr "lähti kanavalta"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list"
msgstr "lista"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list-item"
msgstr "list-item"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "live"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "media player Error"
msgstr "mediasoittimen virhe"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "microphone"
msgstr "mikrofoni"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "malli %s ei salli dokumentin luontia"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "kuukaudet"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "ms"
msgstr "ms"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "mykistetty"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "new"
msgstr "uusi"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "no connection"
msgstr "ei yhteyttä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
#, python-format
msgid "now"
msgstr "nyt"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "päiväyksellä"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "on:"
msgstr "jossa:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#, python-format
msgid "or"
msgstr "tai"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "muut jäsenet."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "raising hand"
msgstr "käden nostaminen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""
"vastaus puuttuvaan asiakirjaan (%(model)s,%(thread)s), palaa asiakirjan "
"luomiseen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""
"vastaus malliin %s, ei hyväksy asiakirjan päivitystä, palaa asiakirjan "
"luomiseen"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "rajoitettu seuraajille"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "rajoitettu tunnetuille kirjoittajille"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "results out of"
msgstr "tulokset"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "screen"
msgstr "näyttö"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "joitakin erityisiä osoitteita"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "tainnuttaa:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "kohdemallia ei ole määritelty"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "tiimi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "template"
msgstr "malli"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "vaihda tangettitilaa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "käännä:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "tuntematon virhe"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "tuntematon kohdemalli %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up DTLS:"
msgstr "ylös DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up ICE:"
msgstr "ylös ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "users"
msgstr "käyttäjät"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "view"
msgstr "näytä"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "websocket-viestien käsittely"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "viikkoa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "your alias"
msgstr "aliaksesi"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/autoresize_input.xml:0
#, python-format
msgid "{{ props.placeholder }}"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "\"%(member_name)s\" kohdassa \"%(channel_name)s\""
