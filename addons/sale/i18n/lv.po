# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale
# 
# Translators:
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON> <arn<PERSON>@allegro.lv>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <AUTHOR> <EMAIL>, 2023
# <PERSON><PERSON><PERSON>, 2023
# Lancelot Semal, 2024
# Will Sensors, 2024
# <PERSON><PERSON><PERSON>lta<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:28+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Iegūtie dati"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr "# Pārdošanas pasūtījumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "Rindu #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr "# Pārdošanas pasūtījumi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "%(attribute)s: %(values)s"
msgstr "%(attribute)s: %(values)s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "%(line_description)s (Canceled)"
msgstr "%(line_description)s (Atcelts)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "%(line_description)s (Draft)"
msgstr "%(line_description)s (Projekts)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "%(line_description)s (ref: %(reference)s on %(date)s)"
msgstr "%(line_description)s (atsauce: %(reference)s uz %(date)s)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"%)\n"
"                                            for this proposal, I agree to the following terms:"
msgstr ""
"%)\n"
"                                            attiecībā uz šo priekšlikumu es piekrītu šādiem noteikumiem:"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "%s has been created"
msgstr "%s ir izveidots"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr "'Priekšapmaksa - %s' % (object.name)"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid ""
"(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or "
"'Order - %s' % (object.name)"
msgstr ""
"(object.state in (\"draft\", \"sent\") and \"Piedāvājumu - %s\" % "
"(object.name)) or \"Pasūtījumu - %s\" % (object.name)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "2023-12-31"
msgstr "2023-12-31"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "27.00"
msgstr "27.00"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "31.05"
msgstr "31.05"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"<b>Congratulations</b>, your first quotation is sent!<br>Check your email to"
" validate the quote."
msgstr ""
"<b>Apsveicam,</b> jūsu pirmais piedāvājums ir nosūtīts!<br>Pārbaudiet savu "
"e-pastu, lai apstiprinātu piedāvājumu."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr "<b>Nosūtiet piedāvājumu</b> sev un pārbaudiet, ko saņems klients."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "<b>Set a price</b>."
msgstr "<b>Noteikt cenu</b>."

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_payment_executed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"></t>\n"
"        Hello,\n"
"        <br><br>\n"
"        A payment with reference\n"
"        <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        amounting\n"
"        <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        for your order\n"
"        <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"        <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            is pending.\n"
"            <br>\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Your order will be confirmed once the payment is confirmed.\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Once confirmed,\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                will remain to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            has been confirmed.\n"
"            <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"                <br>\n"
"                <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                remains to be paid.\n"
"            </t>\n"
"        </t>\n"
"        <br><br>\n"
"        Thank you for your trust!\n"
"        <br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"       <t t-set=\"transaction_sudo\" t-value=\"object.get_portal_last_transaction()\"></t>\n"
"        Sveiki,\n"
"       <br><br>\n"
"        Maksājums ar atsauci\n"
"       <span style=\"font-weight:bold;\" t-out=\"transaction_sudo.reference or ''\">SOOO49</span>\n"
"        summa\n"
"       <span style=\"font-weight:bold;\" t-out=\"format_amount(transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"        par jūsu pasūtījumu\n"
"       <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span>\n"
"       <t t-if=\"transaction_sudo and transaction_sudo.state == 'pending'\">\n"
"            tiek gaidīts.\n"
"           <br>\n"
"           <t t-if=\"object.currency_id.compare_amounts(object.amount_paid + transaction_sudo.amount, object.amount_total) &gt;= 0 and object.state in ('draft', 'sent')\">\n"
"                Jūsu pasūtījums tiks apstiprināts, tiklīdz būs apstiprināts maksājums.\n"
"           </t>\n"
"           <t t-else=\"\">\n"
"                Pēc apstiprināšanas,\n"
"               <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid - transaction_sudo.amount, object.currency_id) or ''\">$ 10.00</span>\n"
"                vēl būs jāsamaksā.\n"
"           </t>\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"            ir apstiprināts.\n"
"           <t t-if=\"object.currency_id.compare_amounts(object.amount_paid, object.amount_total) &lt; 0\">\n"
"               <br>\n"
"               <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total - object.amount_paid, object.currency_id) or ''\">$ 10.00</span>\n"
"                vēl ir jāsamaksā.\n"
"           </t>\n"
"       </t>\n"
"       <br><br>\n"
"        Paldies par uzticēšanos!\n"
"       <br>\n"
"        Ja jums ir kādi jautājumi, sazinieties ar mums.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell admin</t>\n"
"        </t>\n"
"       <br><br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"></t>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"></span>.\n"
"            </t>\n"
"        </t>\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td></td>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            VAT Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            VAT Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\">\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">SubTotal:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">SubTotal:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"></t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"></t>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"></t>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"></t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Labdien,\n"
"        <br><br>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"></t>\n"
"        Jūsu pasūtījums <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> par summu <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            ir apstiprināts.<br>\n"
"            Paldies par pasūtījumu!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"           tiek gatavots. Tiks apstiprināts, kad saņemsim maksājumu.\n"
"            <t t-if=\"object.reference\">\n"
"                Atsauce maksājumam ir <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"></span>.\n"
"            </t>\n"
"        </t>\n"
"        <br><br>\n"
"        Dodiet ziņu, ja ir nepieciešamas izmaiņas vai konsultācija.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Produkti</span></td>\n"
"                <td></td>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Daudzums</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            bez PVN\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            Ar PVN.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Zaļais kurss</span>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Zaļais kurss</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\">\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tZaļais kurss</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Piegāde:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Starpsumma:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Starpsumma:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">PVN:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Kopā:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Kam:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Maksājuma veids:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"></t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"></t>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br>\n"
"                    <span style=\"font-weight:bold;\">Nosūtīt uz:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Piegādes veids:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"></t>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Piegādes apraksts:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"></t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"        Hello,\n"
"        <br><br>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"       <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"        Sveiki,\n"
"       <br><br>\n"
"        Jūsu\n"
"       <t t-if=\"ctx.get('proforma')\">\n"
"            Proforma rēķins <t t-out=\"doc_name or ''\">piedāvājumam</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"           <t t-if=\"object.origin\">\n"
"                (ar atsauci: <t t-out=\"object.origin or ''\"></t> )\n"
"           </t>\n"
"            ir pieejams rēķins <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\"> 10,00 $</span> apmērā.\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"           <t t-out=\"doc_name or ''\">piedāvājums</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"           <t t-if=\"object.origin\">\n"
"                (ar atsauci: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"           </t>\n"
"            summa <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\"> 10,00 $</span> ir gatava izskatīšanai.\n"
"       </t>\n"
"       <br><br>\n"
"        Ja jums ir kādi jautājumi, sazinieties ar mums.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell admin</t>\n"
"        </t>\n"
"       <br><br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"></t>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br><br>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"></t>\n"
"        Sveiki <t t-out=\"object.partner_id.name or ''\">lietotāj </t>,\n"
"        <br><br>\n"
"        Mēs informējam, ka\n"
"        <t t-out=\"doc_name or ''\">piedāvājums</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (ar atsauci: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        ir atcelts. Netiek piestādīta samaksa.\n"
"       Ja būs nepieciešama atmaksa, mēs to veiksim.\n"
"        <br><br>\n"
"        Dodiet ziņu, ja ir kādi jautājumi.\n"
"        <br>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""
"<i class=\"fa fa-comment\"/> Sazinieties ar mums, lai saņemtu jaunu "
"piedāvājumu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Atsauksme"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr ""
"<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" "
"title=\"Done\"/>Pabeigts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Authorized"
msgstr "<i class=\"fa fa-fw fa-check\"/> Autorizēts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Paid"
msgstr "<i class=\"fa fa-fw fa-check\"/> Apmaksāts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> Reversed"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Termiņš iztecējis"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Waiting Payment"
msgstr "<i class=\"fa fa-fw fa-clock-o\"/> Maksājuma gaidīšana"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr "<i class=\"fa fa-fw fa-remove\"/> Atcelts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" "
"title=\"Sales orders\"/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                    Locked"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                    Slēgts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> View Details"
msgstr "<i class=\"fa fa-print\"/> Skatīt informāciju"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Atcelt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small class=\"text-muted\">Your contact</small>"
msgstr "<small class=\"text-muted\">Jūsu kontakts</small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr "<small><b class=\"text-muted\">Jūsu ieguvums</b></small>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""
"<span class=\"d-none d-md-inline\">Klienta pasūtījums #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr "<span class=\"flex-grow-1\">/ Mēnesis</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span class=\"mx-3\" invisible=\"not require_payment\">of</span>"
msgstr "<span class=\"mx-3\" invisible=\"not require_payment\">no</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr "<span class=\"o_stat_text\">Pārdots</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span invisible=\"advance_payment_method != 'percentage'\" class=\"oe_inline\">% </span>\n"
"                        <span invisible=\"not display_invoice_amount_warning\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""
"<span invisible=\"advance_payment_method != 'percentage'\" class=\"oe_inline\">% </span>\n"
"                        <span invisible=\"not display_invoice_amount_warning\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr "<span>Apstprināja:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Amount</span>"
msgstr "<span>Summa</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""
"<span>Vai esat pārliecināts, ka vēlaties atcelt šo pasūtījumu? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" invisible=\"not display_invoice_alert\">\n"
"                            Šī pasūtījuma rēķinu projekti tiks anulēti. <br/>\n"
"                        </span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""
"<span>Parakstot šo piedāvājumu, es piekrītu sekojošiem nosacījumiem::</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr "<span>Atl.%</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr "<span>Summai par:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr "<span>Nodokļi</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_kanban
msgid "<span>Visibility</span>"
msgstr "<span>Redzamība</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr "<span>Ar maksājuma nosacījumiem:</span>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Starpsumma</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong><br/>"
msgstr "<strong>Derīguma termiņš:</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Piezīme par fiskālo stāvokli:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        If you believe that it is an error, please contact the website administrator."
msgstr ""
"<strong>Nevar atrast piemērotu maksājumu.</strong><br/>\n"
"                                        Ziņot par šo kļūdu tīmekļa vietnes adiministratoram."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong><br/>"
msgstr "<strong>Pārdevējs:</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr "<strong>Piegādes adrese:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr "<strong>Paraksts</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<strong>Tax excl.: </strong>"
msgstr "<strong>Bez PVN.: </strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<strong>Tax incl.: </strong>"
msgstr "<strong>Iesk. PVN: </strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr "<strong>Paldies!</strong><br/>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr "<strong>Piedāvājuma derīguma termiņš beidzies!</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr "<strong>Piedāvājums ir atcelts.</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong><br/>"
msgstr "<strong>Jūsu atsauce:</strong><br/>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr "Apstiprinātam pasūtījumam nepieciešams apstiprināšanas datums."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A note, whose content usually applies to the section or product above."
msgstr ""
"Piezīme, kuras saturs parasti attiecas uz iepriekš minēto sadaļu vai "
"produktu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "A section title"
msgstr "Sadaļas nosaukums"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid ""
"A standard invoice is issued with all the order lines ready for "
"invoicing,according to their invoicing policy (based on ordered or delivered"
" quantity)."
msgstr ""
"Standarta rēķins tiek izrakstīts ar visām rēķina izrakstīšanai sagatavotajām"
" pasūtījuma rindiņām saskaņā ar rēķinu izrakstīšanas politiku (pamatojoties "
"uz pasūtīto vai piegādāto daudzumu)."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""
"Uzglabājama prece ir prece, kurai Jūs pārvaldat atlikumu. Inventāra lietotnei jābūt uzstādītai.\n"
"Patērējama prece ir prece, kurai atlikums nav pārvaldams.\n"
"Pakalpojums ir nemateriāla prece, kuru Jūs nodrošinat."

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Var tikt iestatīts brīdinājums klientam vai produktam (Tirdzneicība)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."
msgstr ""
"Ability to select a package type in sales orders and to force a quantity "
"that is a multiple of the number of units per package."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Accept & Pay Quotation"
msgstr "Apstipriniet un apmaksājiet piedāvājumu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Accept & Sign Quotation"
msgstr "Apstipriniet un parakstiet piedāvājumu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr "Pieņemt & amp; Maksāt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr "Accept &amp; Sign"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr "Piekļuves brīdinājums"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""
"Atbilstoši produkta konfigurācijai piegādājamo daudzumu var automātiski aprēķināt ar mehānisma palīdzību:\n"
"  - Manuāli: daudzums tiek iestatīts manuāli uz līnijas\n"
"  - Analītisks No izdevumiem: daudzums ir iegrāmatoto izdevumu daudzuma summa\n"
"  - Laika grafiks: daudzums ir to stundu summa, kas reģistrētas uzdevumos, kuri saistīti ar šo pārdošanas pozīciju\n"
"  - Krājumu pārvietošana: daudzums tiek iegūts no apstiprinātās komplektācijas\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Konta numurs"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "Account used for deposits"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr "Uzkrāto ieņēmumu ieraksts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "Aktivitātes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitātes izņēmuma noformējums"

#. module: sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_plan
msgid "Activity Plans"
msgstr "Aktivitāšu plāni"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Aktivitātes stadija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitātes veida ikona"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Aktivitāšu tipi"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.mail_activity_plan_action_sale_order
msgid "Add a new plan"
msgstr "Pievienot jaunu plānu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr "Pievienot piezīmi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr "Pievienot produktu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr "Pievienot sadaļu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr "Vairāku variantu pievienošana pasūtījumam no režģa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__allowed_so_line_ids
msgid "Allowed So Line"
msgstr "Atļauts Tātad līnija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr "Atļauj nosūtīt Avansa rēķinu klientiem"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr "Atļauj nosūtīt Avansa rēķinu."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_document__attached_on
msgid ""
"Allows you to share the document with your customers within a sale.\n"
"Leave it empty if you don't want to share this document with sales customer.\n"
"Quotation: the document will be sent to and accessible by customers at any time.\n"
"e.g. this option can be useful to share Product description files.\n"
"Confirmed order: the document will be sent to and accessible by customers.\n"
"e.g. this option can be useful to share User Manual or digital content bought on ecommerce. \n"
"Inside quote: The document will be included in the pdf of the quotation \n"
"and sale order between the header pages and the quote table. "
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__amount_paid
msgid "Already Paid"
msgstr "Jau samaksāts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr "Jau izrakstīts rēķins"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr "Amazon Sync"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_amount
msgid "Amount"
msgstr "Summa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Summa pirms atlaides"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_paid
msgid "Amount Paid"
msgstr "Samaksātā summa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr "Piedāvājumu summa izrakstīšanai"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
msgid "Amount to invoice"
msgstr "Rēķinā norādāmā summa"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""
"Pasūtījuma pieprādrošana ir palielināt pārdošanas apjomu, ja piegādātie daudzumi pārsniedz sākotnēji norādītos\n"
"            pasūtītajiem daudzumiem, un rēķinu izrakstīšanas politika ir balstīta uz pasūtītajiem daudzumiem."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "Analītiskais konts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analītiskais Sadalījums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Analītiskā izplatīšanas meklēšana"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr "Analītiskā no izdevumiem"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analītiskā rinda"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Analītiskā plāna piemērojamība"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analītiskā precizitāte"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Analītiskās rindas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Apply"
msgstr "Pielietot"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Apply manual discounts on sales order lines or display discounts computed "
"from pricelists (option to activate in the pricelist configuration)."
msgstr ""
"Piemērot manuālas atlaides pārdošanas pasūtījuma pozīcijām vai parādīt "
"atlaides, kas aprēķinātas no cenrāžiem (iespēja aktivizējama cenrāža "
"konfigurācijā)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr "Vai esat pārliecināts, ka vēlaties atcelt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected quotation?"
msgstr "Vai esat pārliecināts, ka vēlaties atcelt izvēlēto piedāvājumu?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Vai esat pārliecināts, ka vēlaties anulēt autorizēto darījumu? Šo darbību "
"nevar atsaukt."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""
"Piemēram, ja pārdodat iepriekš apmaksātas pakalpojumu stundas, Odoo iesaka\n"
"            pārdot papildu stundas, kad visas pasūtītās stundas ir izlietotas."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Pie izmaksas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr "Atribūta vērtība"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Attributes"
msgstr "Atribūti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr "Autors"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr "Autorizētie darījumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr "Automātisks rēķins"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Bacon Burger"
msgstr "Bekona burgeris"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Bankas nosaukums"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr "Pamatojoties uz klienta ID"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr "Pamatojoties uz dokumenta atsauci"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Bloķēšanas ziņa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Pamata saturs ir tāds pats kā veidnē"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"Palieliniet pārdošanas apjomu, izmantojot vairāku veidu programmas: Kuponi, "
"akcijas, dāvanu kartes, lojalitātes programmas. Var iestatīt īpašus "
"nosacījumus (produkti, klienti, minimālā pirkuma summa, periods). Atlīdzības"
" var būt atlaides (% or summa) vai bezmaksas produkti."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying this <u>down payment</u> of"
msgstr "Veicot šo <u>pirmo iemaksu</u>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "By paying this proposal, I agree to the following terms:"
msgstr "Apmaksājot šo piedāvājumu, es piekrītu šādiem noteikumiem:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_payment__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr "Kampaņa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr "Var rediģēt pamattekstu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr "Var rediģēt produktu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Atcelt"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Cancel %s"
msgstr "Atcelt %s"

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr "Atcelt vairākus piedāvājumus"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr "Atcelt piedāvājumus"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Atcelts"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Cannot create an invoice. No items are available to invoice.\n"
"\n"
"To resolve this issue, please ensure that:\n"
"   • The products have been delivered before attempting to invoice them.\n"
"   • The invoicing policy of the product is configured correctly.\n"
"\n"
"If you want to invoice based on ordered quantities instead:\n"
"   • For consumable or storable products, open the product, go to the 'General Information' tab and change the 'Invoicing Policy' from 'Delivered Quantities' to 'Ordered Quantities'.\n"
"   • For services (and other products), change the 'Invoicing Policy' to 'Prepaid/Fixed Price'.\n"
msgstr ""
"Nevar izveidot rēķinu. Rēķina izrakstīšanai nav pieejami nekādi priekšmeti.\n"
"\n"
"Lai atrisinātu šo problēmu, lūdzu, pārliecinieties, ka:\n"
"   - Pirms mēģinājuma izrakstīt rēķinu, produkti ir piegādāti.\n"
"   - produkta rēķinu izrakstīšanas politika ir konfigurēta pareizi.\n"
"\n"
"Ja vēlaties rēķinu izrakstīt, pamatojoties uz pasūtītajiem daudzumiem:\n"
"   - Attiecībā uz patērējamiem vai uzglabājamiem produktiem atveriet produktu, atveriet cilni \"Vispārīga informācija\" un mainiet \"Rēķinu izrakstīšanas politiku\" no \"Piegādātie daudzumi\" uz \"Pasūtītie daudzumi\".\n"
"   - Pakalpojumiem (un citiem produktiem) mainiet \"Rēķinu izrakstīšanas politiku\" uz \"Priekšapmaksa/fiksēta cena\".\n"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Saistīt darījumus"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr "Katalogs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr "Kategorija"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Changing the company of an existing quotation might need some manual "
"adjustments in the details of the lines. You might consider updating the "
"prices."
msgstr ""
"Lai mainītu esošā piedāvājuma uzņēmumu, var būt nepieciešami daži manuāli "
"pielāgojumi līniju detaļās. Jūs varētu apsvērt iespēju atjaunināt cenas."

#. module: sale
#: model:onboarding.onboarding.step,description:sale.onboarding_onboarding_step_sale_order_confirmation
msgid "Choose between electronic signatures or online payments."
msgstr "Izvēlieties elektronisko parakstu vai tiešsaistes maksājumus."

#. module: sale
#. odoo-python
#: code:addons/sale/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Choose how to confirm quotations"
msgstr "Izvēlieties, kā apstiprināt citātus"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Click here to add some products or services to your quotation."
msgstr ""
"Noklikšķiniet šeit, lai pievienotu dažus produktus vai pakalpojumus savam "
"piedāvājumam."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
#, python-format
msgid "Click to define an invoicing target"
msgstr "Noklikšķiniet, lai definētu rēķina izrakstīšanas mērķi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Aizvērt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "Saziņa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Communication history"
msgstr "Komunikācijas vēsture"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Uzņēmums"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Complete your company's data"
msgstr "Aizpildiet sava uzņēmuma datus"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "Aprēķināt sūtīšanas izmaksas un nosūtīt ar DHL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Aprēķiniet nosūtīšanas izmaksas un nosūtiet ar Easypost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "Aprēķināt sūtīšanas izmaksas un nosūtīt ar FedEx"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Aprēķiniet nosūtīšanas izmaksas un nosūtiet sūtījumu ar Sendcloud"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Aprēķiniet piegādes izmaksas un nosūtiet ar Shiprocket"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "Aprēķināt sūtīšanas izmaksas un nosūtīt ar UPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "Aprēķināt sūtīšanas izmaksas un nosūtīt ar USPS"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "Aprēķināt sūtīšanas izmaksas un nosūtīt ar bpost"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "Aprēķināt sūtīšanas izmaksas pasūtījumiem"

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Uzstādījumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Apstiprināt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_link_wizard__confirmation_message
msgid "Confirmation Message"
msgstr "Apstiprinājuma ziņojums"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on__sale_order
msgid "Confirmed order"
msgstr "Apstiprināts pasūtījums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr "Savienotāji"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__consolidated_billing
msgid "Consolidated Billing"
msgstr "Konsolidēti rēķini"

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "Kontakts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr "Saturs"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Mērvienību konvertēšana starp mērvienībām var notikt tikai tad, ja tās "
"pieder vienai un tai pašai kategorijai. Pārrēķins tiks veikts, pamatojoties "
"uz attiecībām."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr "Valsts kods"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr "Kuponi un lojalitāte"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr "Izveidošanas datums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft Invoice"
msgstr "Rēķina projekta izveide"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr "Izveidot rēķinu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr "Rēķinu izveide"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr "Izveidot klienta rēķinu"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Izveidot jaunu produktu"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Create a new quotation, the first step of a new sale!"
msgstr ""
"Izveidojiet jaunu cenu piedāvājumu, kas ir pirmais solis, lai veiktu jaunu "
"pārdošanu!"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr "Izveidot rēķinus"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid ""
"Create invoices, register payments and keep track of the discussions with "
"your customers."
msgstr ""
"Izveidojiet rēķinus, reģistrējiet maksājumus un sekojiet līdzi diskusijām ar"
" Jūsu klientiem."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__consolidated_billing
msgid ""
"Create one invoice for all orders related to same customer and same "
"invoicing address"
msgstr ""
"Izveidot vienu rēķinu visiem pasūtījumiem, kas saistīti ar vienu un to pašu "
"klientu un vienu un to pašu rēķina izrakstīšanas adresi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Izveidošanas datums"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""
"Projekta/nosūtīto rīkojumu izveides datums,\n"
"Apstiprināto rīkojumu apstiprināšanas datums."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Kredītkarte un debetkarte (izmantojot Stripe)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Valūta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Valūtas kurss"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Pielāgots"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr "Pielāgotās vērtības"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Pielāgotie maksājuma norādījumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Klients"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr "Klienta valsts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr "Klientu vienība"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr "Klienta industrija"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr "Klienta portāla URL"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Klienta Kods"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr "Klienta paraksts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__state_id
msgid "Customer State"
msgstr "Klienta valsts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "Klienta Nodokļi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_zip
msgid "Customer ZIP"
msgstr "Klienta ZIP"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
#, python-format
msgid "Customer needs to pay at least %(amount)s to confirm the order."
msgstr "Lai apstiprinātu pasūtījumu, klientam ir jāveic vismaz %(amount)s."

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_customer
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Klienti"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Customize your quotes and orders."
msgstr "Pielāgojiet savus piedāvājumus un pasūtījumus."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL Express savienotājs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Datums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Datums:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Days between quotation proposal and expiration. 0 days means automatic "
"expiration is disabled"
msgstr ""
"Dienas starp kotācijas priekšlikumu un termiņa beigām. 0 dienas nozīmē, ka "
"automātiskā termiņa beigas ir atspējotas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr "Atskaitiet priekšapmaksu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr "Noklusējuma kotācijas derīguma termiņš"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_discount_product_id
msgid "Default product used for discounts"
msgstr "Noklusējuma produkts, ko izmanto atlaidēm"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_down_payment_product_id
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for down payments"
msgstr "Produkts pēc noklusējuma, ko izmanto priekšapmaksājumiem"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default values"
msgstr "Noklusējuma vērtības"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Default values used when creating new quotations. Those can still be changed"
" on each quotation."
msgstr ""
"Noklusējuma vērtības, ko izmanto, veidojot jaunas kotācijas. Tās joprojām "
"var mainīt katrā citātā."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr "Satura piegāde pa e-pastu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr "Piegādāts"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Delivered Quantity: %s"
msgstr "Piegādātais daudzums: %s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Piegādātie daudzumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Piegādes adrese"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr "Piegādes datums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "Piegādes metodes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr "Piegāde Daudzums"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid ""
"Delivery date you can promise to the customer, computed from the minimum "
"lead time of the order lines in case of Service products. In case of "
"shipping, the shipping policy of the order will be taken into account to "
"either use the minimum or maximum lead time of the order lines."
msgstr ""
"Piegādes termiņš, ko varat apsolīt klientam, rēķinot no minimālā pasūtījuma "
"līniju izpildes termiņa, ja runa ir par servisa produktiem. Piegādes "
"gadījumā tiks ņemta vērā pasūtījuma piegādes politika, lai izmantotu "
"minimālo vai maksimālo pasūtījuma rindu izpildes termiņu."

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depozīts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_down_payment_product_id
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Depozīta produkts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Apraksts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr "Atl.%"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
msgid "Discard"
msgstr "Atmest"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_order_discount.py:0
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_wizard_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Discount"
msgstr "Atlaide"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr "Atlaide %"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Atlaide (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr "Atlaides lielums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_discount_product_id
msgid "Discount Product"
msgstr "Atlaides prece"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_type
msgid "Discount Type"
msgstr "Atlaides veids"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_discount
msgid "Discount Wizard"
msgstr "Atlaižu vednis"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Discount:"
msgstr "Atlaide:"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
#, python-format
msgid "Discount: %(percent)s%%"
msgstr "Atlaide: %(percent)s%%"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
#, python-format
msgid ""
"Discount: %(percent)s%%- On products with the following taxes %(taxes)s"
msgstr "Atlaide: %(percent)s%%- Produktiem ar šādiem nodokļiem %(taxes)s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "Atlaides, lojalitātes un dāvanu kartes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr "Rādīt rēķina projekta brīdinājumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr "Rādīt rēķina summas brīdinājumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Attēlotais nosaukums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr "Parādīt tipu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentācija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Documents"
msgstr "Dokumenti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domēns"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment"
msgstr "Avansa maksājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "Avansa maksājuma daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr "Avansa maksājuma daudzums (Fiksēts)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "Avansa maksājuma prece"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down Payment: %(date)s (Draft)"
msgstr "Pirmā iemaksa: %(date)s (projekts)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#, python-format
msgid "Down Payments"
msgstr "Avansa maksājumi"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment"
msgstr "Avansa maksājums"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Avansa maksājums (fiksēts daudzums)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Avansa maksājums (procentuāli)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Down payment <br/>"
msgstr "Pirmā iemaksa <br/>"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment invoice"
msgstr "Priekšapmaksas rēķins"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "Down payment of %s%%"
msgstr "Avansa maksājums par %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid ""
"Down payments are made when creating invoices from a sales order. They are "
"not copied when duplicating a sales order."
msgstr ""
"Priekšapmaksas rēķinu izveido, kad veido rēķinu no klienta pasūtījuma. Tie "
"nekopējas, kad dublē kienta pasūtījumu."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
#, python-format
msgid "Draft Invoices"
msgstr "Neapstiprināti Rēķini"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost savienotājs"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
#, python-format
msgid "Edit Configuration"
msgstr "Konfigurācijas rediģēšana"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr "Elektroniskais paraksts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr "E-pasts"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr "Klientam tiek nosūtīts e-pasta vēstule, tiklīdz rēķins ir pieejams."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Paredzamais Datums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr "Paredzamais:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr "Derīguma beigas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Expiration Date:"
msgstr "Derīguma termiņš:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Expires on %(date)s"
msgstr "Derīgums beidzas %(date)s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Paplašināti filtri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr "Papildus vērtības"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Extra line with %s"
msgstr "Papildus rinda ar %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx savienotājs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Nodokļu Profils"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid ""
"Fiscal positions are used to adapt taxes and accounts for particular "
"customers or sales orders/invoices.The default value comes from the "
"customer."
msgstr ""
"Fiskālās pozīcijas tiek izmantotas, lai pielāgotu nodokļus un kontus "
"konkrētiem klientiem vai pārdošanas pasūtījumiem/rēķiniem.Noklusējuma "
"vērtība tiek iegūta no klienta."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__amount
msgid "Fixed Amount"
msgstr "Fiksēta Summa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Follow, view or pay your orders"
msgstr "Sekojiet, skatiet vai apmaksājiet savus pasūtījumus"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (kontaktpersonas)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Fonts awesome ikona, piem. fa-tasks"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr "Aizliegtās vērtības neaprēķināmā pārdošanas rīkojuma rindā"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid ""
"From this report, you can have an overview of the amount invoiced to your "
"customer. The search tool can also be used to personalise your Invoices "
"reports and so, match this analysis to your needs."
msgstr ""
"Šī atskaite attēlo klientam piestādīto rēķinu summu. Ja vēlas personalizēt "
"atskaiti, var izmantot meklētāja filtrus."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Full amount <br/>"
msgstr "Pilna summa <br/>"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "Pilnībā izrakstīts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Nākotnes aktivitātes"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Izveidot pārdošanas maksājumu saiti"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr "Maksājuma saites izveide"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr ""
"Automātiska rēķina ģenerēšana, kad ir apstiprināts tiešsaistes maksājums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr "Saņemt brīdinājumus par produktiem vai klientiem"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__so_discount
msgid "Global Discount"
msgstr "Vispārēja atlaide"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Good job, let's continue."
msgstr "Labs darbs, turpināsim."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr "Piešķirt atlaides pārdošanas pasūtījumu līnijām"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Bruto Svars"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Grupēt pēc"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr "Aktīva cenu lapa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr "Apstiprināts pasūtījums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr "Vai ir mainījusies fiskālā pozīcija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr "Ir ziņojums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr "Vai cenrādis ir mainījies"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr "Ir avansa maksājumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona izņēmuma aktivitātes identificēšanai."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, dažiem ziņojumiem ir piegādes kļūda."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__journal_id
msgid ""
"If set, the SO will invoice in this journal; otherwise the sales journal "
"with the lowest sequence is used."
msgstr ""
"Ja iestatīts, SO izrakstīs rēķinu šajā žurnālā; pretējā gadījumā tiek "
"izmantots pārdošanas žurnāls ar zemāko kārtas numuru."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"If the sale is locked, you can not modify it anymore. However, you will "
"still be able to invoice or deliver."
msgstr ""
"Ja pārdošana ir slēgta, to nevar izmainīt. Bet var izrakstīt rēķinu un "
"piegādāt."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr "Ja ir patiess, iepakojumu var izmantot pārdošanas pasūtījumiem"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr "Ja mainīsiet cenrādi, tas ietekmēs tikai no jauna pievienotās rindas."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr "Importēt Amazon pasūtījumus un sinhronizēt piegādes"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "Import Template for Products"
msgstr "Importēt veidni produktiem"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_view_search_catalog
msgid "In the Order"
msgstr "Pasūtījumā"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr "Iesk. nodokļus)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "Peļņas konts"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
#, python-format
msgid "Invalid discount amount"
msgstr "Nederīga atlaides summa"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid order."
msgstr "Nepareizs pasūtījums"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Invalid signature data."
msgstr "Nepareizi paraksta dati"

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
#, python-format
msgid "Invoice %s paid"
msgstr "Rēķins %s apmaksāts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Rēķina Adrese"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr "Brīdinājums par rēķinu"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Rēķins apstiprināts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr "Rēķina skaits"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Rēķins izveidots"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr "Rēķina epasta veidne"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Rēķina Rindas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Izrakstīt rēķinu par klienta pasūtījumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Invoice Status"
msgstr "Rēķina Statuss"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""
"Izrakstīt rēķinu pēc piegādes, balstoties uz piegādāto, nevis pasūtīto "
"apjomu."

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid ""
"Invoice revenue for the current month. This is the amount the sales channel "
"has invoiced this month. It is used to compute the progression ratio of the "
"current and target revenue on the kanban view."
msgstr ""
"Rēķinu apgrozījums par tekošo mēnesi. Šis ir pārdošanas kanāla apjoms šajā "
"mēnesī. Tas tiek izmantots, lai aprēķinātu progresa koeficientu tekošam un "
"plānotajam apgrozījumam kanban skatā."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr "Rēķins par piegādāto"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr "Rēķins par pasūtīto"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Rēķins izrakstīts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Izrakstītais daudzums"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Invoiced Quantity: %s"
msgstr "Izrakstītais daudzums\\: %s"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Izrakstīts šajā mēnesī"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Rēķini"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Rēķinu Analīze"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Rēķinu Statistika"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing"
msgstr "Rēķini"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address"
msgstr "Rēķina adrese"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing Address:"
msgstr "Saņēmēja adrese"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__journal_id
msgid "Invoicing Journal"
msgstr "Rēķinu izrakstīšanas žurnāls"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Rēķinu politika"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr "Rēķina mērķis"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address"
msgstr "Rēķina izrakstīšanas un piegādes adrese"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Invoicing and Shipping Address:"
msgstr "Rēķina un piegādes adrese"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr "Ir avansa maksājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr "Ir redaktors"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr "Iztecējis termiņš"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr "Ir avansa maksājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr "Ir izmaksa"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid ""
"Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""
"Ir patiess, ja pārdošanas rīkojuma rinda nāk no izmaksas vai piegādātāja "
"rēķiniem"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""
"Aizliegts mainīt šādus laukus bloķētā secībā:\n"
"%s"

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr "Grāmatojumi"

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr "Kontējums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr "Valoda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Last Invoices"
msgstr "Pēdējie rēķini"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Pēdējoreiz atjaunināja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Pēdējoreiz atjaunināts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Pēdējās aktivitātes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr "Piegādes laiks"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr "Atļaut klientu pieeju, lai redzētu viņu dokumentus"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Let's send the quote."
msgstr "Nosūtīt piedāvājumu"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Lets keep electronic signature for now."
msgstr "Pagaidām atļaut elektronisko parakstu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Lock"
msgstr "Slēgts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr "Slēgt apsitprinātus piedāvājumus"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__locked
msgid "Locked"
msgstr "Slēgts"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__locked
msgid "Locked orders cannot be modified."
msgstr "Bloķētos rīkojumus nevar mainīt."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Looks good. Let's continue."
msgstr "Izskatās labi. Tā turpināt!"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr "Ziņojuma veidne"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Make your quote attractive by adding header pages, product descriptions and "
"footer pages to your quote."
msgstr ""
"Padariet savu piedāvājumu pievilcīgu, pievienojot tam galvenes lapas, "
"produktu aprakstus un pēdējās lapas."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""
"Akciju, kuponu, lojalitātes karšu, dāvanu karšu un e-portfeļa pārvaldība"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Manuālā"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr "Manuāls maksājums"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Ievadīt skaitu pēc pasūtījuma"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Peļņas"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr "Atzīmēt piedāvājumu kā nosūtītu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr "Mārketings"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_account_payment__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr "Vidējs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr "Ziņojums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Ziņa klienta pasūtījumam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Ziņa klienta pasūtījuma rindai"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr "Piegādātā daudzuma atjaunināšanas metode"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr "Trūkst obligāto lauku uzskaites pārdošanas rīkojuma rindā."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Mitchell Admin"
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Cien.\n"
"       <t t-if=\"object.partner_id.parent_id\">\n"
"           <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> (<t t-out=\"object.partner_id.parent_id.name or ''\">Azure Interior</t>),\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"           <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,\n"
"       </t>\n"
"       <br><br>\n"
"        Šeit ir jūsu\n"
"       <t t-if=\"object.name\">\n"
"            kredīta atzīme <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">RINV/2021/05/0001</span>\n"
"       </t>\n"
"       <t t-else=\"\">\n"
"            kredītrēķins\n"
"       </t>\n"
"       <t t-if=\"object.invoice_origin\">\n"
"            (ar atsauci: <t t-out=\"object.invoice_origin or ''\">SUB003</t>)\n"
"       </t>\n"
"       <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 143 750,00</span>apmērā\n"
"        no <t t-out=\"object.company_id.name or ''\">Jūsu uzņēmuma</t>.\n"
"       <br><br>\n"
"        Ja jums ir kādi jautājumi, sazinieties ar mums.\n"
"        <t t-if=\"not is_html_empty(object.invoice_user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.invoice_user_id.signature or ''\">--<br>Mitchell admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"            "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Manas aktivitātes izpildes termiņš"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Mani pasūtījumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr "Mani piedavājumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Manas klientu pasūtījumu rindas"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "New"
msgstr "Jauns"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Jauns piedāvājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nākamās darbības kalendāra pasākums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nākamās darbības beigu termiņš"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Nākamās darbības kopsavilkums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Nākamās darbības veids"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Numurs"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Nav ziņu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "No further requirements for this payment"
msgstr "Šim maksājumam nav papildu prasību"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr "Pēc apstiprināšanas vairs nav pasūtījumu rediģēšanas iespēju"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr "Nav pasūtījumu, kas gaida rēķina izveidi"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr "Nav atrasti pasūtījumi, lai palielinātu pārdošanas apjomu."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Piezīme"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Nav ko izrakstīt"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Now, we'll create a sample quote."
msgstr "Tagad izveidosim piedāvājuma paraugu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr "Numurs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"
msgstr ""
"Number of days between the order confirmation and the shipping of the "
"products to the customer"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "To ziņojumu skaits, kuros nepieciešama rīcība"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu, kas satur piegādes kļūdu, skaits"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr "Pasūtījumu skaits, kas jāizraksta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr "Pārdošanas apjoms, kas jāizraksta"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_discount__discount_type__sol_discount
msgid "On All Order Lines"
msgstr "Visās pasūtījuma līnijās"

#. module: sale
#: model:ir.model,name:sale.model_onboarding_onboarding
msgid "Onboarding"
msgstr "Uzņemšana"

#. module: sale
#: model:onboarding.onboarding.step,step_image_alt:sale.onboarding_onboarding_step_sale_order_confirmation
msgid "Onboarding Order Confirmation"
msgstr "Uzņemšanas pasūtījuma apstiprinājums"

#. module: sale
#: model:onboarding.onboarding.step,step_image_alt:sale.onboarding_onboarding_step_sample_quotation
msgid "Onboarding Sample Quotation"
msgstr "Uzņemšanas piedāvājuma paraugs"

#. module: sale
#: model:ir.model,name:sale.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Uzņemšanas posms"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"Once a sales order is confirmed, you can't remove one of its lines (we need to track if something gets invoiced or delivered).\n"
"                Set the quantity to 0 instead."
msgstr ""
"Kad pārdošanas pasūtījums ir apstiprināts, jūs nevarat noņemt kādu no tā rindiņām (mums ir nepieciešams izsekot, vai par kādu preci tiek izrakstīts rēķins vai tā tiek piegādāta).\n"
"                Tā vietā iestatiet daudzumu uz 0."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid ""
"Once the quotation is confirmed by the customer, it becomes a sales "
"order.<br> You will be able to create an invoice and collect the payment."
msgstr ""
"Kad klients ir apstiprinājis cenu piedāvājumu, tas kļūst par pārdošanas "
"pasūtījumu.<br> Jūs varēsiet izveidot rēķinu un iekasēt maksājumu."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid ""
"Once the quotation is confirmed, it becomes a sales order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Kad piedāvājums ir apstiprināts, tas kļūst par pārdošanas pasūtījumu.<br> "
"Jūs varēsiet izveidot rēķinu un iekasēt maksājumu."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
msgid "Online Payment"
msgstr "Maksājums tiešsaistē."

#. module: sale
#: model:ir.ui.menu,name:sale.payment_menu
msgid "Online Payments"
msgstr "Tiešsaistes maksājumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
msgid "Online Signature"
msgstr "Parakstīšanās tiešsaistē"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online payment"
msgstr "Tiešsaistes maksājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online signature"
msgstr "Tiešsaistes paraksts"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr "Tiek ņemti vērā tikai apstiprināti avansa maksājumi."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Only draft orders can be marked as sent directly."
msgstr "Tikai iesāktie pasūtījumi var tikt atzīmēti kā nosūtīti pa tiešo"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid ""
"Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""
"Vienā pārdošanas pasūtījuma rindā ir atļauta tikai viena Pielāgotā vērtība "
"katram atribūtam."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr ""
"Atveriet Pārdošanas lietotni, lai ar dažiem klikšķiem nosūtītu savu pirmo "
"piedāvājumu."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operācija netiek atbalstīta"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Izvēles tulkošanas valoda (ISO kods), ko izvēlēties, sūtot e-pastu. Ja tas "
"nav iestatīts, tiks izmantota versija angļu valodā. Parasti tai ir jābūt "
"viettura izteiksmei, kas nodrošina atbilstošo valodu, piem. {{ "
"object.partner_id.lang }}."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Pasūtījums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Pasūtījums #"

#. module: sale
#: model:onboarding.onboarding.step,title:sale.onboarding_onboarding_step_sale_order_confirmation
msgid "Order Confirmation"
msgstr "Pasūtījuma apstiprinājums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr "Pasūtījuma skaits"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#, python-format
msgid "Order Date"
msgstr "Pasūtījuma Datums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Order Date:"
msgstr "Pasūtījuma datums:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr "Pasūtījuma datums: pēdējās 365 dienas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Pasūtījuma rindas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Atsauce uz Pasūtījumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Pasūtījuma statuss"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr "Pasūtīt piepārdošanu"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Order signed by %s"
msgstr "Pasūtījumu parakstīja %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr "Pasūtījums izrakstīšanai"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr "Pasūtītais daudzums: %(old_qty)s -> %(new_qty)s"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""
"Pasūtītais daudzums: Rēķinā norādītais daudzums, ko pasūtījis klients.\n"
"Piegādātais daudzums: Rēķinā norādītais klientam piegādātais daudzums."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Pasūtītie daudzumi"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Pasūtījumi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Pasūtījumi izrakstīšanai"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Pasūtījumi piepārdošanai"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Oscar Morgan"
msgstr "Oskars Morgans"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr "Cita informācija"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "PDF Quote"
msgstr "PDF citāts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_pdf_quote_builder
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "PDF Quote builder"
msgstr "PDF citātu veidotājs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr "PDT identitātes žetons"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr "Priekšapmaksas rēķins"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr "Iepakojums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr "Iepakojumu skaits"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr "Partnera kredīta brīdinājums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "Apmaksāt tagad"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr "Apmaksāt ar"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr "Maksāt ar citu maksājuma veidu"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Maksāšanas instrukcija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Apmaksas metode"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_method_menu
msgid "Payment Methods"
msgstr "Maksājumu metodes"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr "Maksājumu sniedzējs"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_provider_menu
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr "Maksājumu sniedzēji"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr "Maksājuma ref."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Maksājuma nosacījumi"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_token_menu
msgid "Payment Tokens"
msgstr "Maksājumu žetoni"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Maksājuma darījums"

#. module: sale
#: model:ir.ui.menu,name:sale.payment_transaction_menu
msgid "Payment Transactions"
msgstr "Maksājumu darījumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr "Maksājuma nosacījumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__discount_percentage
msgid "Percentage"
msgstr "Procentuālā attiecība"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr "Portāla pieejas URL"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Prepayment amount"
msgstr "Priekšapmaksas summa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_res_config_settings__prepayment_percent
#: model:ir.model.fields,field_description:sale.field_sale_order__prepayment_percent
msgid "Prepayment percentage"
msgstr "Priekšapmaksas procentuālā daļa"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Prepayment percentage must be a valid percentage."
msgstr ""
"Priekšapmaksas procentuālajai daļai jābūt derīgai procentuālajai daļai."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "Priekšskatīt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Cenas samazinājums bez PVN"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr "Cenas samazinājums iesk. nodokļus"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Cenu lapa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr "Cenu lapas pozīcija"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cenu lapas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricing"
msgstr "Cenas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr "Pro-Forma rēķins"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr "Priekšapmaksas rēķins #"

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Priekšapmaksas rēķins"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Produkts"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "Produkta atribūts"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr "Produkta atribūta pielāgota vērtība"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr "Produktu katalogs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr "Produktu katalogs Produkts ir pārdošanas kārtībā"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_categories
msgid "Product Categories"
msgstr "Produktu kategorijas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Produkta Kategorija"

#. module: sale
#: model:ir.model,name:sale.model_product_document
msgid "Product Document"
msgstr "Produkta dokuments"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr "Produkta iepakojums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
msgid "Product Template"
msgstr "Produkta Veidne"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
msgid "Product Type"
msgstr "Produkta Tips"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr "Produkts Uom Tikai nolasāms"

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Variant"
msgstr "Produkta variants"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Produktu Varianti"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product prices have been recomputed according to pricelist %s."
msgstr "Produktu cenas ir pārrēķinātas saskaņā ar cenrādi %s."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product prices have been recomputed."
msgstr "Produktu cenas ir pārrēķinātas."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr "Produktu nodokļi ir pārrēķināti saskaņā ar fiskālo pozīciju %s."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr "Prece izmantota avansa maksājumiem"

#. module: sale
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.menu_reporting_product
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Products"
msgstr "Produkti"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Piegādātāis daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Izrakstītais daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr "Pasūtītais daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr "Daudzums piegādei"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Daudzums izrakstīšanai"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Daudzums izrakstīšanai no klienta pasūtījumiem"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "Daudzums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr "Daudzums izrakstīšanai"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr "Daudzums:"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_document__attached_on__quotation
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#, python-format
msgid "Quotation"
msgstr "Piedāvājums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Piedāvājums #"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr "Piedāvājuma skaits "

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr "Piedāvājuma datums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation Date:"
msgstr "Piedāvājuma datums:"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Piedāvājums nosūtīts"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
#, python-format
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr ""
"Piedāvājuma derīgums ir obligāts, un tam jābūt lielākam vai vienādam ar 0."

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Piedāvājums apstiprināts"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Cenu piedāvājums nosūtīts"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Quotation viewed by customer %s"
msgstr "Klients apskatījis piedāvājumu %s"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "Piedāvājumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr "Piedāvājumi un pasūtījumi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Piedāvājumu analīze"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Piedāvājumi un realizācija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
msgid "Quotations to review"
msgstr "Piedāvājumi, kas jāpārskata"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr "Reitingi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr "Papildus izrakstīšanas izmaksas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr "Redzama atkārtotu rēķinu izrakstīšanas politika"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr "Saņēmēji"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr "Pārrēķināt visas cenas, pamatojoties uz šo cenrādi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr "Pārrēķināt visus nodokļus, pamatojoties uz šo fiskālo stāvokli"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Reference"
msgstr "Atsauce"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr ""
"Atsauce uz dokumentu, kas radīja šo pārdošanas pasūtījuma pieprasījumu"

#. module: sale
#: model:ir.model,name:sale.model_account_payment_register
msgid "Register Payment"
msgstr "Reģistrēt maksājumu"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr "Standarta rēķins"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr "Atsaukt šo piedāvājumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_reference
msgid "Related Order"
msgstr "Saistīts pasūtījums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr "Renderēšanas modelis"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Atskaites"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_payment
msgid "Request a online payment from the customer to confirm the order."
msgstr ""
"Pieprasiet klientam veikt tiešsaistes maksājumu, lai apstiprinātu "
"pasūtījumu."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature from the customer to confirm the order."
msgstr ""
"Pieprasiet no klienta parakstu tiešsaistē, lai apstiprinātu pasūtījumu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Request an online prepayment from your customers to confirm orders. You can "
"ask them to fully pay the order or partially with a downpayment"
msgstr ""
"Pieprasiet klientiem veikt priekšapmaksu tiešsaistē, lai apstiprinātu "
"pasūtījumus. Jūs varat lūgt viņiem pilnībā vai daļēji apmaksāt pasūtījumu, "
"veicot priekšapmaksu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr "Pieprasīt tiešsaistes parakstu, lai apstiprinātu pasūtījumus"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Requested date is too soon."
msgstr "Pieprasītais datums ir pārāk agrs."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Atbildīgie lietotāji"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid ""
"Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr ""
"Tekošā mēneša plānotie ieņēmumi (apstoprinātie pasūtījumi kopā bez "
"nodokļiem)."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr "Ieņēmumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr "Ieņēmumi no kampaņas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS piegādes kļūda"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "SO0000"
msgstr "SO0000"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Sale Information"
msgstr "Pārdošanas informācija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields,field_description:sale.field_sale_order_discount__sale_order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "Pārdošanas pasūtījums"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_payment__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr "Pārdošanas pasūtījuma skaits"

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_plan_action_sale_order
msgid "Sale Order Plans"
msgstr "Pārdošanas pasūtījuma plāni"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr "Pārdošanas pasūtījuma brīdinājums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr "Pārdošanas pasūtījumi "

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr "Pārdošanas pasūtījumu skaits"

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr "Pārdošana Maksājumu pakalpojumu sniedzēja iekāpšanas vednis"

#. module: sale
#: model:onboarding.onboarding,name:sale.onboarding_onboarding_sale_quotation
msgid "Sale Quotation Onboarding"
msgstr "Pārdošanas piedāvājuma izcenojumu izvietošana uz klāja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr "Pārdošanas brīdinājumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr "Pārdošanas izvēlētā maksājuma metode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr "Pārdošanas rīkojumu atcelšana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.menu_reporting_sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.product_document_form
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Tirdzniecība"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Priekšapmaksas Rēķins"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#, python-format
msgid "Sales Analysis"
msgstr "Pārdošanas analīze"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_salesperson
msgid "Sales Analysis By Salespersons"
msgstr "Pārdošanas analīze, ko veic pārdevēji"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_customers
msgid "Sales Analysis Per Customers"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_products
msgid "Sales Analysis Products"
msgstr "Pārdošanas analīzes produkti"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "Pārdošanas analīzes atskaite"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__order_reference__sale_order
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.product_document_search
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#, python-format
msgid "Sales Order"
msgstr "Pasūtījums"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr "Pārdošanas pasūtījums atcelts"

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Pārdošanas pasūtījums apstiprināts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr "Pārdošanas pasūtījuma pozīcija"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Pārdošanas pasūtījuma rinda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Pārdošanas pasūtījuma rindas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Pārdošanas pasūtījuma rindas gatavas rēķina izrakstīšanai"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Pārdošanas pasūtījuma rindas attiecas uz manu pasūtījumu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
#, python-format
msgid "Sales Order(s)"
msgstr "Pārdošanas pasūtījums(i)"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Pārdošanas pasūtījumi"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_account_payment__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Pārdevēju komanda"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Pārdevēju komandas"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr "Pārdošanas brīdinājumi"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid ""
"Sales order item to which the time spent will be added in order to be "
"invoiced to your customer. Remove the sales order item for the timesheet "
"entry to be non-billable."
msgstr ""
"Pārdošanas pasūtījuma vienība, kurai tiks pievienots pavadītais laiks, lai "
"par to klientam varētu izrakstīt rēķinu. Noņemiet pārdošanas pasūtījuma "
"pozīciju, lai par laika uzskaites ierakstu nevarētu izrakstīt rēķinu."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr "Pārdošanas cena"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr "Pārdošana: Pasūtījums atcelts"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr "Pārdošana: Pasūtījums apstiprināts"

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_payment_executed
msgid "Sales: Payment Done"
msgstr "Pārdošana: Maksājums veikts"

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr "Pārdošana: Nosūtīt piedāvājumu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid "Sales: Untaxed Total"
msgstr "Tirdzniecība: Summa bez nodokļa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Pārdevējs"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_reporting_salespeople
msgid "Salespersons"
msgstr "Pārdevēji"

#. module: sale
#. odoo-python
#: code:addons/sale/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Sample Order Line"
msgstr "Paraugs pasūtījuma rindai"

#. module: sale
#. odoo-python
#: code:addons/sale/models/onboarding_onboarding_step.py:0
#, python-format
msgid "Sample Product"
msgstr "Produkta paraugs"

#. module: sale
#: model:onboarding.onboarding.step,title:sale.onboarding_onboarding_step_sample_quotation
msgid "Sample Quotation"
msgstr "Piedāvājuma paraugs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Meklēt pasūtījumu"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Sadaļa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr "Sadaļas nosaukums (piem., Produkti, Pakalpojumi)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Drošības žetons"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Select a product, or create a new one on the fly."
msgstr "Izvēlieties vai izvedojiet jaunu produktu uzreiz."

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Izvēloties \"Brīdinājumu\" lietotājam tiks ziņots, izvēloties \"Bloķēšanas "
"ziņu\" ar šo ziņu lietotājs var apstādināt plūsmu. Ziņa jāieraksta nākamā "
"laukā."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "Pārdod un iepērc produktus dažādās mērvienībās"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr "Pādod produktus dažādās mērvienībās # iepakojumā"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr "Pārdod produktu variantus izmantojot atribūtus (izmērs, krāsa, utml.)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr "Nosūtīt priekšapmaksas rēķinu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr "Kad rēķins ir apstirināts, nosūtīt produktam atbilstošu epastu."

#. module: sale
#: model:onboarding.onboarding.step,description:sale.onboarding_onboarding_step_sample_quotation
msgid "Send a quotation to test the customer portal."
msgstr "Nosūtīt piedāvājumu klienta portāla testam"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr "Sūtīt un atcelt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Sūtīt ar e-pastu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Send message"
msgstr "Sūtīt ziņojumu"

#. module: sale
#: model:onboarding.onboarding.step,button_text:sale.onboarding_onboarding_step_sample_quotation
msgid "Send sample"
msgstr "Sūtīt paraugu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud savienotājs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"Sending an email is useful if you need to share specific information or "
"content about a product (instructions, rules, links, media, etc.). Create "
"and set the email template from the product detail form (in Accounting tab)."
msgstr ""
"E-pasta sūtīšana ir noderīga, ja nepieciešams kopīgot konkrētu informāciju "
"vai saturu par produktu (instrukcijas, noteikumus, saites, multivides u. "
"c.). Izveidojiet un iestatiet e-pasta veidni produkta detalizācijas veidlapā"
" (cilnē Grāmatvedība)."

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr "Automātiski nosūtīt klientam, kad atcelts vai pasūtīts"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr "Sūtīt klientiem piedāvājuma apstiprinājumu"

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_payment_executed
msgid ""
"Sent to customers when a payment is received but doesn't immediately confirm"
" their order"
msgstr ""
"Nosūtīts klientiem, kad ir saņemts maksājums, bet pasūtījums netiek "
"apstiprināts uzreiz"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr "Set multiple prices per product, automated discounts, etc."

#. module: sale
#: model:onboarding.onboarding.step,button_text:sale.onboarding_onboarding_step_sale_order_confirmation
msgid "Set payments"
msgstr "Noteikt maksājumus"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Pārveidot uz piedāvājumu"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Uzstādījumi"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Dalīties"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Shipping"
msgstr "Piegāde"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Shipping Address"
msgstr "Piegādes adrese"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket savienotājs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Rādīt visus ierakstus, kuriem  nākamais darbības datums ir pirms šodienas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr "Rādīt peļņu pasūtījumos"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Sign & Pay Quotation"
msgstr "Parakstīt un maksāt piedāvājumu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr "Parakstīt &amp; Maksāt"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr "Parakstīt tiešsaistē"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Paraksts"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Signature is missing."
msgstr "Iztrūkst paraksts"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr "Parakstījis"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr "Parakstīts uz"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr "Pārdots"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr "Pārdots pēdējās 365 dienās"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed sale orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""
"Ir atlasīti daži apstiprināti pārdošanas pasūtījumi. Ar tiem saistītie dokumenti var būt šādi\n"
"                        atcelšana var ietekmēt."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_account_payment__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr "Avots"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Avota dokuments"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr "Konkrēts epasts"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "Stage"
msgstr "Stadija"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Start by checking your company's data."
msgstr "Iesāciet ar Jūsu uzņēmuma datu pārbaudi."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Statuss"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statuss, kas balstās uz aktivitātēm\n"
"Nokavēts: izpildes termiņš jau ir pagājis\n"
"Šodien: aktivitātes izpildes datums ir šodien\n"
"Plānots: nākotnes aktivitātes."

#. module: sale
#: model:onboarding.onboarding.step,done_text:sale.onboarding_onboarding_step_sale_order_confirmation
#: model:onboarding.onboarding.step,done_text:sale.onboarding_onboarding_step_sample_quotation
msgid "Step Completed!"
msgstr "Solis Pabeigts!"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr "Svītra"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr "Priekšmets"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Starpsumma"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr "Gala summa"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr "Summa bez nodokļa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr "Birkas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax 15%"
msgstr "15%"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr "Nodokļu aprēķina noapaļojuma metode"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_country_id
msgid "Tax Country"
msgstr "Nodokļa valsts"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Tax ID"
msgstr "PVN ID"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr "Nodokļi kopā"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr "Nodokļi kopā"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr "Nodokļu aprēķina noapaļošanas metode"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax excl."
msgstr "Bez PVN"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax incl."
msgstr "Ar PVN."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Nodokļi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Depozīta nodokļi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#, python-format
msgid ""
"Team %(team_name)s has %(sale_order_count)s active sale orders. Consider "
"canceling them or archiving the team instead."
msgstr ""
"Komandai %(team_name)s ir %(sale_order_count)s aktīvu pārdošanas pasūtījumu."
" Apsveriet iespēju tos atcelt vai arhivēt komandu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"Tell us why you are refusing this quotation, this will help us improve our "
"services."
msgstr ""
"Pastāstiet mums, kāpēc atsakāties no šī piedāvājuma, jo tas palīdzēs mums "
"uzlabot mūsu pakalpojumus."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr "Nosacījumu formāts"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Terms & Conditions: %s"
msgstr "Nosacījumi: %s"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Nosacījumi &amp; noteikumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions:"
msgstr "Nosacījumi &amp; noteikumi:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Nosacījumi un noteikumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr "Noteikumi un nosacījumi..."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO valstu kods sastāv no diviem burtiem. \n"
"Izmanto meklēšanas lauku."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %(order)s linked to the Analytic Account %(account)s is "
"cancelled. You cannot register an expense on a cancelled Sales Order."
msgstr ""
"Ar analītisko kontu %(account)s saistītais pārdošanas rīkojums %(order)s "
"tiek atcelts. Anulētam pārdošanas rīkojumam nevar reģistrēt izdevumus."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %(order)s linked to the Analytic Account %(account)s is "
"currently locked. You cannot register an expense on a locked Sales Order. "
"Please create a new SO linked to this Analytic Account."
msgstr ""
"Pārdošanas rīkojums %(order)s, kas saistīts ar analītisko kontu %(account)s,"
" pašlaik ir bloķēts. Nevar reģistrēt izdevumus bloķētam pārdošanas "
"rīkojumam. Lūdzu, izveidojiet jaunu SO, kas saistīts ar šo analītisko kontu."

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
#, python-format
msgid ""
"The Sales Order %(order)s linked to the Analytic Account %(account)s must be"
" validated before registering expenses."
msgstr ""
"Pirms izdevumu reģistrēšanas jāapstiprina ar analītisko kontu %(account)s "
"saistītais pārdošanas rīkojums %(order)s."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The access token is invalid."
msgstr "Pieejas žetons nav derīgs"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr ""
"Rēķinā norādāmā summa = Pārdošanas rīkojuma kopsumma - apstiprinātais "
"priekšapmaksa."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"The delivery date is sooner than the expected date. You may be unable to "
"honor the delivery date."
msgstr ""
"Piegādes datums ir agrāks par gaidīto. Iespējams, ka piegādes datumu "
"nevarēsiet ievērot."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr "Fiksētā summa, par kuru rēķins jāizsniedz avansā."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "The following orders are not in a state requiring confirmation: %s"
msgstr "Turpmāk norādītie rīkojumi nav apstiprināšanas stadijā: %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""
"Šādus produktus nevar ierobežot attiecībā uz uzņēmumu %s, jo tie jau ir izmantoti citos citos piedāvājumos vai pārdošanas pasūtījumos citā uzņēmumā:\n"
"%s\n"
"Varat arhivēt šos produktus un no jauna izveidot tos ar sava uzņēmuma ierobežojumu vai atstāt tos kā koplietošanas produktus."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""
"Rēķins tiek ģenerēts automātiski un ir pieejams klientu portālā, kad maksājumu pakalpojumu sniedzējs ir apstiprinājis darījumu.\n"
"Rēķins tiek atzīmēts kā apmaksāts, un maksājums tiek reģistrēts maksājumu pakalpojumu sniedzēja konfigurācijā noteiktajā maksājumu žurnālā.\n"
"Šo režīmu ieteicams izmantot, ja galīgo rēķinu izsniedzat pie pasūtījuma, nevis pēc piegādes."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"The margin is computed as the sum of product sales prices minus the cost set"
" in their detail form."
msgstr ""
"Uzcenojums tiek aprēķināts no produkta pārdošanas vērtības mīnus izmaksa."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr ""
"Jaunajā rēķinā tiks atskaitīti rēķinu projekti, kas saistīti ar šo "
"pārdošanas pasūtījumu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr ""
"Pasūtījums nav tādā stāvoklī, lai klients par to varētu veikt maksājumu."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The order is not in a state requiring customer signature."
msgstr "Pasūtījumā nav nepieciešams klienta paraksts."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "The ordered quantity has been updated."
msgstr "Pasūtītais daudzums ir atjaunināts."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr "Šī pārdošanas rīkojuma maksājuma paziņojums."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "The payment should also be transmitted with love"
msgstr "Maksājums jāveic rūpīgi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr "Procentuālā daļa no summas, par kuru jāizsniedz rēķins avansā."

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__prepayment_percent
msgid ""
"The percentage of the amount needed that must be paid by the customer to "
"confirm the order."
msgstr ""
"Nepieciešamās summas procentuālā daļa, kas klientam jāsamaksā, lai "
"apstiprinātu pasūtījumu."

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__prepayment_percent
#: model:ir.model.fields,help:sale.field_res_config_settings__prepayment_percent
msgid "The percentage of the amount needed to be paid to confirm quotations."
msgstr ""
"Procentuālā daļa no summas, kas jāsamaksā, lai apstiprinātu cenu "
"piedāvājumus."

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid "The product (%s) has incompatible values: %s"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should be of type 'Service'. "
"Please use another product or update this product."
msgstr ""
"Ja produkts tiek izmantots kā avansa maksājums, tam jābūt 'Pakalpojums'. "
"Lūdzu, izmanto citu produktu vai atjauno šo produktu."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid ""
"The product used to invoice a down payment should have an invoice policyset "
"to \"Ordered quantities\". Please update your deposit product to be able to "
"create a deposit invoice."
msgstr ""
"Produktam, kas tiek izmantots priekšapmaksas rēķina izrakstīšanai, ir jābūt "
"rēķina politikai, kas iestatīta uz \"Pasūtītie daudzumi\". Lūdzu, "
"atjauniniet savu avansa produktu, lai varētu izveidot avansa rēķinu."

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: code:addons/sale/controllers/portal.py:0
#, python-format
msgid "The provided parameters are invalid."
msgstr "Sniegtie parametri ir nederīgi."

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#, python-format
msgid "The value of the down payment amount must be positive."
msgstr "Avansa maksājuma daudzuma vērtībai jābūt pozitīvai."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr "There are currently no quotations for your account."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no sales orders for your account."
msgstr "Pašlaik jūsu kontā nav pārdošanas pasūtījumu."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr "Ir esošās"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_order_discount.py:0
#, python-format
msgid ""
"There does not seem to be any discount product configured for this company "
"yet. You can either use a per-line discount, or ask an administrator to "
"grant the discount the first time."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"This default value is applied to any new product created. This can be "
"changed in the product detail form."
msgstr ""
"Kad tiek izveidots jauns produkts, tam tiek piemēroti noklusējuma parametri."
" Tos var izmainīt produkta kartiņā."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_account_payment__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Šis nosaukus palīdz jums sekot līdzi dažādiem kampaņas pasākumiem, piemēram,"
" Fall_Drive, Christmas_Special"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid ""
"This is the delivery date promised to the customer. If set, the delivery "
"order will be scheduled based on this date rather than product lead times."
msgstr ""
"Tas ir klientam solītais piegādes datums. Ja tas ir iestatīts, piegādes "
"pasūtījums tiks ieplānots, pamatojoties uz šo datumu, nevis uz produkta "
"piegādes laiku."

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_account_payment__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Tas ir piegādes veids, piemēram, pastkarte, e-pasta vēstule vai baneru "
"reklāma"

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_account_payment__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Tas ir saites avots, piemēram, meklētājprogramma, cits domēns vai e-pasta "
"saraksta nosaukums"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/payment_link_wizard.py:0
#, python-format
msgid "This payment will confirm the quotation."
msgstr "Ar šo maksājumu tiks apstiprināts piedāvājums."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"This product is packaged by %(pack_size).2f %(pack_name)s. You should sell "
"%(quantity).2f %(unit)s."
msgstr ""
"Šo produktu iesaiņo %(pack_size).2f %(pack_name)s. Jums vajadzētu pārdot "
"%(quantity).2f %(unit)s."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
#: model_terms:ir.actions.act_window,help:sale.action_order_report_customers
#: model_terms:ir.actions.act_window,help:sale.action_order_report_products
#: model_terms:ir.actions.act_window,help:sale.action_order_report_salesperson
msgid ""
"This report performs analysis on your quotations and sales orders. Analysis "
"check your sales revenues and sort it by different group criteria (salesman,"
" partner, product, etc.) Use this report to perform analysis on sales not "
"having invoiced yet. If you want to analyse your turnover, you should use "
"the Invoice Analysis report in the Accounting application."
msgstr ""
"Šajā pārskatā tiek veikta jūsu piedāvājumu un pārdošanas pasūtījumu analīze."
" Analizējiet pārdošanas ieņēmumus un sakārtojiet tos pēc dažādiem grupas "
"kritērijiem (pārdevējs, partneris, produkts utt.) Izmantojiet šo pārskatu, "
"lai veiktu analīzi par pārdošanu, par kuru vēl nav izrakstīti rēķini. Ja "
"vēlaties analizēt savu apgrozījumu, lietotnē Grāmatvedība jāizmanto rēķinu "
"analīzes atskaite."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid ""
"This report performs analysis on your quotations. Analysis check your sales "
"revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Šajā pārskatā tiek veikta jūsu piedāvājumu analīze. Analizējiet pārdošanas "
"ieņēmumus un sakārtojiet tos pēc dažādiem grupas kritērijiem (pārdevējs, "
"partneris, produkts utt.) Izmantojiet šo pārskatu, lai veiktu analīzi par "
"pārdošanu, par kuru vēl nav izrakstīti rēķini. Ja vēlaties analizēt savu "
"apgrozījumu, lietotnē Grāmatvedība jāizmanto rēķinu analīzes atskaite."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid ""
"This report performs analysis on your sales orders. Analysis check your "
"sales revenues and sort it by different group criteria (salesman, partner, "
"product, etc.) Use this report to perform analysis on sales not having "
"invoiced yet. If you want to analyse your turnover, you should use the "
"Invoice Analysis report in the Accounting application."
msgstr ""
"Šajā pārskatā tiek veikta jūsu pārdošanas pasūtījumu analīze. Analizējiet "
"pārdošanas ieņēmumus un sakārtojiet tos pēc dažādiem grupas kritērijiem "
"(pārdevējs, partneris, produkts utt.) Izmantojiet šo pārskatu, lai veiktu "
"analīzi par pārdošanu, par kuru vēl nav izrakstīti rēķini. Ja vēlaties "
"analizēt savu apgrozījumu, lietotnē Grāmatvedība jāizmanto rēķinu analīzes "
"atskaite."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update all taxes based on the currently selected fiscal position."
msgstr ""
"Tādējādi visi nodokļi tiks atjaunināti, pamatojoties uz pašlaik izvēlēto "
"fiskālo pozīciju."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid ""
"This will update the unit price of all products based on the new pricelist."
msgstr ""
"Tādējādi tiks atjaunināta visu produktu vienības cena, pamatojoties uz jauno"
" cenrādi."

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Piestādīt rēķinu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr "Piepārdošana"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"Lai nosūtītu ielūgumus B2B režīmā, atveriet kontaktu vai atlasiet vairākus "
"kontaktus saraksta skatā un nolaižamajā izvēlnē *Akcija* noklikšķiniet uz "
"opcijas \"Portāla piekļuves pārvaldība\"."

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid ""
"To speed up order confirmation, we can activate electronic signatures or "
"payments."
msgstr ""
"Lai paātrinātu pasūtījuma apstiprināšanu, mēs varam aktivizēt elektronisko "
"parakstu vai maksājumus."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Šodienas aktivitātes"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Kopā"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr "Nodokļi kopā"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr "Kopā bez PVN"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "Iekļauto nodokļu summa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Izsekošanas pakalpojums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr "Izsekošana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Grāmatojumi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr "Rakstīt vārdu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr "Rakstīt ziņu"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Reģistrētās izņēmuma aktivitātes veids."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr "Ierakstiet, lai atrastu klientu..."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr "Ierakstiet, lai atrastu produktu..."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS savienotājs"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS savienotājs"

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM kampaņa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Vienības cena"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr "Vienības cena:"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Mērvienība"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Mērvienības"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr "Mērvienību kategoriju vienības"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unlock"
msgstr "Atbloķēt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Summa bez nodokļa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr "Piestādītā summa bez nodokļa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr "Summa bez nodokļa, kas jāizraksta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr "Piestādītā summa bez nodokļa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Kopā bez nodokļiem"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr "Mērv"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr "Atjaunot cenas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr "Atjaunot nodokļus"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Upsell %(order)s for customer %(customer)s"
msgstr "Upsell %(order)s klientam %(customer)s"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Piepārdošanas iespēja"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""
"Izmanto pārdevēji, nosūtot potenciālajiem klientiem cenu piedāvājumus vai "
"proformas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Derīgs līdz"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr "Apstiprināt pasūtījumu"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid ""
"Validated expenses and vendor bills can be re-invoiced to a customer at its "
"cost or sales price."
msgstr ""
"Apstiprinātos izdevumus un pārdevēju rēķinus var pārrēķināt klientam pēc to "
"pašizmaksas vai pārdošanas cenas."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr "Izvēle varianta režģim"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "View Details"
msgstr "Skatīt vairāk"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "View Order"
msgstr "Skatīt pasūtījumu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "View Quotation"
msgstr "Skatīt piedāvājumu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_document__attached_on
msgid "Visible at"
msgstr "Redzams pie"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr "Darījuma anulēšana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Skaļums"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#, python-format
msgid "Warning"
msgstr "Brīdinājums"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid "Warning for %s"
msgstr "Brīdinājums par %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "Warning for the change of your quotation's company"
msgstr "Brīdinājums par jūsu piedāvājuma uzņēmuma maiņu"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Brīdinājums pārdošanas pasūtījumam"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Brīdinājums, kad pārdod šo produktu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Tīmekļa vietnes ziņojumi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr "Tīmekļa vietnes saziņas vēsture"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
#, python-format
msgid "Write a company name to create one, or see suggestions."
msgstr ""
"Uzrakstiet uzņēmuma nosaukumu, lai to izveidotu, vai skatiet ieteikumus."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "You can invoice them before they are delivered."
msgstr "Jūs varat izrakstīt rēķinu, pirms tie ir piegādāti."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"You can not delete a sent quotation or a confirmed sales order. You must "
"first cancel it."
msgstr ""
"Nosūtītu cenu piedāvājumu vai apstiprinātu pārdošanas pasūtījumu nevar "
"dzēst. Vispirms tas ir jāatceļ."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""
"Varat atlasīt visus pasūtījumus un izrakstīt tiem rēķinus partijās,<br>\n"
"            vai atzīmēt katru pasūtījumu un izrakstīt rēķinu pa vienam."

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid ""
"You can set here the communication type that will appear on sales orders.The"
" communication will be given to the customer when they choose the payment "
"method."
msgstr ""
"Šeit varat iestatīt saziņas veidu, kas tiks parādīts pārdošanas "
"pasūtījumos.Saziņa tiks sniegta klientam, kad viņš izvēlēsies maksājuma "
"veidu."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "You cannot cancel a locked order. Please unlock it first."
msgstr "Bloķētu pasūtījumu nevar atcelt. Lūdzu, vispirms to atbloķējiet."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "You cannot change the pricelist of a confirmed order !"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#, python-format
msgid ""
"You cannot change the product's type because it is already used in sales "
"orders."
msgstr ""
"Jūs nevarat mainīt produkta veidu, jo tas jau tiek izmantots pārdošanas "
"pasūtījumos."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#, python-format
msgid ""
"You cannot change the type of a sale order line. Instead you should delete "
"the current line and create a new line of the proper type."
msgstr ""
"Pārdošanas rīkojuma rindas veidu mainīt nevar. Tā vietā ir jāizdzēš "
"pašreizējā rinda un jāizveido jauna atbilstoša tipa rinda."

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid ""
"You cannot set a negative number for the default quotation validity. Leave "
"empty (or 0) to disable the automatic expiration of quotations."
msgstr ""
"Noklusējuma piedāvājuma derīguma vērtībai nevar iestatīt negatīvu skaitli. "
"Atstājiet tukšu (vai 0), lai atspējotu automātisko derīguma termiņu."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""
"Jums ir jādefinē prece visam ko Jūs pārdodat vai iegādājaties,\n"
"                neatkarīgi no tā, vai tā ir uzglabājama prece, patērējama prece vai pakalpojums."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "Your Orders"
msgstr "Jūsu pasūtījumi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Your Reference:"
msgstr "Jūsu atsauce:"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr "Jūsu atbilde"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr "Jūsu pasūtījums ir apstiprināts."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""
"Jūsu pasūtījums ir parakstīts, taču nepieciešama apmaksa, lai pasūtījums "
"tiktu apstiprināts."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr "Jūsu pasūtījums ir parakstīts."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr "Jūsu pasūtījums ir stadijā, kad to nevar atcelt."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""
"Jūsu piedāvājumā ir uzņēmuma %(product_company)s produkti, bet jūsu piedāvājums pieder uzņēmumam %(quote_company)s.\n"
" Lūdzu, mainiet sava piedāvājuma uzņēmumu vai izņemiet produktus no citiem uzņēmumiem (%(bad_products)s)."

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr "automātiksa rēķina izrakstīšana: nosūtīt gatavu rēķinu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost savienotājs"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "aizvērt"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "dienas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr "šim pārdošanas pasūtījumam."

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#, python-format
msgid "sale order"
msgstr "pārdošanas pasūtījums"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    quotations?"
msgstr ""
"atlasīts\n"
"                    piedāvājumi?"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "units"
msgstr "vienības"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
#: model:mail.template,subject:sale.mail_template_sale_payment_executed
msgid ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"
msgstr ""
"{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state "
"== 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a'"
" }})"

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and "
"(ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ "
"object.name or 'n/a' }})"

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
msgstr ""
"{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ "
"object.name or 'n/a' }})"
